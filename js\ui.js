// 创建UI日志记录器
const logger = window.Logger ? window.Logger.getLogger('UI') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

// 等待DOM加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    // 初始化日志系统
    if (window.Logger) {
        // 设置日志级别为WARN，减少日志输出
        window.Logger.setLevel(window.Logger.LogLevel.WARN);
        logger.info('日志系统已初始化');
    }

    // 初始化资源加载器
    if (window.ResourceLoader) {
        window.ResourceLoader.configure({
            basePath: '',
            imagePath: 'images/',
            lazyLoadThreshold: 200,
            maxConcurrentLoads: 6
        }).init();
        logger.info('ResourceLoader已初始化');
    }

    // 初始化BackgroundRunner
    if (window.BackgroundRunner) {
        window.BackgroundRunner.init({
            audioVolume: 0.0001,  // 设置极低的音量，几乎听不到
            debug: false,         // 关闭调试日志
            maxCallbackRate: 20,  // 提高回调频率，从默认100ms降低到20ms
            checkInterval: 5,     // 降低检查间隔到5ms
            keepAliveInterval: 3  // 降低保活间隔到3ms
        });
        logger.info('BackgroundRunner已初始化');
    }

    // 创建游戏实例
    window.game = new Game();
    window.game.init();  // 在这里调用init()方法，它会处理所有按钮的事件绑定

    // 添加一个默认玩家
    window.game.addPlayer('玩家1');

    // 初始化游戏设置菜单
    initGameSettings();

    // 初始化筹码管理功能
    initChipManagement();

    // 初始化测试模式
    initTestMode();

    // 初始化自动速度控制
    initSpeedControl();

    // 添加庄家发牌速度滑块处理
    const dealerSpeedSlider = document.getElementById('dealer-speed');
    const dealerSpeedValue = document.getElementById('dealer-speed-value');
    const decreaseDealerSpeed = document.getElementById('decrease-dealer-speed');
    const increaseDealerSpeed = document.getElementById('increase-dealer-speed');

    if (dealerSpeedSlider && dealerSpeedValue) {
        // 初始化显示
        dealerSpeedValue.textContent = dealerSpeedSlider.value;

        // 滑块变化时更新显示值并设置庄家发牌延迟
        dealerSpeedSlider.addEventListener('input', function() {
            const value = parseInt(this.value);
            dealerSpeedValue.textContent = value;
            window.game.setDealerCardDelay(value);
        });

        // 减号按钮减少延迟（减少数值）
        if (decreaseDealerSpeed) {
            decreaseDealerSpeed.addEventListener('click', function() {
                const value = parseInt(dealerSpeedSlider.value);
                const newValue = Math.max(500, value - 100);
                dealerSpeedSlider.value = newValue;
                dealerSpeedValue.textContent = newValue;
                window.game.setDealerCardDelay(newValue);
            });
        }

        // 加号按钮增加延迟（增加数值）
        if (increaseDealerSpeed) {
            increaseDealerSpeed.addEventListener('click', function() {
                const value = parseInt(dealerSpeedSlider.value);
                const newValue = Math.min(2000, value + 100);
                dealerSpeedSlider.value = newValue;
                dealerSpeedValue.textContent = newValue;
                window.game.setDealerCardDelay(newValue);
            });
        }
    }

    // 更新UI
    window.game.updateUI();
});

// 初始化筹码管理功能
function initChipManagement() {
    // 获取相关元素
    const playerSelect = document.getElementById('chip-player-select');
    const chipAmount = document.getElementById('chip-amount');
    const addChipsSingle = document.getElementById('add-chips-single');
    const addChipsAll = document.getElementById('add-chips-all');

    // 初始填充玩家选择下拉框
    updatePlayerSelectOptions();

    // 为单个玩家添加筹码
    addChipsSingle.addEventListener('click', () => {
        const selectedPlayerIndex = parseInt(playerSelect.value);
        const amount = parseInt(chipAmount.value);

        if (isNaN(selectedPlayerIndex) || isNaN(amount) || amount <= 0) {
            alert('请选择有效的玩家和金额');
            return;
        }

        if (window.game.addChips(selectedPlayerIndex, amount)) {
            window.game.updateUI();
            const playerName = window.game.players[selectedPlayerIndex].name;
            showToast(`已为 ${playerName} 添加 ${amount} 筹码`);
        } else {
            alert('添加筹码失败');
        }
    });

    // 为所有玩家添加筹码
    addChipsAll.addEventListener('click', () => {
        const amount = parseInt(chipAmount.value);

        if (isNaN(amount) || amount <= 0) {
            alert('请输入有效的筹码金额');
            return;
        }

        if (window.game.addChipsForAll(amount)) {
            window.game.updateUI();
            showToast(`已为所有玩家添加 ${amount} 筹码`);
        } else {
            alert('添加筹码失败');
        }
    });

    // 监听玩家数量变化事件，更新下拉框
    document.getElementById('add-player').addEventListener('click', updatePlayerSelectOptions);
    document.getElementById('remove-player').addEventListener('click', updatePlayerSelectOptions);
    document.getElementById('player-count').addEventListener('change', updatePlayerSelectOptions);
}

// 更新玩家选择下拉框选项
function updatePlayerSelectOptions() {
    const playerSelect = document.getElementById('chip-player-select');
    if (!playerSelect) return;

    // 保存当前选中的值
    const currentSelectedValue = playerSelect.value;

    // 清空现有选项
    playerSelect.innerHTML = '';

    // 添加新选项
    window.game.players.forEach((player, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${player.name} (${player.chips} 筹码)`;
        playerSelect.appendChild(option);
    });

    // 尝试恢复之前选中的值
    if (currentSelectedValue && playerSelect.querySelector(`option[value="${currentSelectedValue}"]`)) {
        playerSelect.value = currentSelectedValue;
    }
}

/**
 * 显示提示消息（高性能优化版）
 * 使用requestAnimationFrame和DOM缓存优化性能
 * @param {string} message - 要显示的消息
 * @param {number} duration - 消息显示时间（毫秒）
 */
function showToast(message, duration = 2000) {
    // 使用缓存的DOM元素
    if (!window._toastElement) {
        const toast = document.createElement('div');
        toast.id = 'toast-notification';

        // 使用CSS类而不是内联样式，提高性能
        toast.className = 'toast-notification';

        // 只设置必要的内联样式，其余使用CSS类
        Object.assign(toast.style, {
            opacity: '0',
            position: 'fixed',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: '9999'
        });

        document.body.appendChild(toast);
        window._toastElement = toast;

        // 记录创建日志
        logger.debug('Toast元素已创建');
    }

    // 取消任何正在进行的隐藏动画
    if (window._toastTimeoutId) {
        clearTimeout(window._toastTimeoutId);
        window._toastTimeoutId = null;
    }

    const toast = window._toastElement;

    // 设置消息内容
    if (toast.textContent !== message) {
        toast.textContent = message;
    }

    // 使用requestAnimationFrame优化渲染
    if (!window._toastAnimationFrameId) {
        window._toastAnimationFrameId = requestAnimationFrame(() => {
            window._toastAnimationFrameId = null;

            // 强制重排，确保样式应用
            void toast.offsetWidth;
            toast.style.opacity = '1';

            // 设置淡出动画
            window._toastTimeoutId = setTimeout(() => {
                requestAnimationFrame(() => {
                    toast.style.opacity = '0';
                });
            }, duration);
        });
    }
}

// 初始化游戏设置菜单
function initGameSettings() {
    // 获取设置菜单元素
    const gameSettingsBtn = document.getElementById('game-settings');
    const gameSettingsMenu = document.getElementById('game-settings-menu');
    const closeSettingsBtn = document.getElementById('close-settings');
    const saveSettingsBtn = document.getElementById('save-settings');
    const cancelSettingsBtn = document.getElementById('cancel-settings');

    // 自动下注相关元素
    const autoBettingSwitch = document.getElementById('auto-betting');
    const autoBetSettings = document.getElementById('auto-bet-settings');

    // 自动补充筹码相关元素
    const autoRefillSwitch = document.getElementById('auto-refill-chips');
    const autoRefillSettings = document.getElementById('auto-refill-settings');

    // 绑定设置菜单打开按钮事件
    if (gameSettingsBtn && gameSettingsMenu) {
        gameSettingsBtn.addEventListener('click', () => {
            // 显示设置菜单前更新当前设置值
            updateSettingsDisplay();
            gameSettingsMenu.style.display = 'block';
        });
    }

    // 绑定关闭按钮事件
    if (closeSettingsBtn) {
        closeSettingsBtn.addEventListener('click', () => {
            gameSettingsMenu.style.display = 'none';
        });
    }

    // 绑定取消按钮事件
    if (cancelSettingsBtn) {
        cancelSettingsBtn.addEventListener('click', () => {
            gameSettingsMenu.style.display = 'none';
        });
    }

    // 绑定保存按钮事件
    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', () => {
            saveSettings();
        });
    }

    // 自动下注开关事件
    if (autoBettingSwitch && autoBetSettings) {
        autoBettingSwitch.addEventListener('change', (e) => {
            autoBetSettings.style.display = e.target.checked ? 'block' : 'none';
        });
    }

    // 自动补充筹码开关事件
    if (autoRefillSwitch && autoRefillSettings) {
        autoRefillSwitch.addEventListener('change', (e) => {
            autoRefillSettings.style.display = e.target.checked ? 'block' : 'none';
        });
    }

    // 更新设置显示
    function updateSettingsDisplay() {
        // 更新渗透率滑块值
        const penetrationRateSlider = document.getElementById('penetration-rate-slider');
        const penetrationRateValue = document.getElementById('penetration-rate-value');
        if (window.game && window.game.deck && penetrationRateSlider && penetrationRateValue) {
            const currentRate = Math.round(window.game.deck.getPenetrationRate() * 100);
            penetrationRateSlider.value = currentRate;
            penetrationRateValue.textContent = `${currentRate}%`;
        }

        // 更新筹码设置值
        if (window.game) {
            document.getElementById('min-bet').value = window.game.minBet;
            document.getElementById('max-bet').value = window.game.maxBet;
            document.getElementById('default-bet').value = window.game.defaultBet;

            // 更新自动下注设置
            if (autoBettingSwitch) {
                autoBettingSwitch.checked = window.game.isAutoBetting;
                if (autoBetSettings) {
                    autoBetSettings.style.display = window.game.isAutoBetting ? 'block' : 'none';
                }
            }

            // 更新自动补充筹码设置
            if (autoRefillSwitch && window.game.autoRefillChips) {
                autoRefillSwitch.checked = window.game.autoRefillChips.enabled;

                if (autoRefillSettings) {
                    autoRefillSettings.style.display = window.game.autoRefillChips.enabled ? 'block' : 'none';

                    // 更新阈值和金额
                    const thresholdInput = document.getElementById('auto-refill-threshold');
                    const amountInput = document.getElementById('auto-refill-amount');

                    if (thresholdInput) {
                        thresholdInput.value = window.game.autoRefillChips.threshold;
                    }

                    if (amountInput) {
                        amountInput.value = window.game.autoRefillChips.amount;
                    }
                }
            }
        }
    }

    // 保存设置
    function saveSettings() {
        if (!window.game) {
            alert('游戏未初始化，无法保存设置');
            return;
        }

        // 获取渗透率设置
        const penetrationRateSlider = document.getElementById('penetration-rate-slider');
        if (penetrationRateSlider) {
            const penetrationRate = parseInt(penetrationRateSlider.value) / 100;
            window.game.deck.setPenetrationRate(penetrationRate);
        }

        // 获取筹码设置
        const minBet = parseInt(document.getElementById('min-bet').value);
        const maxBet = parseInt(document.getElementById('max-bet').value);
        const defaultBet = parseInt(document.getElementById('default-bet').value);

        // 验证筹码设置
        if (isNaN(minBet) || isNaN(maxBet) || isNaN(defaultBet) ||
            minBet <= 0 || maxBet <= 0 || defaultBet <= 0 ||
            minBet > maxBet || defaultBet < minBet || defaultBet > maxBet) {
            alert('筹码设置无效，请检查最小下注、最大下注和默认下注的值');
            return;
        }

        // 应用筹码设置
        window.game.setMinBet(minBet);
        window.game.setMaxBet(maxBet);
        window.game.setDefaultBet(defaultBet);

        // 获取自动下注设置
        const autoBetting = document.getElementById('auto-betting').checked;
        window.game.setAutoBetting(autoBetting);

        // 如果启用了自动下注，更新自动下注金额
        if (autoBetting) {
            const autoBetAmount = parseInt(document.getElementById('auto-bet-amount').value);
            if (!isNaN(autoBetAmount) && autoBetAmount >= minBet && autoBetAmount <= maxBet) {
                window.game.setAllPlayersAutoBetAmount(autoBetAmount);
            } else {
                window.game.setAllPlayersAutoBetAmount(defaultBet);
            }
        }

        // 获取自动补充筹码设置
        const autoRefillEnabled = document.getElementById('auto-refill-chips').checked;
        const refillThreshold = parseInt(document.getElementById('auto-refill-threshold').value);
        const refillAmount = parseInt(document.getElementById('auto-refill-amount').value);

        // 验证自动补充筹码设置
        if (autoRefillEnabled) {
            if (isNaN(refillThreshold) || isNaN(refillAmount) ||
                refillThreshold <= 0 || refillAmount <= 0) {
                alert('自动补充筹码设置无效，请检查阈值和金额');
                return;
            }
        }

        // 应用自动补充筹码设置
        window.game.setAutoRefillChips(autoRefillEnabled, refillThreshold, refillAmount);
        logger.info(`设置自动补充筹码: 启用=${autoRefillEnabled}, 阈值=${refillThreshold}, 金额=${refillAmount}`);

        // 获取其他游戏规则设置
        const dealerStandSoft17 = document.getElementById('dealer-stand-soft17').checked;
        window.game.setDealerStandSoft17(dealerStandSoft17);

        // 获取策略提示设置
        const strategyHint = document.getElementById('strategy-hint-switch').checked;
        window.game.setStrategyHint(strategyHint);

        // 获取重置统计设置
        const resetStats = document.getElementById('reset-stats').checked;
        window.game.setResetStatsOnNewGame(resetStats);

        // 获取蜗牛洗牌模式设置
        const snailMode = document.getElementById('snail-shuffle').checked;
        const snailModeChanged = window.game.deck.isSnailMode !== snailMode;
        window.game.deck.setSnailMode(snailMode);

        // 立即更新牌库统计显示，确保蜗牛洗牌模式变化能立即反映在UI上
        window.game.updateDeckInfo();

        // 如果蜗牛洗牌模式发生了变化，更新整个UI
        if (snailModeChanged) {
            window.game.updateUI();
        }

        // 关闭设置菜单
        gameSettingsMenu.style.display = 'none';

        // 显示保存成功提示
        showToast('游戏设置已保存');
    }
}

// 初始化测试模式UI
function initTestMode() {
    // 测试模式开关
    const testModeSwitch = document.getElementById('test-mode');
    const testModeControls = document.getElementById('test-mode-controls');
    const testModeClose = document.getElementById('test-mode-close');

    if (testModeSwitch && testModeControls) {
        // 测试模式开关事件
        testModeSwitch.addEventListener('change', (e) => {
            testModeControls.style.display = e.target.checked ? 'flex' : 'none';
        });

        // 关闭按钮事件
        if (testModeClose) {
            testModeClose.addEventListener('click', () => {
                testModeControls.style.display = 'none';
                testModeSwitch.checked = false;
            });
        }

        // 实现拖动功能
        makeDraggable(testModeControls);
    }

    // 测试模式相关事件处理
    const testAddCardButton = document.getElementById('test-add-card');
    const testClearHandButton = document.getElementById('test-clear-hand');
    const testPlayerSelect = document.getElementById('test-player-select');
    const testHandSelect = document.getElementById('test-hand-select');
    const testCardSuit = document.getElementById('test-card-suit');
    const testCardRank = document.getElementById('test-card-rank');

    // 检查测试事件是否已初始化，防止重复绑定
    if (window.testEventsInitialized) {
        return;
    }

    if (testAddCardButton && testClearHandButton && testPlayerSelect && testHandSelect && testCardSuit && testCardRank) {
        // 初始化时触发一次玩家选择变化，以正确设置手牌选择器显示状态
        if (testPlayerSelect.value === 'dealer') {
            testHandSelect.style.display = 'none';
        }

        // 添加牌
        testAddCardButton.addEventListener('click', () => {
            const playerValue = testPlayerSelect.value;
            const handIndex = parseInt(testHandSelect.value);
            const suit = testCardSuit.value;
            const rank = testCardRank.value;

            if (playerValue === 'dealer') {
                // 设置庄家手牌
                const currentDealerHand = window.game.dealerHand || [];
                // 创建新的牌对象
                const newCard = { suit, rank };
                // 检查是否启用了测试模式
                if (!window.game.isTestMode) {
                    window.game.isTestMode = true;
                    logger.info('已自动启用测试模式');
                }
                window.game.setDealerHand([...currentDealerHand, newCard]);
            } else {
                // 设置玩家手牌
                const playerIndex = parseInt(playerValue);
                const player = window.game.players[playerIndex];
                if (!player) {
                    logger.error('找不到指定的玩家');
                    return;
                }

                // 确保手牌数组容量足够
                while (player.hands.length <= handIndex) {
                    player.hands.push([]);
                }

                const currentHand = player.hands[handIndex] || [];
                // 创建新的牌对象
                const newCard = { suit, rank };
                // 检查是否启用了测试模式
                if (!window.game.isTestMode) {
                    window.game.isTestMode = true;
                    logger.info('已自动启用测试模式');
                }
                window.game.setCustomHand(playerIndex, handIndex, [...currentHand, newCard]);
            }
        });

        // 清空手牌
        testClearHandButton.addEventListener('click', () => {
            const playerValue = testPlayerSelect.value;
            const handIndex = parseInt(testHandSelect.value);

            if (playerValue === 'dealer') {
                window.game.setDealerHand([]);
            } else {
                const playerIndex = parseInt(playerValue);
                window.game.setCustomHand(playerIndex, handIndex, []);
            }
        });

        // 玩家选择变化时更新手牌选择器
        testPlayerSelect.addEventListener('change', () => {
            const playerValue = testPlayerSelect.value;
            testHandSelect.style.display = playerValue === 'dealer' ? 'none' : 'inline-block';
        });

        // 标记测试事件已初始化
        window.testEventsInitialized = true;
    }
}

// 使元素可拖动的函数 - 高性能版
function makeDraggable(element) {
    const header = element.querySelector('.test-control-header');
    const handle = header || element;

    // 初始化变量
    let startX, startY, startLeft, startTop, dragging = false;
    let touchId = null;
    let animationFrameId = null;
    let newX, newY;

    // 鼠标按下事件
    handle.onmousedown = dragStart;

    // 拖动开始
    function dragStart(e) {
        // 如果点击的是按钮或其他控制元素，不进行拖动
        if (e.target.id === 'test-mode-close' || e.target.tagName === 'BUTTON' ||
            e.target.tagName === 'SELECT' || e.target.tagName === 'INPUT') return;

        e.preventDefault();

        // 记录初始位置
        startX = e.clientX;
        startY = e.clientY;
        startLeft = parseInt(document.defaultView.getComputedStyle(element).left, 10) || 0;
        startTop = parseInt(document.defaultView.getComputedStyle(element).top, 10) || 0;

        newX = startLeft;
        newY = startTop;

        // 添加事件监听
        document.addEventListener('mousemove', dragMove);
        document.addEventListener('mouseup', dragEnd);

        // 添加样式
        element.classList.add('dragging');
        dragging = true;

        // 禁用过渡效果以减少延迟
        element.style.transition = 'none';
    }

    // 拖动过程 - 只更新位置变量
    function dragMove(e) {
        if (!dragging) return;
        e.preventDefault();

        // 计算新位置
        newX = startLeft + (e.clientX - startX);
        newY = startTop + (e.clientY - startY);

        // 使用requestAnimationFrame进行渲染
        if (!animationFrameId) {
            animationFrameId = requestAnimationFrame(updatePosition);
        }
    }

    // 位置更新函数 - 分离计算和渲染
    function updatePosition() {
        animationFrameId = null;
        if (!dragging) return;

        // 获取元素尺寸
        const rect = element.getBoundingClientRect();
        const elementWidth = rect.width;
        const elementHeight = rect.height;

        // 确保不超出屏幕边界
        let constrainedX = newX;
        let constrainedY = newY;

        if (constrainedX < 0) constrainedX = 0;
        if (constrainedY < 0) constrainedY = 0;
        if (constrainedX + elementWidth > window.innerWidth)
            constrainedX = window.innerWidth - elementWidth;
        if (constrainedY + elementHeight > window.innerHeight)
            constrainedY = window.innerHeight - elementHeight;

        // 直接设置样式，减少中间步骤
        element.style.left = `${constrainedX}px`;
        element.style.top = `${constrainedY}px`;

        // 如果仍在拖动，继续请求下一帧
        if (dragging) {
            animationFrameId = requestAnimationFrame(updatePosition);
        }
    }

    // 拖动结束
    function dragEnd() {
        document.removeEventListener('mousemove', dragMove);
        document.removeEventListener('mouseup', dragEnd);

        // 取消任何挂起的动画帧
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }

        element.classList.remove('dragging');
        dragging = false;

        // 恢复过渡效果
        element.style.transition = '';
    }

    // 触摸事件支持
    handle.addEventListener('touchstart', function(e) {
        // 如果点击的是按钮或其他控制元素，不进行拖动
        if (e.target.id === 'test-mode-close' || e.target.tagName === 'BUTTON' ||
            e.target.tagName === 'SELECT' || e.target.tagName === 'INPUT') return;

        // 如果已经在拖动中，忽略额外的触摸
        if (dragging) return;

        // 阻止触摸事件的默认行为
        e.preventDefault();

        const touch = e.touches[0];
        touchId = touch.identifier;

        startX = touch.clientX;
        startY = touch.clientY;
        startLeft = parseInt(document.defaultView.getComputedStyle(element).left, 10) || 0;
        startTop = parseInt(document.defaultView.getComputedStyle(element).top, 10) || 0;

        newX = startLeft;
        newY = startTop;

        element.classList.add('dragging');
        dragging = true;

        // 禁用过渡效果以减少延迟
        element.style.transition = 'none';

        document.addEventListener('touchmove', touchMove, { passive: false });
        document.addEventListener('touchend', touchEnd);
        document.addEventListener('touchcancel', touchEnd);
    }, { passive: false });

    function touchMove(e) {
        if (!dragging) return;

        // 阻止默认的触摸行为，确保拖动顺畅
        e.preventDefault();

        // 查找与开始触摸时相同ID的触摸点
        let currentTouch = null;
        for (let i = 0; i < e.touches.length; i++) {
            if (e.touches[i].identifier === touchId) {
                currentTouch = e.touches[i];
                break;
            }
        }

        // 如果找不到相应的触摸点，结束拖动
        if (!currentTouch) {
            touchEnd();
            return;
        }

        // 计算新位置
        newX = startLeft + (currentTouch.clientX - startX);
        newY = startTop + (currentTouch.clientY - startY);

        // 使用requestAnimationFrame进行渲染
        if (!animationFrameId) {
            animationFrameId = requestAnimationFrame(updatePosition);
        }
    }

    function touchEnd(e) {
        document.removeEventListener('touchmove', touchMove);
        document.removeEventListener('touchend', touchEnd);
        document.removeEventListener('touchcancel', touchEnd);

        // 取消任何挂起的动画帧
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }

        element.classList.remove('dragging');
        dragging = false;
        touchId = null;

        // 恢复过渡效果
        element.style.transition = '';

        // 阻止默认行为
        if (e && e.cancelable) e.preventDefault();
    }
}

// 初始化自动速度控制
function initSpeedControl() {
    const autoSpeedSlider = document.getElementById('auto-speed');
    const speedValue = document.getElementById('speed-value');
    const decreaseSpeedBtn = document.getElementById('decrease-speed');
    const increaseSpeedBtn = document.getElementById('increase-speed');

    if (autoSpeedSlider && speedValue) {
        // 速度调节事件
        autoSpeedSlider.addEventListener('input', function() {
            const value = this.value;
            speedValue.textContent = value;
            if (window.game) {
                window.game.setAutoDelay(parseInt(value));
            }
        });

        // 减速按钮事件
        if (decreaseSpeedBtn) {
            decreaseSpeedBtn.addEventListener('click', function() {
                // 获取当前速度值
                let currentSpeed = parseInt(autoSpeedSlider.value);
                // 确保不低于最小值
                currentSpeed = Math.max(parseInt(autoSpeedSlider.min), currentSpeed - 100);
                // 更新滑动条和显示值
                autoSpeedSlider.value = currentSpeed;
                speedValue.textContent = currentSpeed;
                // 更新游戏速度
                if (window.game) {
                    window.game.setAutoDelay(currentSpeed);
                }
            });
        }

        // 加速按钮事件
        if (increaseSpeedBtn) {
            increaseSpeedBtn.addEventListener('click', function() {
                // 获取当前速度值
                let currentSpeed = parseInt(autoSpeedSlider.value);
                // 确保不超过最大值
                currentSpeed = Math.min(parseInt(autoSpeedSlider.max), currentSpeed + 100);
                // 更新滑动条和显示值
                autoSpeedSlider.value = currentSpeed;
                speedValue.textContent = currentSpeed;
                // 更新游戏速度
                if (window.game) {
                    window.game.setAutoDelay(currentSpeed);
                }
            });
        }
    }
}
