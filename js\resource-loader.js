/**
 * 资源加载器 - 优化资源加载性能
 * 提供图片预加载、延迟加载和缓存功能
 */
(function() {
    // 创建日志记录器
    const logger = window.Logger ? window.Logger.getLogger('ResourceLoader') : {
        debug: () => {},
        info: () => {},
        warn: console.warn,
        error: console.error
    };
    
    // 资源缓存
    const resourceCache = {
        images: new Map(),
        audio: new Map(),
        data: new Map()
    };
    
    // 配置选项
    const config = {
        basePath: '',
        imagePath: 'images/',
        audioPath: 'audio/',
        dataPath: 'data/',
        lazyLoadThreshold: 200, // 延迟加载阈值（像素）
        preloadPriority: ['high', 'medium', 'low'],
        maxConcurrentLoads: 6,
        retryCount: 2,
        retryDelay: 1000
    };
    
    // 加载队列
    const loadQueue = [];
    let activeLoads = 0;
    
    // 延迟加载观察器
    let lazyLoadObserver = null;
    
    // 初始化延迟加载观察器
    function initLazyLoadObserver() {
        if (!window.IntersectionObserver) {
            logger.warn('浏览器不支持IntersectionObserver，将使用滚动事件实现延迟加载');
            return false;
        }
        
        lazyLoadObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const src = element.getAttribute('data-src');
                    const srcset = element.getAttribute('data-srcset');
                    
                    if (src) {
                        element.src = src;
                        element.removeAttribute('data-src');
                    }
                    
                    if (srcset) {
                        element.srcset = srcset;
                        element.removeAttribute('data-srcset');
                    }
                    
                    // 加载完成后取消观察
                    observer.unobserve(element);
                    
                    logger.debug(`延迟加载资源: ${src || srcset}`);
                }
            });
        }, {
            rootMargin: `${config.lazyLoadThreshold}px`,
            threshold: 0.01
        });
        
        return true;
    }
    
    // 处理加载队列
    function processQueue() {
        if (loadQueue.length === 0 || activeLoads >= config.maxConcurrentLoads) {
            return;
        }
        
        // 按优先级排序队列
        loadQueue.sort((a, b) => {
            const priorityA = config.preloadPriority.indexOf(a.priority);
            const priorityB = config.preloadPriority.indexOf(b.priority);
            return priorityA - priorityB;
        });
        
        // 加载下一个资源
        const nextLoad = loadQueue.shift();
        activeLoads++;
        
        loadResource(nextLoad.type, nextLoad.url, nextLoad.options)
            .then(nextLoad.resolve)
            .catch(nextLoad.reject)
            .finally(() => {
                activeLoads--;
                // 继续处理队列
                processQueue();
            });
    }
    
    // 加载资源
    function loadResource(type, url, options = {}) {
        return new Promise((resolve, reject) => {
            let resource;
            let retries = 0;
            
            const attemptLoad = () => {
                switch (type) {
                    case 'image':
                        resource = new Image();
                        resource.onload = () => {
                            resourceCache.images.set(url, resource);
                            resolve(resource);
                        };
                        resource.onerror = (error) => {
                            if (retries < config.retryCount) {
                                retries++;
                                logger.warn(`加载图片失败，重试 (${retries}/${config.retryCount}): ${url}`);
                                setTimeout(attemptLoad, config.retryDelay);
                            } else {
                                logger.error(`加载图片失败: ${url}`, error);
                                reject(error);
                            }
                        };
                        resource.src = url.startsWith('http') ? url : config.basePath + config.imagePath + url;
                        break;
                        
                    case 'audio':
                        resource = new Audio();
                        resource.oncanplaythrough = () => {
                            resourceCache.audio.set(url, resource);
                            resolve(resource);
                        };
                        resource.onerror = (error) => {
                            if (retries < config.retryCount) {
                                retries++;
                                logger.warn(`加载音频失败，重试 (${retries}/${config.retryCount}): ${url}`);
                                setTimeout(attemptLoad, config.retryDelay);
                            } else {
                                logger.error(`加载音频失败: ${url}`, error);
                                reject(error);
                            }
                        };
                        resource.src = url.startsWith('http') ? url : config.basePath + config.audioPath + url;
                        resource.load();
                        break;
                        
                    case 'json':
                        fetch(url.startsWith('http') ? url : config.basePath + config.dataPath + url)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                resourceCache.data.set(url, data);
                                resolve(data);
                            })
                            .catch(error => {
                                if (retries < config.retryCount) {
                                    retries++;
                                    logger.warn(`加载JSON失败，重试 (${retries}/${config.retryCount}): ${url}`);
                                    setTimeout(attemptLoad, config.retryDelay);
                                } else {
                                    logger.error(`加载JSON失败: ${url}`, error);
                                    reject(error);
                                }
                            });
                        break;
                        
                    default:
                        reject(new Error(`不支持的资源类型: ${type}`));
                }
            };
            
            attemptLoad();
        });
    }
    
    // 导出资源加载器
    window.ResourceLoader = {
        // 配置加载器
        configure: function(options) {
            Object.assign(config, options);
            return this;
        },
        
        // 初始化
        init: function() {
            // 初始化延迟加载观察器
            const observerInitialized = initLazyLoadObserver();
            
            // 如果不支持IntersectionObserver，使用滚动事件
            if (!observerInitialized) {
                this.setupScrollLazyLoad();
            }
            
            logger.info('ResourceLoader已初始化');
            return this;
        },
        
        // 预加载图片
        preloadImage: function(url, priority = 'medium', options = {}) {
            return new Promise((resolve, reject) => {
                // 检查缓存
                if (resourceCache.images.has(url)) {
                    resolve(resourceCache.images.get(url));
                    return;
                }
                
                // 添加到加载队列
                loadQueue.push({
                    type: 'image',
                    url,
                    priority,
                    options,
                    resolve,
                    reject
                });
                
                // 处理队列
                processQueue();
            });
        },
        
        // 预加载多个图片
        preloadImages: function(urls, priority = 'medium', options = {}) {
            return Promise.all(urls.map(url => this.preloadImage(url, priority, options)));
        },
        
        // 预加载音频
        preloadAudio: function(url, priority = 'low', options = {}) {
            return new Promise((resolve, reject) => {
                // 检查缓存
                if (resourceCache.audio.has(url)) {
                    resolve(resourceCache.audio.get(url));
                    return;
                }
                
                // 添加到加载队列
                loadQueue.push({
                    type: 'audio',
                    url,
                    priority,
                    options,
                    resolve,
                    reject
                });
                
                // 处理队列
                processQueue();
            });
        },
        
        // 加载JSON数据
        loadJSON: function(url, priority = 'high', options = {}) {
            return new Promise((resolve, reject) => {
                // 检查缓存
                if (resourceCache.data.has(url)) {
                    resolve(resourceCache.data.get(url));
                    return;
                }
                
                // 添加到加载队列
                loadQueue.push({
                    type: 'json',
                    url,
                    priority,
                    options,
                    resolve,
                    reject
                });
                
                // 处理队列
                processQueue();
            });
        },
        
        // 设置图片延迟加载
        lazyLoadImage: function(element) {
            if (!lazyLoadObserver) {
                // 如果不支持IntersectionObserver，添加data-lazy属性
                element.setAttribute('data-lazy', 'true');
                return;
            }
            
            // 开始观察元素
            lazyLoadObserver.observe(element);
        },
        
        // 使用滚动事件实现延迟加载
        setupScrollLazyLoad: function() {
            const lazyLoad = window.PerformanceUtils.throttle(() => {
                const lazyElements = document.querySelectorAll('[data-lazy="true"]');
                
                lazyElements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    const isVisible = 
                        rect.top <= window.innerHeight + config.lazyLoadThreshold &&
                        rect.bottom >= -config.lazyLoadThreshold &&
                        rect.left <= window.innerWidth + config.lazyLoadThreshold &&
                        rect.right >= -config.lazyLoadThreshold;
                    
                    if (isVisible) {
                        const src = element.getAttribute('data-src');
                        const srcset = element.getAttribute('data-srcset');
                        
                        if (src) {
                            element.src = src;
                            element.removeAttribute('data-src');
                        }
                        
                        if (srcset) {
                            element.srcset = srcset;
                            element.removeAttribute('data-srcset');
                        }
                        
                        element.removeAttribute('data-lazy');
                        
                        logger.debug(`滚动延迟加载资源: ${src || srcset}`);
                    }
                });
            }, 200);
            
            // 添加滚动事件监听
            window.addEventListener('scroll', lazyLoad);
            window.addEventListener('resize', lazyLoad);
            window.addEventListener('orientationchange', lazyLoad);
            
            // 初始检查
            setTimeout(lazyLoad, 200);
        },
        
        // 获取缓存的资源
        getImage: function(url) {
            return resourceCache.images.get(url);
        },
        
        getAudio: function(url) {
            return resourceCache.audio.get(url);
        },
        
        getData: function(url) {
            return resourceCache.data.get(url);
        },
        
        // 清除缓存
        clearCache: function(type) {
            if (!type) {
                resourceCache.images.clear();
                resourceCache.audio.clear();
                resourceCache.data.clear();
            } else {
                resourceCache[type].clear();
            }
            return this;
        }
    };
})();
