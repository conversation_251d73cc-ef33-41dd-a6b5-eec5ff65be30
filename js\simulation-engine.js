/**
 * BlackJack3 - 21点快速模拟引擎
 * 基于游戏本体逻辑直接模拟，无需UI交互
 */

/**
 * 模拟配置类 - 存储所有模拟参数
 */
class SimulationConfig {
    constructor() {
        // 基础游戏设置
        this.numberOfGames = 10000; // 默认模拟局数
        this.numberOfDecks = 8; // 默认牌库数量
        this.penetrationRate = 0.65; // 默认渗透率
        this.dealerStandSoft17 = true; // 默认庄家软17停牌
        this.playerCount = 1; // 默认玩家数量

        // 算牌系统设置
        this.countingSystem = 'hi-lo'; // 默认使用Hi-Lo系统
        this.countingSystems = ['none', 'hi-lo', 'omega-ii', 'halves']; // 支持的算牌系统

        // 下注策略设置
        this.bettingStrategy = {
            enabled: true,
            useCountingBasedBetting: true, // 是否使用基于算牌的下注策略
            fixedBet: 100, // 固定下注金额
            thresholds: [ // 真数阈值及对应下注金额
                { trueCount: -999, bet: 100 }, // 默认最小下注
                { trueCount: 1, bet: 200 },
                { trueCount: 2, bet: 300 },
                { trueCount: 3, bet: 400 },
                { trueCount: 4, bet: 500 },
                { trueCount: 5, bet: 600 }
            ]
        };

        // 玩家策略设置
        this.playerStrategy = {
            useBasicStrategy: true // 使用基本策略
        };

        // 高级设置
        this.startingChips = 100000; // 起始筹码
        this.batchSize = 1000; // 批量处理大小
    }

    /**
     * 从表单更新配置
     * @param {Object} formData - 表单数据对象
     */
    updateFromForm(formData) {
        // 基础游戏设置
        this.numberOfGames = parseInt(formData.numberOfGames) || 10000;
        this.numberOfDecks = parseInt(formData.numberOfDecks) || 8;
        this.penetrationRate = parseFloat(formData.penetrationRate) || 0.65;
        this.dealerStandSoft17 = Boolean(formData.dealerStandSoft17);
        this.playerCount = parseInt(formData.playerCount) || 1;

        // 算牌系统
        this.countingSystem = formData.countingSystem || 'hi-lo';

        // 下注策略
        this.bettingStrategy.enabled = Boolean(formData.bettingEnabled);
        this.bettingStrategy.useCountingBasedBetting = Boolean(formData.useCountingBasedBetting);
        this.bettingStrategy.fixedBet = parseInt(formData.fixedBet) || 100;

        // 如果表单中有thresholds数据，则更新
        if (formData.thresholds && Array.isArray(formData.thresholds)) {
            this.bettingStrategy.thresholds = formData.thresholds;
        }

        // 起始筹码
        this.startingChips = parseInt(formData.startingChips) || 100000;

        return this;
    }

    /**
     * 验证配置有效性
     * @returns {Object} 包含isValid和errorMessages的对象
     */
    validate() {
        return {
            isValid: true,
            errorMessages: []
        };
    }

    /**
     * 创建预设策略
     * @param {string} presetName - 预设名称：'conservative', 'balanced', 'aggressive'
     * @returns {SimulationConfig} 更新后的配置对象
     */
    applyPreset(presetName) {
        // 根据预设名称设置下注阈值
        switch (presetName) {
            case 'conservative':
                // 保守策略 - 较小的下注增幅
                this.bettingStrategy.thresholds = [
                    { trueCount: -999, bet: 100 }, // 默认最小下注
                    { trueCount: 1, bet: 150 },
                    { trueCount: 2, bet: 200 },
                    { trueCount: 3, bet: 250 },
                    { trueCount: 4, bet: 300 },
                    { trueCount: 5, bet: 350 }
                ];
                break;

            case 'balanced':
                // 均衡策略 - 中等下注增幅
                this.bettingStrategy.thresholds = [
                    { trueCount: -999, bet: 100 }, // 默认最小下注
                    { trueCount: 1, bet: 200 },
                    { trueCount: 2, bet: 300 },
                    { trueCount: 3, bet: 400 },
                    { trueCount: 4, bet: 500 },
                    { trueCount: 5, bet: 600 }
                ];
                break;

            case 'aggressive':
                // 激进策略 - 较大的下注增幅
                this.bettingStrategy.thresholds = [
                    { trueCount: -999, bet: 100 }, // 默认最小下注
                    { trueCount: 1, bet: 250 },
                    { trueCount: 2, bet: 400 },
                    { trueCount: 3, bet: 600 },
                    { trueCount: 4, bet: 900 },
                    { trueCount: 5, bet: 1200 }
                ];
                break;

            default:
                // 默认使用均衡策略
                return this.applyPreset('balanced');
        }

        // 确保下注策略启用
        this.bettingStrategy.enabled = true;
        this.bettingStrategy.useCountingBasedBetting = true;

        return this;
    }
}

/**
 * 模拟结果类 - 存储模拟数据和分析结果
 */
class SimulationResult {
    constructor() {
        // 基础统计数据
        this.totalGames = 0;
        this.completedGames = 0;
        this.totalPlayers = 0;
        this.initialChips = 0;         // 每个玩家的初始筹码
        this.initialTotalChips = 0;    // 所有玩家的初始筹码总和
        this.finalChips = 0;
        this.netProfit = 0;
        this.profitPerGame = 0;

        // 胜率指标
        this.winCount = 0;
        this.loseCount = 0;
        this.pushCount = 0;
        this.blackjackCount = 0;
        this.winRate = 0;
        this.blackjackRate = 0;

        // 操作统计
        this.doubleCount = 0;
        this.splitCount = 0;
        this.surrenderCount = 0;
        this.doubleSuccessRate = 0;
        this.doubleWinCount = 0;

        // 下注历史记录
        this.betHistory = [];
        this.gameRecords = [];

        // 玩家详细统计信息
        this.playerStats = {
            handsPlayed: 0,
            wins: 0,
            losses: 0,
            pushes: 0,
            blackjacks: 0,
            surrenders: 0,
            doubles: 0,
            doubleWins: 0,
            splits: 0,
            totalBetAmount: 0,
            totalWinAmount: 0,
            totalLossAmount: 0,
            largestWin: 0,
            largestLoss: 0,
            longestWinStreak: 0,
            longestLoseStreak: 0,
            currentWinStreak: 0,
            currentLoseStreak: 0
        };

        // 高级分析数据
        this.trueCountDistribution = {}; // 真数分布
        this.betDistribution = {}; // 下注分布
        this.chipsCurve = []; // 筹码变化曲线
        this.maxDrawdown = 0; // 最大回撤
        this.maxConsecutiveLosses = 0; // 最大连续亏损次数

        // 执行性能数据
        this.simulationTime = 0; // 模拟总耗时(毫秒)
        this.gamesPerSecond = 0; // 每秒模拟局数
    }
}

/**
 * 模拟引擎类 - 基于游戏本体逻辑实现
 */
class SimulationEngine {
    constructor() {
        this.config = new SimulationConfig();
        this.result = new SimulationResult();
        this.gameInstance = null;
        this.cardCountingSystem = null;
        this.autoStrategy = null;
        this.isRunning = false;
        this.isPaused = false;
        this.progressCallback = null;
        this.completeCallback = null;
        this.startTime = 0;
        this.currentBatch = 0;
        this.batchTimerId = null;
    }

    /**
     * 检查游戏核心组件是否存在
     * @returns {boolean} 组件是否齐全
     */
    checkGameComponents() {
        // 检查游戏本体关键类是否存在
        const hasGame = typeof window.Game === 'function';
        const hasCardCountingSystem = typeof window.CardCountingSystem === 'function';
        const hasAutoStrategy = typeof window.AutoStrategy === 'function';

        if (!hasGame || !hasCardCountingSystem || !hasAutoStrategy) {
            console.error('游戏核心组件检查失败:', {
                Game: hasGame,
                CardCountingSystem: hasCardCountingSystem,
                AutoStrategy: hasAutoStrategy
            });
            return false;
        }

        // 尝试创建实例
        if (!window.game && hasGame) {
            try {
                console.log('尝试创建游戏实例...');
                window.game = new window.Game();
                window.game.init();
            } catch (e) {
                console.error('创建游戏实例失败:', e);
                return false;
            }
        }

        if (!window.cardCountingSystem && hasCardCountingSystem) {
            try {
                console.log('尝试创建算牌系统实例...');
                window.cardCountingSystem = new window.CardCountingSystem();
            } catch (e) {
                console.error('创建算牌系统实例失败:', e);
                return false;
            }
        }

        if (!window.autoStrategy && hasAutoStrategy) {
            try {
                console.log('尝试创建自动策略实例...');
                window.autoStrategy = new window.AutoStrategy();
            } catch (e) {
                console.error('创建自动策略实例失败:', e);
                return false;
            }
        }

        console.log('游戏核心组件检查通过');
        return true;
    }

    /**
     * 初始化模拟引擎
     * @param {SimulationConfig} config - 模拟配置
     * @param {Function} progressCallback - 进度回调函数
     * @param {Function} completeCallback - 完成回调函数
     * @returns {boolean} 初始化是否成功
     */
    initialize(config, progressCallback, completeCallback) {
        try {
            console.info('初始化模拟引擎...');

            // 保存配置和回调
            this.config = config;
            this.progressCallback = progressCallback;
            this.completeCallback = completeCallback;

            // 检查游戏组件
            if (!this.checkGameComponents()) {
                console.error('游戏核心组件未找到，无法初始化模拟引擎');

                // 尝试主动创建游戏实例
                if (typeof window.Game === 'function' && !window.game) {
                    try {
                        console.log('尝试主动创建游戏实例...');
                        window.game = new Game();
                        window.game.init();
                        console.log('游戏实例创建成功');

                        // 创建其他必要组件
                        if (typeof window.CardCountingSystem === 'function' && !window.cardCountingSystem) {
                            window.cardCountingSystem = new CardCountingSystem();
                        }

                        if (typeof window.AutoStrategy === 'function' && !window.autoStrategy) {
                            window.autoStrategy = new AutoStrategy();
                        }

                        // 重新检查
                        if (!this.checkGameComponents()) {
                            return false;
                        }
                    } catch (error) {
                        console.error('主动创建游戏实例失败:', error);
                        return false;
                    }
                } else {
                    return false;
                }
            }

            // 初始化结果对象
            this.result = new SimulationResult();
            this.result.totalGames = config.numberOfGames;
            this.result.totalPlayers = config.playerCount;
            this.result.initialChips = config.startingChips;

            // 设置总初始筹码（所有玩家的初始筹码总和）
            this.result.initialTotalChips = config.startingChips * config.playerCount;

            console.log('初始化结果对象:', {
                totalGames: this.result.totalGames,
                totalPlayers: this.result.totalPlayers,
                initialChips: this.result.initialChips,
                initialTotalChips: this.result.initialTotalChips
            });

            // 重置状态
            this.isRunning = false;
            this.isPaused = false;

            console.info('模拟引擎初始化完成');
            return true;
        } catch (error) {
            console.error('模拟引擎初始化失败:', error);
            return false;
        }
    }

    /**
     * 初始化游戏实例
     * @private
     */
    _initializeGameInstance() {
        console.info('初始化游戏实例...');

        try {
            // 创建游戏实例
            this.gameInstance = new Game(this.config.numberOfDecks);
            console.log(`创建了 ${this.config.numberOfDecks} 副牌的游戏实例`);

            // 设置庄家规则
            this.gameInstance.dealerStandSoft17 = this.config.dealerStandSoft17;
            console.log(`庄家软17停牌规则: ${this.config.dealerStandSoft17 ? '是' : '否'}`);

            // 确保规则被正确设置
            if (this.gameInstance.dealerStandSoft17 !== this.config.dealerStandSoft17) {
                console.warn(`庄家软17停牌规则设置失败，尝试再次设置`);
                // 尝试使用defineProperty强制设置
                Object.defineProperty(this.gameInstance, 'dealerStandSoft17', {
                    value: this.config.dealerStandSoft17,
                    writable: true,
                    configurable: true
                });
                console.log(`庄家软17停牌规则重新设置: ${this.gameInstance.dealerStandSoft17 ? '是' : '否'}`);
            }

            // 设置渗透率
            // 确保渗透率是小数形式 (0.0-1.0)
            const penetrationRate = this.config.penetrationRate > 1 ? this.config.penetrationRate / 100 : this.config.penetrationRate;
            this.gameInstance.deck.setPenetrationRate(penetrationRate);
            console.log(`设置渗透率: ${penetrationRate * 100}%`);

            // 初始化运行计数
            this.gameInstance.runningCount = 0;

            // 设置玩家数量和初始筹码
            if (typeof this.gameInstance.updatePlayerCount === 'function') {
                this.gameInstance.updatePlayerCount(0); // 先清空所有玩家
            } else {
                // 如果没有updatePlayerCount方法，直接清空players数组
                this.gameInstance.players = [];
            }

            console.log(`添加 ${this.config.playerCount} 名玩家，每人初始筹码: ${this.config.startingChips}`);

            // 记录总初始筹码
            this.result.initialTotalChips = this.config.startingChips * this.config.playerCount;

            // 添加玩家并设置初始筹码
            for (let i = 0; i < this.config.playerCount; i++) {
                this.gameInstance.addPlayer(`玩家${i+1}`);

                // 设置初始筹码
                if (this.gameInstance.players[i]) {
                    this.gameInstance.players[i].chips = this.config.startingChips;
                    this.gameInstance.players[i].initialChips = this.config.startingChips; // 记录初始筹码，用于计算盈亏
                    console.log(`玩家${i+1} 初始筹码设置为 ${this.gameInstance.players[i].chips}`);
                } else {
                    console.error(`无法设置玩家${i+1}的初始筹码，玩家对象不存在`);
                }
            }

            // 确认玩家数量和初始筹码
            if (this.gameInstance.players && this.gameInstance.players.length > 0) {
                const playerChips = this.gameInstance.players.map(p => p.chips);
                console.log(`确认玩家初始筹码: [${playerChips.join(', ')}], 总计: ${this.result.initialTotalChips}`);
            } else {
                console.error('玩家数组为空或不存在');
            }

            // 初始化算牌系统
            this.cardCountingSystem = window.cardCountingSystem || new window.CardCountingSystem();

            // 确保使用配置中指定的算牌系统
            if (this.cardCountingSystem.currentSystem !== this.config.countingSystem) {
                this.cardCountingSystem.setCountingSystem(this.config.countingSystem);
            }
            console.log(`使用算牌系统: ${this.config.countingSystem}`);

            // 设置下注策略
            if (this.config.bettingStrategy.enabled && this.config.bettingStrategy.useCountingBasedBetting) {
                this.cardCountingSystem.setAutoBetStrategy(true, this.config.bettingStrategy.thresholds);
                console.log('启用基于算牌的下注策略:', JSON.stringify(this.config.bettingStrategy.thresholds));
            } else {
                // 确保禁用自动下注策略
                this.cardCountingSystem.setAutoBetStrategy(false);
                console.log(`使用固定下注金额: ${this.config.bettingStrategy.fixedBet}`);
            }

            // 初始化自动策略
            this.autoStrategy = window.autoStrategy || new window.AutoStrategy();
            console.log('初始化自动策略完成');

            // 初始化性能计数器和回撤跟踪
            this._totalSimulationTime = 0;
            this._highestChips = this.result.initialTotalChips;
            this._currentDrawdown = 0;

            // 确保游戏状态正确
            this.gameInstance.gameState = 'betting';

            // 初始化筹码曲线
            this.result.chipsCurve = [{
                game: 0,
                chips: Array(this.config.playerCount).fill(this.config.startingChips)
            }];

            // 洗牌
            this.gameInstance.deck.shuffle();
            console.log('初始洗牌完成');

            console.info('游戏实例初始化完成');
        } catch (error) {
            console.error('初始化游戏实例时出错:', error);
            throw error; // 重新抛出错误，让上层处理
        }
    }

    /**
     * 下注阶段 - 使用游戏本体的自动下注逻辑
     * @param {number} trueCount - 当前真数
     * @private
     */
    _placeBets(trueCount) {
        try {
            // 确保真数是有效数字
            const validTrueCount = isNaN(trueCount) ? 0 : trueCount;

            console.log(`下注阶段 - 真数: ${validTrueCount}`);

            // 为每个玩家设置下注金额
            for (let i = 0; i < this.gameInstance.players.length; i++) {
                const player = this.gameInstance.players[i];

                if (!player) {
                    console.error(`玩家索引 ${i} 不存在`);
                    continue;
                }

                // 根据下注策略确定下注金额
                let betAmount = this.config.bettingStrategy.fixedBet;

                // 记录下注策略状态
                console.log(`下注策略状态: 启用=${this.config.bettingStrategy.enabled}, 使用算牌=${this.config.bettingStrategy.useCountingBasedBetting}, 算牌系统=${this.config.countingSystem}`);

                if (this.config.bettingStrategy.enabled && this.config.bettingStrategy.useCountingBasedBetting && this.config.countingSystem !== 'none') {
                    try {
                        // 直接从阈值中查找适用的下注金额
                        const thresholds = this.config.bettingStrategy.thresholds;
                        if (thresholds && thresholds.length > 0) {
                            // 默认使用最低阈值的下注额
                            betAmount = thresholds[0].bet;

                            // 遍历阈值，找到适用的下注金额
                            for (let i = 0; i < thresholds.length; i++) {
                                const threshold = thresholds[i];
                                if (validTrueCount >= threshold.trueCount) {
                                    betAmount = threshold.bet;
                                } else {
                                    // 如果当前真数小于阈值，使用前一个阈值的下注额
                                    break;
                                }
                            }

                            console.log(`基于真数 ${validTrueCount} 设置下注金额: ${betAmount} (阈值: ${JSON.stringify(thresholds)})`);
                        } else {
                            console.warn('下注阈值配置无效，使用固定下注金额');
                        }
                    } catch (error) {
                        console.error('获取推荐下注金额时出错:', error);
                    }
                } else {
                    console.log(`使用固定下注金额: ${betAmount}`);
                }

                // 确保下注金额是有效数字
                if (isNaN(betAmount) || betAmount <= 0) {
                    console.warn(`下注金额无效 (${betAmount})，使用默认值 ${this.config.bettingStrategy.fixedBet}`);
                    betAmount = this.config.bettingStrategy.fixedBet;
                }

                // 确保下注金额不超过玩家筹码和最大下注限制
                const maxBet = this.gameInstance.maxBet || 500;
                betAmount = Math.min(betAmount, player.chips, maxBet);

                // 确保下注金额不低于最小下注限制
                const minBet = this.gameInstance.minBet || 10;
                betAmount = Math.max(betAmount, minBet);

                console.log(`玩家 ${i+1} 下注金额: ${betAmount}, 筹码: ${player.chips}`);

                // 清除当前下注，避免累积
                if (player.getCurrentBet && typeof player.getCurrentBet === 'function' && player.getCurrentBet() > 0) {
                    if (player.clearBet && typeof player.clearBet === 'function') {
                        player.clearBet();
                    }
                }

                // 执行下注 - 使用游戏本体的下注方法
                if (player.placeBet && typeof player.placeBet === 'function') {
                    player.placeBet(betAmount);
                    if (player.confirmBet && typeof player.confirmBet === 'function') {
                        player.confirmBet();
                    }
                } else {
                    // 如果没有placeBet方法，直接设置下注金额
                    player.currentBet = betAmount;
                    player.originalBet = betAmount;
                    player.betStatus = 'confirmed';
                    player.chips -= betAmount;
                }

                // 记录下注分布
                const betKey = betAmount.toString();
                this.result.betDistribution[betKey] = (this.result.betDistribution[betKey] || 0) + 1;
            }
        } catch (error) {
            console.error('下注阶段出错:', error);
            // 出错时使用默认下注
            this._placeDefaultBets();
        }
    }

    /**
     * 使用默认下注金额进行下注（出错时的备用方法）
     * @private
     */
    _placeDefaultBets() {
        console.warn('使用默认下注金额');

        for (let i = 0; i < this.gameInstance.players.length; i++) {
            const player = this.gameInstance.players[i];
            if (!player) continue;

            // 使用固定下注金额
            const betAmount = Math.min(this.config.bettingStrategy.fixedBet, player.chips);

            // 直接设置下注金额
            player.currentBet = betAmount;
            player.originalBet = betAmount;
            player.betStatus = 'confirmed';
            player.chips -= betAmount;

            // 记录下注分布
            const betKey = betAmount.toString();
            this.result.betDistribution[betKey] = (this.result.betDistribution[betKey] || 0) + 1;
        }
    }

    /**
     * 发牌阶段 - 使用游戏本体的发牌逻辑
     * @private
     */
    _dealInitialCards() {
        // 如果游戏本体有发牌方法，直接调用
        if (this.gameInstance.dealInitialCards && typeof this.gameInstance.dealInitialCards === 'function') {
            this.gameInstance.dealInitialCards();
            return;
        }

        // 否则，使用自定义发牌逻辑
        // 给每个玩家发两张牌
        for (let i = 0; i < this.gameInstance.players.length; i++) {
            const player = this.gameInstance.players[i];
            player.hands = [[]]; // 清空手牌

            // 发两张牌
            for (let j = 0; j < 2; j++) {
                const card = this.gameInstance.deck.deal();
                player.hands[0].push(card);

                // 更新计数
                if (this.config.countingSystem !== 'none') {
                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
                }
            }
        }

        // 给庄家发两张牌
        this.gameInstance.dealerHand = [];
        for (let j = 0; j < 2; j++) {
            const card = this.gameInstance.deck.deal();
            this.gameInstance.dealerHand.push(card);

            // 更新计数 (只对明牌计数)
            if (j === 0 && this.config.countingSystem !== 'none') {
                this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
            }
        }
    }

    /**
     * 玩家回合 - 使用游戏本体的自动策略
     * @private
     */
    _playPlayerTurns() {
        // 如果游戏本体有玩家回合方法，直接调用
        if (this.gameInstance.playPlayerTurnsWithoutUI && typeof this.gameInstance.playPlayerTurnsWithoutUI === 'function') {
            this.gameInstance.playPlayerTurnsWithoutUI(this.autoStrategy);
            return;
        }

        // 处理每个玩家
        for (let playerIndex = 0; playerIndex < this.gameInstance.players.length; playerIndex++) {
            const player = this.gameInstance.players[playerIndex];

            // 处理玩家的每手牌
            let handIndex = 0;
            while (handIndex < player.hands.length) {
                const hand = player.hands[handIndex];

                // 检查是否是黑杰克
                if (hand.length === 2 && this.gameInstance.isBlackjack(hand)) {
                    handIndex++;
                    continue;
                }

                // 获取庄家明牌
                const dealerUpCard = this.gameInstance.dealerHand[0];

                // 获取当前手牌点数
                const handPoints = this.gameInstance.calculateHandValue(hand);

                // 获取当前下注金额
                let currentBet = 0;
                if (Array.isArray(player.bets) && player.bets[handIndex] !== undefined) {
                    currentBet = player.bets[handIndex];
                } else if (typeof player.currentBet === 'number') {
                    currentBet = player.currentBet;
                }

                // 使用基本策略决定行动
                // 标记手牌是否为分牌后的手牌，确保策略正确应用
                if (player.hands.length > 1) {
                    hand.split = true;
                }

                let action = window.getAutoAction(hand, dealerUpCard, this.gameInstance, false);

                // 详细记录策略决策过程
                console.log(`[策略决策] 玩家${playerIndex+1} 手牌${handIndex+1}: 点数=${handPoints.value} 软牌=${handPoints.isSoft} 长度=${hand.length} 庄家明牌=${dealerUpCard.rank}${dealerUpCard.suit} 策略建议=${action}`);
                console.log(`[策略决策] 玩家${playerIndex+1} 当前筹码=${player.chips} 当前下注=${currentBet} 手牌数量=${player.hands.length} 分牌=${hand.split ? '是' : '否'}`);

                let keepPlaying = true;

                while (keepPlaying) {
                    switch (action) {
                        case 'H': // 要牌
                            // A分牌后的手牌不允许要牌，包括AA
                            if (hand.isAceSplit && hand.length >= 2) {
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1}是A分牌后的手牌，不能要牌，自动停牌`);
                                keepPlaying = false;
                                break;
                            }

                            console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 执行要牌`);
                            const card = this.gameInstance.deck.deal();
                            hand.push(card);
                            console.log(`[执行操作] 玩家${playerIndex+1} 要到的牌: ${card.rank}${card.suit}`);

                            // 更新计数
                            if (this.config.countingSystem !== 'none') {
                                this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
                            }

                            // 检查是否爆牌
                            if (this.gameInstance.isBust(hand)) {
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 爆牌，点数: ${this.gameInstance.calculateHandValue(hand).value}`);
                                keepPlaying = false;
                            } else {
                                // 继续决策
                                // 确保分牌标记在要牌后仍然保留
                                if (hand.split) {
                                    console.log(`[策略决策] 保留分牌标记`);
                                }
                                action = window.getAutoAction(hand, dealerUpCard, this.gameInstance, false);
                                console.log(`[策略决策] 玩家${playerIndex+1} 手牌${handIndex+1} 要牌后新策略: ${action}, 当前点数: ${this.gameInstance.calculateHandValue(hand).value}, 分牌=${hand.split ? '是' : '否'}`);
                            }
                            break;

                        case 'S': // 停牌
                            // 计算并记录正确的手牌点数
                            const handValue = this.gameInstance.calculateHandValue(hand);
                            console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 执行停牌，最终点数: ${handValue.value}`);
                            keepPlaying = false;
                            break;

                        case 'D': // 加倍
                            // 获取当前下注金额
                            let betAmount = 0;
                            if (Array.isArray(player.bets) && player.bets[handIndex] !== undefined) {
                                betAmount = player.bets[handIndex];

                                // 检查分牌后的手牌下注是否为0或异常值，如果是则修正
                                if ((betAmount === 0 || betAmount === undefined || isNaN(betAmount)) && hand.split) {
                                    console.warn(`[执行操作] 检测到分牌手牌${handIndex+1}下注异常: ${betAmount}`);

                                    // 尝试从initialBets获取正确的下注金额
                                    if (Array.isArray(player.initialBets) && player.initialBets[handIndex] > 0) {
                                        betAmount = player.initialBets[handIndex];
                                        player.bets[handIndex] = betAmount;
                                        console.warn(`[执行操作] 从initialBets修正为${betAmount}`);
                                    } else if (player.bets.some(bet => bet > 0)) {
                                        // 如果有任何一手牌有有效下注，使用该下注金额
                                        const validBet = player.bets.find(bet => bet > 0);
                                        betAmount = validBet;
                                        player.bets[handIndex] = betAmount;
                                        console.warn(`[执行操作] 从其他手牌下注修正为${betAmount}`);
                                    } else {
                                        // 如果没有任何有效下注，使用默认下注金额
                                        betAmount = 100; // 默认下注金额
                                        player.bets[handIndex] = betAmount;
                                        console.warn(`[执行操作] 使用默认下注金额修正为${betAmount}`);
                                    }
                                }

                                // 记录当前所有手牌的下注情况
                                console.log(`[执行操作] 当前所有手牌下注情况: ${JSON.stringify(player.bets)}`);
                            } else if (typeof player.currentBet === 'number') {
                                betAmount = player.currentBet;
                            }

                            // 确保下注金额是有效的正数
                            if (betAmount <= 0 || isNaN(betAmount)) {
                                console.warn(`[执行操作] 下注金额无效: ${betAmount}，使用默认值100`);
                                betAmount = 100;
                                if (Array.isArray(player.bets)) {
                                    player.bets[handIndex] = betAmount;
                                }
                            }

                            // 只能在第一次行动时加倍
                            if (hand.length === 2 && player.chips >= betAmount) {
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 执行加倍，当前筹码: ${player.chips}, 当前下注: ${betAmount}`);

                                // 记录加倍前的手牌
                                console.log(`[执行操作] 加倍前手牌: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(hand).value}`);

                                // 执行加倍操作
                                const additionalBet = betAmount;

                                // 更新下注金额
                                if (Array.isArray(player.bets)) {
                                    player.bets[handIndex] += additionalBet;
                                    console.log(`[执行操作] 加倍使用bets数组: 手牌${handIndex+1}下注从${betAmount}增加到${player.bets[handIndex]}`);

                                    // 记录原始下注（用于结算）
                                    if (!Array.isArray(player.initialBets)) {
                                        player.initialBets = Array(player.hands.length).fill(0);
                                        player.initialBets[handIndex] = betAmount;
                                        console.log(`[执行操作] 加倍创建initialBets数组: ${JSON.stringify(player.initialBets)}`);
                                    }
                                } else {
                                    player.currentBet += additionalBet;
                                    console.log(`[执行操作] 加倍使用currentBet: 从${betAmount}增加到${player.currentBet}`);

                                    // 记录原始下注
                                    if (player.originalBet === undefined) {
                                        player.originalBet = betAmount;
                                        console.log(`[执行操作] 加倍设置originalBet: ${player.originalBet}`);
                                    }
                                }

                                // 扣除筹码
                                player.chips -= additionalBet;
                                console.log(`[执行操作] 加倍扣除筹码: ${additionalBet}, 剩余筹码: ${player.chips}`);

                                // 标记为加倍手牌
                                hand.doubled = true;

                                // 发一张牌
                                const card = this.gameInstance.deck.deal();
                                hand.push(card);
                                console.log(`[执行操作] 加倍要到的牌: ${card.rank}${card.suit}`);
                                console.log(`[执行操作] 加倍后手牌: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(hand).value}`);

                                // 更新计数
                                if (this.config.countingSystem !== 'none') {
                                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
                                }

                                // 记录加倍操作
                                this.result.doubleCount++;

                                // 加倍后不能继续要牌
                                keepPlaying = false;
                            } else {
                                // 如果不能加倍，则要牌
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 无法加倍，原因: ${hand.length !== 2 ? '不是初始两张牌' : '筹码不足'}, 手牌长度: ${hand.length}, 筹码: ${player.chips}, 下注: ${betAmount}`);
                                action = 'H';
                            }
                            break;

                        case 'P': // 分牌
                            // 获取当前下注金额
                            let splitBetAmount = 0;
                            if (Array.isArray(player.bets) && player.bets[handIndex] !== undefined) {
                                splitBetAmount = player.bets[handIndex];
                            } else if (typeof player.currentBet === 'number') {
                                splitBetAmount = player.currentBet;
                            }

                            // 检查是否可以分牌
                            const canSplit = hand.length === 2 &&
                                            hand[0].rank === hand[1].rank &&
                                            player.chips >= splitBetAmount &&
                                            player.hands.length < 4; // 最多分到4手牌

                            // 只能在第一次行动时分牌
                            if (canSplit) {
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 执行分牌，当前筹码: ${player.chips}, 当前下注: ${splitBetAmount}, 当前手牌数: ${player.hands.length}`);
                                console.log(`[执行操作] 分牌前手牌: ${hand.map(c => c.rank + c.suit).join(', ')}`);

                                // 记录第一张牌是否是A
                                const isAce = hand[0].rank === 'A';

                                // 创建两手新牌
                                const firstHand = [hand[0]];
                                const secondHand = [hand[1]];

                                // 标记为分牌手牌
                                firstHand.split = true;
                                secondHand.split = true;

                                // 如果是A分牌，标记特殊状态
                                if (isAce) {
                                    firstHand.isAceSplit = true;
                                    secondHand.isAceSplit = true;
                                    console.log(`[执行操作] AA分牌，标记isAceSplit=true`);
                                }

                                // 为每手牌各补一张
                                const card1 = this.gameInstance.deck.deal();
                                const card2 = this.gameInstance.deck.deal();
                                firstHand.push(card1);
                                secondHand.push(card2);

                                console.log(`[执行操作] 第一手牌补牌: ${card1.rank}${card1.suit}, 第二手牌补牌: ${card2.rank}${card2.suit}`);

                                // 更新计数
                                if (this.config.countingSystem !== 'none') {
                                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card1);
                                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card2);
                                }

                                // 更新玩家手牌
                                player.hands[handIndex] = firstHand;
                                player.hands.push(secondHand);

                                // 为新手牌设置下注
                                if (Array.isArray(player.bets)) {
                                    // 如果使用bets数组
                                    const currentBet = player.bets[handIndex];
                                    // 确保新手牌的下注金额正确设置为与原手牌相同
                                    player.bets.push(currentBet);
                                    player.chips -= currentBet;

                                    // 验证新手牌下注是否正确设置
                                    const newHandIndex = player.hands.length - 1;
                                    if (player.bets[newHandIndex] !== currentBet) {
                                        console.warn(`[执行操作] 分牌后手牌${newHandIndex+1}下注设置错误，手动修正: 从${player.bets[newHandIndex]}到${currentBet}`);
                                        player.bets[newHandIndex] = currentBet;
                                    }

                                    console.log(`[执行操作] 分牌使用bets数组: 原手牌下注=${player.bets[handIndex]}, 新手牌下注=${player.bets[newHandIndex]}, 扣除筹码=${currentBet}`);
                                    console.log(`[执行操作] 当前手牌数=${player.hands.length}, 当前下注数组=${JSON.stringify(player.bets)}`);

                                    // 如果有initialBets数组，也需要更新
                                    if (Array.isArray(player.initialBets)) {
                                        const initialBet = player.initialBets[handIndex] || currentBet;
                                        player.initialBets.push(initialBet);

                                        // 验证新手牌初始下注是否正确设置
                                        if (player.initialBets[newHandIndex] !== initialBet) {
                                            console.warn(`[执行操作] 分牌后手牌${newHandIndex+1}初始下注设置错误，手动修正: 从${player.initialBets[newHandIndex]}到${initialBet}`);
                                            player.initialBets[newHandIndex] = initialBet;
                                        }

                                        console.log(`[执行操作] 更新initialBets数组: 原手牌初始下注=${player.initialBets[handIndex]}, 新手牌初始下注=${player.initialBets[newHandIndex]}`);
                                        console.log(`[执行操作] 当前初始下注数组=${JSON.stringify(player.initialBets)}`);
                                    } else {
                                        // 创建initialBets数组
                                        player.initialBets = Array(player.hands.length).fill(0);
                                        player.initialBets[handIndex] = currentBet;
                                        player.initialBets[newHandIndex] = currentBet;
                                        console.log(`[执行操作] 创建initialBets数组: ${JSON.stringify(player.initialBets)}`);
                                    }

                                    // 确保bets和initialBets数组长度与hands数组长度一致
                                    if (player.bets.length !== player.hands.length) {
                                        console.warn(`[执行操作] 下注数组长度(${player.bets.length})与手牌数组长度(${player.hands.length})不一致，进行修正`);
                                        while (player.bets.length < player.hands.length) {
                                            player.bets.push(currentBet);
                                        }
                                    }

                                    if (player.initialBets.length !== player.hands.length) {
                                        console.warn(`[执行操作] 初始下注数组长度(${player.initialBets.length})与手牌数组长度(${player.hands.length})不一致，进行修正`);
                                        while (player.initialBets.length < player.hands.length) {
                                            player.initialBets.push(currentBet);
                                        }
                                    }
                                } else {
                                    // 如果使用currentBet
                                    const currentBet = player.currentBet;
                                    player.chips -= currentBet;

                                    // 创建bets数组以支持多手牌下注
                                    player.bets = Array(player.hands.length).fill(0);
                                    player.bets[handIndex] = currentBet;
                                    player.bets[player.hands.length - 1] = currentBet;

                                    // 同时创建initialBets数组
                                    player.initialBets = Array(player.hands.length).fill(0);
                                    player.initialBets[handIndex] = player.originalBet || currentBet;
                                    player.initialBets[player.hands.length - 1] = player.originalBet || currentBet;

                                    console.log(`[执行操作] 从currentBet创建bets数组: ${JSON.stringify(player.bets)}`);
                                    console.log(`[执行操作] 从originalBet创建initialBets数组: ${JSON.stringify(player.initialBets)}`);
                                }

                                console.log(`[执行操作] 玩家${playerIndex+1} 分牌完成，当前手牌数: ${player.hands.length}, 剩余筹码: ${player.chips}`);
                                console.log(`[执行操作] 第一手牌: ${firstHand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(firstHand).value}`);
                                console.log(`[执行操作] 第二手牌: ${secondHand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(secondHand).value}`);

                                // 记录分牌操作
                                this.result.splitCount++;
                                this.result.playerStats.splits++; // <--- 添加此行

                                // 确保分牌标记正确设置
                                if (!firstHand.split) {
                                    console.warn(`[策略决策] 第一手牌分牌标记丢失，重新设置`);
                                    firstHand.split = true;
                                }
                                if (!secondHand.split) {
                                    console.warn(`[策略决策] 第二手牌分牌标记丢失，重新设置`);
                                    secondHand.split = true;
                                }

                                // 关键修改：分牌后，我们需要完全处理完第一手牌，然后再处理第二手牌
                                console.log(`[执行操作] 玩家${playerIndex+1} 分牌完成，现在处理第一手牌`);

                                // 创建一个递归函数来处理分牌后的手牌
                                const playHand = (hand, handIdx) => {
                                    console.log(`[执行操作] 开始处理玩家${playerIndex+1}的手牌${handIdx+1}`);

                                    // 检查是否是黑杰克
                                    if (hand.length === 2 && this.gameInstance.isBlackjack(hand)) {
                                        console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1}是黑杰克，自动停牌`);
                                        return;
                                    }

                                    // A分牌后的手牌不允许要牌，包括AA
                                    if (hand.isAceSplit && hand.length >= 2) {
                                        console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1}是A分牌后的手牌，只能要一张牌，自动停牌`);
                                        return;
                                    }

                                    // 获取策略 - 这里不排除分牌选项，允许再次分牌
                                    let handAction = window.getAutoAction(hand, dealerUpCard, this.gameInstance, false);
                                    console.log(`[策略决策] 玩家${playerIndex+1} 手牌${handIdx+1} 策略: ${handAction}, 分牌=${hand.split ? '是' : '否'}`);

                                    let handKeepPlaying = true;

                                    while (handKeepPlaying) {
                                        switch (handAction) {
                                            case 'H': // 要牌
                                                // A分牌后的手牌不允许要牌，包括AA
                                                if (hand.isAceSplit && hand.length >= 2) {
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1}是A分牌后的手牌，不能要牌，自动停牌`);
                                                    handKeepPlaying = false;
                                                    break;
                                                }

                                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 执行要牌`);
                                                const newCard = this.gameInstance.deck.deal();
                                                hand.push(newCard);
                                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 要到的牌: ${newCard.rank}${newCard.suit}`);

                                                // 更新计数
                                                if (this.config.countingSystem !== 'none') {
                                                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(newCard);
                                                }

                                                // 检查是否爆牌
                                                if (this.gameInstance.isBust(hand)) {
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 爆牌，点数: ${this.gameInstance.calculateHandValue(hand).value}`);
                                                    handKeepPlaying = false;
                                                } else {
                                                    // 继续决策
                                                    handAction = window.getAutoAction(hand, dealerUpCard, this.gameInstance, false);
                                                    console.log(`[策略决策] 玩家${playerIndex+1} 手牌${handIdx+1} 要牌后新策略: ${handAction}, 当前点数: ${this.gameInstance.calculateHandValue(hand).value}`);
                                                }
                                                break;

                                            case 'S': // 停牌
                                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 执行停牌，最终点数: ${this.gameInstance.calculateHandValue(hand).value}`);
                                                handKeepPlaying = false;
                                                break;

                                            case 'D': // 加倍
                                                // 获取当前下注金额
                                                let handBetAmount = 0;
                                                if (Array.isArray(player.bets) && player.bets[handIdx] !== undefined) {
                                                    handBetAmount = player.bets[handIdx];
                                                } else if (typeof player.currentBet === 'number') {
                                                    handBetAmount = player.currentBet;
                                                }

                                                // 只能在第一次行动时加倍
                                                if (hand.length === 2 && player.chips >= handBetAmount) {
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 执行加倍，当前筹码: ${player.chips}, 当前下注: ${handBetAmount}`);

                                                    // 执行加倍操作
                                                    const additionalBet = handBetAmount;

                                                    // 更新下注金额
                                                    if (Array.isArray(player.bets)) {
                                                        player.bets[handIdx] += additionalBet;
                                                        console.log(`[执行操作] 加倍使用bets数组: 手牌${handIdx+1}下注从${handBetAmount}增加到${player.bets[handIdx]}`);

                                                        // 记录原始下注（用于结算）
                                                        if (!Array.isArray(player.initialBets)) {
                                                            player.initialBets = Array(player.hands.length).fill(0);
                                                            player.initialBets[handIdx] = handBetAmount;
                                                        }
                                                    } else {
                                                        player.currentBet += additionalBet;
                                                        console.log(`[执行操作] 加倍使用currentBet: 从${handBetAmount}增加到${player.currentBet}`);

                                                        // 记录原始下注
                                                        if (player.originalBet === undefined) {
                                                            player.originalBet = handBetAmount;
                                                        }
                                                    }

                                                    // 扣除筹码
                                                    player.chips -= additionalBet;
                                                    console.log(`[执行操作] 加倍扣除筹码: ${additionalBet}, 剩余筹码: ${player.chips}`);

                                                    // 标记为加倍手牌
                                                    hand.doubled = true;

                                                    // 发一张牌
                                                    const doubleCard = this.gameInstance.deck.deal();
                                                    hand.push(doubleCard);
                                                    console.log(`[执行操作] 加倍要到的牌: ${doubleCard.rank}${doubleCard.suit}`);
                                                    console.log(`[执行操作] 加倍后手牌: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(hand).value}`);

                                                    // 更新计数
                                                    if (this.config.countingSystem !== 'none') {
                                                        this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(doubleCard);
                                                    }

                                                    // 记录加倍操作
                                                    this.result.doubleCount++;

                                                    // 加倍后不能继续要牌
                                                    handKeepPlaying = false;
                                                } else {
                                                    // 如果不能加倍，则要牌
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 无法加倍，原因: ${hand.length !== 2 ? '不是初始两张牌' : '筹码不足'}, 手牌长度: ${hand.length}, 筹码: ${player.chips}, 下注: ${handBetAmount}`);
                                                    handAction = 'H';
                                                }
                                                break;

                                            case 'P': // 分牌
                                                // 获取当前下注金额
                                                let splitBetAmount = 0;
                                                if (Array.isArray(player.bets) && player.bets[handIdx] !== undefined) {
                                                    splitBetAmount = player.bets[handIdx];
                                                } else if (typeof player.currentBet === 'number') {
                                                    splitBetAmount = player.currentBet;
                                                }

                                                // 检查是否可以分牌
                                                const canSplit = hand.length === 2 &&
                                                                hand[0].rank === hand[1].rank &&
                                                                player.chips >= splitBetAmount &&
                                                                player.hands.length < 4; // 最多分到4手牌

                                                // 只能在第一次行动时分牌
                                                if (canSplit) {
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 执行分牌，当前筹码: ${player.chips}, 当前下注: ${splitBetAmount}, 当前手牌数: ${player.hands.length}`);
                                                    console.log(`[执行操作] 分牌前手牌: ${hand.map(c => c.rank + c.suit).join(', ')}`);

                                                    // 记录第一张牌是否是A
                                                    const isAce = hand[0].rank === 'A';

                                                    // 创建两手新牌
                                                    const splitFirstHand = [hand[0]];
                                                    const splitSecondHand = [hand[1]];

                                                    // 标记为分牌手牌
                                                    splitFirstHand.split = true;
                                                    splitSecondHand.split = true;

                                                    // 如果是A分牌，标记特殊状态
                                                    if (isAce) {
                                                        splitFirstHand.isAceSplit = true;
                                                        splitSecondHand.isAceSplit = true;
                                                        console.log(`[执行操作] AA分牌，标记isAceSplit=true`);
                                                    }

                                                    // 为每手牌各补一张
                                                    const card1 = this.gameInstance.deck.deal();
                                                    const card2 = this.gameInstance.deck.deal();
                                                    splitFirstHand.push(card1);
                                                    splitSecondHand.push(card2);

                                                    console.log(`[执行操作] 第一手牌补牌: ${card1.rank}${card1.suit}, 第二手牌补牌: ${card2.rank}${card2.suit}`);

                                                    // 更新计数
                                                    if (this.config.countingSystem !== 'none') {
                                                        this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card1);
                                                        this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card2);
                                                    }

                                                    // 更新玩家手牌
                                                    player.hands[handIdx] = splitFirstHand;
                                                    player.hands.push(splitSecondHand);

                                                    // 为新手牌设置下注
                                                    if (Array.isArray(player.bets)) {
                                                        // 如果使用bets数组
                                                        const currentBet = player.bets[handIdx];
                                                        // 确保新手牌的下注金额正确设置为与原手牌相同
                                                        player.bets.push(currentBet);
                                                        player.chips -= currentBet;

                                                        // 验证新手牌下注是否正确设置
                                                        const newHandIndex = player.hands.length - 1;
                                                        if (player.bets[newHandIndex] !== currentBet) {
                                                            console.warn(`[执行操作] 分牌后手牌${newHandIndex+1}下注设置错误，手动修正: 从${player.bets[newHandIndex]}到${currentBet}`);
                                                            player.bets[newHandIndex] = currentBet;
                                                        }

                                                        // 如果有initialBets数组，也需要更新
                                                        if (Array.isArray(player.initialBets)) {
                                                            const initialBet = player.initialBets[handIdx] || currentBet;
                                                            player.initialBets.push(initialBet);
                                                        } else {
                                                            // 创建initialBets数组
                                                            player.initialBets = Array(player.hands.length).fill(0);
                                                            player.initialBets[handIdx] = currentBet;
                                                            player.initialBets[newHandIndex] = currentBet;
                                                        }

                                                        // 确保bets和initialBets数组长度与hands数组长度一致
                                                        if (player.bets.length !== player.hands.length) {
                                                            while (player.bets.length < player.hands.length) {
                                                                player.bets.push(currentBet);
                                                            }
                                                        }

                                                        if (player.initialBets.length !== player.hands.length) {
                                                            while (player.initialBets.length < player.hands.length) {
                                                                player.initialBets.push(currentBet);
                                                            }
                                                        }
                                                    } else {
                                                        // 如果使用currentBet
                                                        const currentBet = player.currentBet;
                                                        player.chips -= currentBet;

                                                        // 创建bets数组以支持多手牌下注
                                                        player.bets = Array(player.hands.length).fill(0);
                                                        player.bets[handIdx] = currentBet;
                                                        player.bets[player.hands.length - 1] = currentBet;

                                                        // 同时创建initialBets数组
                                                        player.initialBets = Array(player.hands.length).fill(0);
                                                        player.initialBets[handIdx] = player.originalBet || currentBet;
                                                        player.initialBets[player.hands.length - 1] = player.originalBet || currentBet;
                                                    }

                                                    console.log(`[执行操作] 玩家${playerIndex+1} 分牌完成，当前手牌数: ${player.hands.length}, 剩余筹码: ${player.chips}`);
                                                    console.log(`[执行操作] 第一手牌: ${splitFirstHand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(splitFirstHand).value}`);
                                                    console.log(`[执行操作] 第二手牌: ${splitSecondHand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(splitSecondHand).value}`);

                                                    // 记录分牌操作
                                                    this.result.splitCount++;
                                                    this.result.playerStats.splits++; // <--- 添加此行

                                                    // 递归处理新分出的两手牌
                                                    // 先处理第一手牌
                                                    playHand(splitFirstHand, handIdx);

                                                    // 再处理第二手牌
                                                    const splitSecondHandIndex = player.hands.length - 1;
                                                    playHand(splitSecondHand, splitSecondHandIndex);

                                                    // 分牌处理完毕，结束当前手牌处理
                                                    handKeepPlaying = false;
                                                } else {
                                                    // 如果不能分牌，则默认要牌
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 无法分牌，原因: ${hand.length !== 2 ? '不是初始两张牌' : (hand[0].rank !== hand[1].rank ? '不是对子' : (player.chips < splitBetAmount ? '筹码不足' : '已达到最大手牌数'))}`);
                                                    handAction = 'H';
                                                }
                                                break;

                                            case 'R': // 投降
                                                // 只能在第一次行动时投降
                                                if (hand.length === 2) {
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 执行投降，手牌: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(hand).value}`);

                                                    // 标记手牌为已投降
                                                    hand.surrendered = true;

                                                    // 记录投降操作
                                                    this.result.surrenderCount++;

                                                    // 投降后结束这手牌
                                                    handKeepPlaying = false;
                                                } else {
                                                    // 不能投降，则按照基本策略要牌
                                                    console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 无法投降（不是初始两张牌），手牌长度: ${hand.length}, 改为要牌`);
                                                    handAction = 'H';
                                                }
                                                break;

                                            default:
                                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIdx+1} 未知操作: ${handAction}，默认结束回合`);
                                                handKeepPlaying = false;
                                        }
                                    }

                                    console.log(`[执行操作] 完成处理玩家${playerIndex+1}的手牌${handIdx+1}`);
                                };

                                // 处理第一手牌
                                playHand(firstHand, handIndex);

                                // 不需要在这里处理第二手牌，因为在playHand函数中已经递归处理了所有分牌手牌

                                // 分牌处理完毕，继续循环
                                keepPlaying = false;
                            } else {
                                // 如果不能分牌，则默认要牌
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 无法分牌，原因: ${hand.length !== 2 ? '不是初始两张牌' : (hand[0].rank !== hand[1].rank ? '不是对子' : (player.chips < splitBetAmount ? '筹码不足' : '已达到最大手牌数'))}`);
                                console.log(`[执行操作] 手牌详情: 长度=${hand.length}, 牌=${hand.map(c => c.rank + c.suit).join(', ')}, 筹码=${player.chips}, 下注=${splitBetAmount}, 手牌数=${player.hands.length}`);
                                action = 'H';
                            }
                            break;

                        case 'R': // 投降
                            // 只能在第一次行动时投降
                            if (hand.length === 2) {
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 执行投降，手牌: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${this.gameInstance.calculateHandValue(hand).value}`);

                                // 标记手牌为已投降
                                hand.surrendered = true;

                                // 退还一半赌注（在结算阶段处理）
                                // 注意：这里不将currentBet设为0，而是在结算阶段处理
                                // 这样可以在结算阶段知道原始下注金额

                                // 记录投降操作
                                this.result.surrenderCount++;

                                // 投降后结束这手牌
                                keepPlaying = false;
                            } else {
                                // 不能投降，则按照基本策略要牌
                                console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 无法投降（不是初始两张牌），手牌长度: ${hand.length}, 改为要牌`);
                                action = 'H';
                            }
                            break;

                        default:
                            console.log(`[执行操作] 玩家${playerIndex+1} 手牌${handIndex+1} 未知操作: ${action}，默认结束回合`);
                            keepPlaying = false;
                    }
                }

                handIndex++;
            }
        }
    }

    /**
     * 庄家回合 - 使用游戏本体的庄家逻辑
     * @private
     */
    _playDealerTurn() {
        // 检查游戏本体是否有dealerPlay方法（游戏本体中的庄家回合方法）
        if (this.gameInstance.dealerPlay && typeof this.gameInstance.dealerPlay === 'function') {
            console.log('使用游戏本体的dealerPlay方法');
            // 由于dealerPlay是异步方法，我们需要同步执行它
            // 创建一个同步版本的dealerPlay
            this._syncDealerPlay();
            return;
        }

        // 如果游戏本体有playDealerTurn方法，直接调用
        if (this.gameInstance.playDealerTurn && typeof this.gameInstance.playDealerTurn === 'function') {
            console.log('使用游戏本体的playDealerTurn方法');
            this.gameInstance.playDealerTurn();
            return;
        }

        console.log('使用模拟引擎自定义庄家逻辑');

        // 检查是否有玩家还在牌局中
        let activePlayersExist = false;
        for (const player of this.gameInstance.players) {
            for (const hand of player.hands) {
                if (player.currentBet > 0 && !this.gameInstance.isBust(hand)) {
                    activePlayersExist = true;
                    break;
                }
            }
            if (activePlayersExist) break;
        }

        // 如果没有活跃玩家，庄家不需要要牌
        if (!activePlayersExist) return;

        // 计算庄家手牌点数
        let dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);

        // 更新第二张牌的计数
        if (this.config.countingSystem !== 'none') {
            this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(this.gameInstance.dealerHand[1]);
        }

        // 庄家按规则要牌
        // 检查是否黑杰克，如果是则不要牌
        if (this.gameInstance.isBlackjack(this.gameInstance.dealerHand)) {
            console.log('庄家黑杰克，不需要要牌');
        } else {
            // 修改规则：当dealerStandSoft17为false时，所有软牌（包括软17、软18、软19、软20）都要继续要牌
            while (dealerPoints.value < 17 || (dealerPoints.isSoft && !this.gameInstance.dealerStandSoft17)) {
            const card = this.gameInstance.deck.deal();
            this.gameInstance.dealerHand.push(card);

            // 更新计数
            if (this.config.countingSystem !== 'none') {
                this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
            }

            // 重新计算点数
            dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);
            console.log(`庄家当前点数: ${dealerPoints.value}, 软牌: ${dealerPoints.isSoft}, 软17停牌: ${this.gameInstance.dealerStandSoft17}`);

            // 检查是否需要继续要牌
            if (dealerPoints.value > 21) {
                // 爆牌，停牌
                break;
            } else if (!dealerPoints.isSoft) {
                // 如果是硬牌
                if (dealerPoints.value >= 17) {
                    // 硬17及以上停牌
                    break;
                }
            } else if (this.gameInstance.dealerStandSoft17) {
                // 如果是软牌且规则是软17停牌
                if (dealerPoints.value >= 17) {
                    // 软17及以上停牌
                    break;
                }
            }
            // 其他情况继续要牌:
            // 1. 硬牌且点数小于17
            // 2. 软牌且软17不停牌规则
            }
        }
    }

    /**
     * 同步执行游戏本体的庄家回合逻辑
     * @private
     */
    _syncDealerPlay() {
        try {
            // 确保所有庄家牌都是明牌
            for (const card of this.gameInstance.dealerHand) {
                if (card.hidden) {
                    card.setHidden(false);
                }
            }

            // 获取庄家软17点停牌设置
            const standOnSoft17 = this.gameInstance.dealerStandSoft17;

            // 计算庄家手牌点数
            let dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);

            console.log(`庄家初始手牌点数: ${dealerPoints.value}, 软牌: ${dealerPoints.isSoft}, 软17停牌: ${standOnSoft17}`);

            // 更新第二张牌的计数
            if (this.config.countingSystem !== 'none') {
                this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(this.gameInstance.dealerHand[1]);
            }

            // 庄家按规则要牌
            // 检查是否黑杰克，如果是则不要牌
            if (this.gameInstance.isBlackjack(this.gameInstance.dealerHand)) {
                console.log('庄家黑杰克，不需要要牌');
            } else {
                // 修改规则：当standOnSoft17为false时，所有软牌（包括软17、软18、软19、软20）都要继续要牌
                while (dealerPoints.value < 17 ||
                      (dealerPoints.isSoft && !standOnSoft17)) {

                // 检查剩余牌数
                if (this.gameInstance.checkDeckAndShuffle && typeof this.gameInstance.checkDeckAndShuffle === 'function') {
                    this.gameInstance.checkDeckAndShuffle();
                }

                const card = this.gameInstance.deck.deal();
                if (!card) {
                    console.error('没有牌可抽了');
                    break;
                }

                // 确保牌是明牌
                card.setHidden(false);

                this.gameInstance.dealerHand.push(card);
                console.log(`庄家要牌: ${card.rank}${card.suit}`);

                // 更新计数
                if (this.config.countingSystem !== 'none') {
                    this.gameInstance.runningCount += this.cardCountingSystem.getCardValue(card);
                }

                // 重新计算点数
                dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);
                console.log(`庄家当前点数: ${dealerPoints.value}, 软牌: ${dealerPoints.isSoft}, 软17停牌: ${standOnSoft17}`);

                // 检查是否需要继续要牌
                if (dealerPoints.value > 21) {
                    // 爆牌，停牌
                    break;
                } else if (!dealerPoints.isSoft) {
                    // 如果是硬牌
                    if (dealerPoints.value >= 17) {
                        // 硬17及以上停牌
                        break;
                    }
                } else if (standOnSoft17) {
                    // 如果是软牌且规则是软17停牌
                    if (dealerPoints.value >= 17) {
                        // 软17及以上停牌
                        break;
                    }
                }
                // 其他情况继续要牌:
                // 1. 硬牌且点数小于17
                // 2. 软牌且软17不停牌规则
                }
            }

            console.log(`庄家最终点数: ${dealerPoints.value}, 爆牌: ${this.gameInstance.isBust(this.gameInstance.dealerHand)}`);

        } catch (error) {
            console.error('执行庄家回合时出错:', error);
        }
    }

    /**
     * 结算阶段 - 使用游戏本体的结算逻辑
     * @private
     */
    _settleGame() {
        try {
            console.log('开始结算游戏...');

            // 注意：这里不再调用游戏本体的settleGame方法
            // 因为我们已经在_simulateOneGame方法中处理了这种情况
            // 这里只执行自定义结算逻辑

            console.log('使用自定义结算逻辑');

            // 确保庄家手牌存在
            if (!this.gameInstance.dealerHand || !Array.isArray(this.gameInstance.dealerHand) || this.gameInstance.dealerHand.length === 0) {
                console.error('庄家手牌无效，无法结算');
                return;
            }

            // 计算庄家点数和状态
            const dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);
            const dealerBust = this.gameInstance.isBust(this.gameInstance.dealerHand);
            const dealerBlackjack = this.gameInstance.isBlackjack(this.gameInstance.dealerHand);

            console.log(`庄家手牌: ${this.gameInstance.dealerHand.length}张, 点数: ${dealerPoints.value}, 爆牌: ${dealerBust}, 黑杰克: ${dealerBlackjack}`);

            // 遍历所有玩家
            for (let playerIndex = 0; playerIndex < this.gameInstance.players.length; playerIndex++) {
                const player = this.gameInstance.players[playerIndex];

                if (!player) {
                    console.error(`玩家索引 ${playerIndex} 不存在`);
                    continue;
                }

                console.log(`结算玩家 ${player.name}, 当前筹码: ${player.chips}`);

                // 确保玩家手牌数组存在
                if (!player.hands || !Array.isArray(player.hands)) {
                    console.error(`玩家 ${player.name} 的手牌数组无效`);
                    continue;
                }

                // 遍历玩家的所有手牌
                for (let handIndex = 0; handIndex < player.hands.length; handIndex++) {
                    const hand = player.hands[handIndex];

                    if (!hand || !Array.isArray(hand)) {
                        console.error(`玩家 ${player.name} 的手牌 ${handIndex} 无效`);
                        continue;
                    }

                    // 获取当前下注金额
                    let currentBet = 0;
                    if (typeof player.currentBet === 'number') {
                        currentBet = player.currentBet;
                    } else if (Array.isArray(player.bets) && player.bets[handIndex]) {
                        currentBet = player.bets[handIndex];
                    }

                    // 如果没有下注，跳过
                    if (currentBet <= 0) {
                        console.log(`玩家 ${player.name} 手牌 ${handIndex} 没有下注，跳过结算`);
                        continue;
                    }

                    // 获取原始下注金额
                    let originalBet = currentBet;
                    if (typeof player.originalBet === 'number') {
                        originalBet = player.originalBet;
                    } else if (Array.isArray(player.initialBets) && player.initialBets[handIndex]) {
                        originalBet = player.initialBets[handIndex];
                    }

                    // 计算手牌点数
                    const handValue = this.gameInstance.calculateHandValue(hand);
                    console.log(`[结算阶段] 玩家 ${player.name} 手牌 ${handIndex}: ${hand.length}张, 点数: ${handValue.value}, 下注: ${currentBet}, 原始下注: ${originalBet}`);
                    console.log(`[结算阶段] 手牌详情: ${hand.map(c => c.rank + c.suit).join(', ')}, 加倍: ${hand.doubled ? '是' : '否'}, 分牌: ${hand.split ? '是' : '否'}, 投降: ${hand.surrendered ? '是' : '否'}`);

                    // 记录下注历史
                    const betRecord = {
                        playerIndex,
                        playerName: player.name,
                        handIndex,
                        bet: currentBet,
                        originalBet: originalBet,
                        result: null,
                        profit: 0,
                        game: this.result.completedGames + 1,
                        timestamp: Date.now(),
                        playerPoints: handValue.value, // 存储玩家点数
                        dealerPoints: dealerPoints.value, // dealerPoints 是在循环外计算的庄家最终点数对象
                        playerCards: hand.map(c => ({ rank: c.rank, suit: c.suit })),
                        dealerCards: this.gameInstance.dealerHand.map(c => ({ rank: c.rank, suit: c.suit }))
                    };

                    let profit = 0;

                    // 判断结果
                    if (hand.surrendered) {
                        // 玩家已投降，返还一半赌注
                        profit = -currentBet / 2;
                        betRecord.result = 'surrender';
                        this.result.surrenderCount++;
                        console.log(`玩家 ${player.name} 已投降，返还 ${currentBet / 2} 筹码，亏损 ${Math.abs(profit)}`);
                    } else if (this.gameInstance.isBust(hand)) {
                        // 玩家爆牌
                        profit = -currentBet;
                        betRecord.result = 'bust';
                        this.result.loseCount++;
                        console.log(`玩家 ${player.name} 爆牌，输 ${Math.abs(profit)}`);
                    } else if (this.gameInstance.isBlackjack(hand)) {
                        // 玩家黑杰克
                        if (dealerBlackjack) {
                            // 双方黑杰克，平局
                            profit = 0;
                            betRecord.result = 'push';
                            this.result.pushCount++;
                            console.log(`玩家 ${player.name} 和庄家都是黑杰克，平局`);
                        } else {
                            // 玩家单方黑杰克，赔率3:2
                            profit = currentBet * 1.5;
                            betRecord.result = 'blackjack';
                            this.result.winCount++;
                            this.result.blackjackCount++;
                            console.log(`玩家 ${player.name} 黑杰克，赢 ${profit}`);
                        }
                    } else if (dealerBlackjack) {
                        // 庄家黑杰克，玩家输
                        profit = -currentBet;
                        betRecord.result = 'lose';
                        this.result.loseCount++;
                        console.log(`庄家黑杰克，玩家 ${player.name} 输 ${Math.abs(profit)}`);
                    } else if (dealerBust) {
                        // 庄家爆牌，玩家赢
                        profit = currentBet;
                        betRecord.result = 'win';
                        this.result.winCount++;
                        console.log(`庄家爆牌，玩家 ${player.name} 赢 ${profit}`);

                        // 如果是加倍的手牌，记录加倍获胜
                        if (hand.doubled || (hand.length === 3 && currentBet > originalBet)) {
                            this.result.doubleWinCount++;
                            console.log(`玩家 ${player.name} 加倍获胜`);
                        }
                    } else {
                        // 比较点数
                        if (handValue.value > dealerPoints.value) {
                            // 玩家点数高，赢
                            profit = currentBet;
                            betRecord.result = 'win';
                            this.result.winCount++;
                            console.log(`玩家 ${player.name} 点数高于庄家，赢 ${profit}`);

                            // 如果是加倍的手牌，记录加倍获胜
                            if (hand.doubled || (hand.length === 3 && currentBet > originalBet)) {
                                this.result.doubleWinCount++;
                                console.log(`玩家 ${player.name} 加倍获胜`);
                            }
                        } else if (handValue.value < dealerPoints.value) {
                            // 庄家点数高，输
                            profit = -currentBet;
                            betRecord.result = 'lose';
                            this.result.loseCount++;
                            console.log(`玩家 ${player.name} 点数低于庄家，输 ${Math.abs(profit)}`);
                        } else {
                            // 点数相同，平局
                            profit = 0;
                            betRecord.result = 'push';
                            this.result.pushCount++;
                            console.log(`玩家 ${player.name} 与庄家点数相同，平局`);
                        }
                    }

                    // 更新筹码
                    player.chips += currentBet + profit;
                    console.log(`玩家 ${player.name} 结算后筹码: ${player.chips}`);

                    // 更新玩家统计信息
                    switch (betRecord.result) {
                        case 'win':
                        case 'blackjack':
                            this.result.playerStats.wins++;
                            this.result.playerStats.totalWinAmount += profit;
                            this.result.playerStats.largestWin = Math.max(this.result.playerStats.largestWin, profit);
                            this.result.playerStats.currentWinStreak++;
                            this.result.playerStats.currentLoseStreak = 0;
                            break;

                        case 'lose':
                        case 'bust':
                            this.result.playerStats.losses++;
                            this.result.playerStats.totalLossAmount += Math.abs(profit);
                            this.result.playerStats.largestLoss = Math.max(this.result.playerStats.largestLoss, Math.abs(profit));
                            this.result.playerStats.currentLoseStreak++;
                            this.result.playerStats.currentWinStreak = 0;
                            break;

                        case 'push':
                            this.result.playerStats.pushes++;
                            break;

                        case 'surrender':
                            // 投降计入投降次数和亏损金额
                            this.result.playerStats.surrenders++;
                            this.result.playerStats.totalLossAmount += Math.abs(profit);
                            // 投降也算作一种亏损，但不计入连败
                            this.result.playerStats.losses++;
                            this.result.playerStats.currentLoseStreak++;
                            this.result.playerStats.currentWinStreak = 0;
                            break;
                    }

                    // 更新连胜/连败纪录
                    this.result.playerStats.longestWinStreak = Math.max(this.result.playerStats.longestWinStreak, this.result.playerStats.currentWinStreak);
                    this.result.playerStats.longestLoseStreak = Math.max(this.result.playerStats.longestLoseStreak, this.result.playerStats.currentLoseStreak);

                    // 完成下注记录
                    betRecord.profit = profit;
                    this.result.betHistory.push(betRecord);

                    // 更新总赌注
                    this.result.playerStats.totalBetAmount += currentBet;

                    // 增加已玩手牌数
                    this.result.playerStats.handsPlayed++;
                }

                // 清除当前下注
                if (typeof player.clearBet === 'function') {
                    player.clearBet();
                } else {
                    player.currentBet = 0;
                    if (Array.isArray(player.bets)) {
                        player.bets.fill(0);
                    }
                }
            }

            console.log('游戏结算完成');
        } catch (error) {
            console.error('结算游戏时出错:', error);
        }
    }

    /**
     * 修复分牌手牌的结算结果
     * 当检测到分牌情况但结果记录数量不足时，尝试生成缺失的结果记录
     * @param {Array} results - 原始结算结果数组
     * @returns {Array} 修复后的结算结果数组
     * @private
     */
    _fixSplitHandResults(results) {
        if (!results || !Array.isArray(results) || results.length === 0) {
            console.error('无法修复空的结果数组');
            return results;
        }

        console.log('[修复分牌结果] 开始修复分牌手牌的结算结果');

        // 创建一个新的结果数组，包含原始结果
        const fixedResults = [...results];

        // 遍历所有玩家
        for (const player of this.gameInstance.players) {
            if (!player || !player.hands || player.hands.length <= 1) {
                continue; // 不是分牌情况，跳过
            }

            const playerIndex = this.gameInstance.players.indexOf(player);
            console.log(`[修复分牌结果] 检查玩家${playerIndex+1}的分牌结果`);

            // 检查每个手牌是否有对应的结果记录
            for (let handIndex = 0; handIndex < player.hands.length; handIndex++) {
                // 查找当前手牌的结果记录
                const existingResult = results.find(r =>
                    r.playerIndex === playerIndex && r.handIndex === handIndex);

                // 如果没有找到结果记录，且手牌有下注，则创建一个
                if (!existingResult && Array.isArray(player.bets) && player.bets[handIndex] > 0) {
                    console.warn(`[修复分牌结果] 玩家${playerIndex+1}的手牌${handIndex+1}没有结果记录，创建一个`);

                    const hand = player.hands[handIndex];
                    const betAmount = player.bets[handIndex];
                    const originalBet = Array.isArray(player.initialBets) ? player.initialBets[handIndex] : betAmount;

                    // 计算手牌点数和状态
                    const playerPoints = this.gameInstance.calculateHandValue(hand);
                    const playerBust = this.gameInstance.isBust(hand);
                    const playerBlackjack = this.gameInstance.isBlackjack(hand) && !hand.split;

                    // 计算庄家点数和状态
                    const dealerPoints = this.gameInstance.calculateHandValue(this.gameInstance.dealerHand);
                    const dealerBust = this.gameInstance.isBust(this.gameInstance.dealerHand);
                    const dealerBlackjack = this.gameInstance.isBlackjack(this.gameInstance.dealerHand);

                    // 判断是否为加倍或分牌
                    const isDoubleDown = hand.doubled || false;
                    const isSplitHand = hand.split || false;

                    // 结算结果
                    let outcome = '';
                    let profit = 0;

                    // 如果手牌已经投降
                    if (hand.surrendered) {
                        outcome = 'surrender';
                        profit = -originalBet / 2;
                    }
                    // 如果玩家爆牌
                    else if (playerBust) {
                        outcome = 'bust';
                        profit = -betAmount;
                    }
                    // 如果庄家爆牌且玩家没爆牌
                    else if (dealerBust) {
                        outcome = 'win';
                        profit = betAmount;
                    }
                    // 如果玩家有黑杰克
                    else if (playerBlackjack) {
                        // 如果庄家也有黑杰克，平局
                        if (dealerBlackjack) {
                            outcome = 'push';
                            profit = 0;
                        } else {
                            outcome = 'blackjack';
                            profit = betAmount * 1.5;
                        }
                    }
                    // 如果庄家有黑杰克且玩家没有黑杰克
                    else if (dealerBlackjack) {
                        outcome = 'lose';
                        profit = -betAmount;
                    }
                    // 比较点数
                    else {
                        if (playerPoints.value > dealerPoints.value) {
                            outcome = 'win';
                            profit = betAmount;
                        } else if (playerPoints.value < dealerPoints.value) {
                            outcome = 'lose';
                            profit = -betAmount;
                        } else {
                            outcome = 'push';
                            profit = 0;
                        }
                    }

                    // 创建新的结果记录
                    const newResult = {
                        playerIndex: playerIndex,
                        playerName: player.name,
                        handIndex: handIndex,
                        bet: betAmount,
                        originalBet: originalBet,
                        outcome: outcome,
                        profit: profit,
                        isDoubleDown: isDoubleDown,
                        isSplit: isSplitHand
                    };

                    console.log(`[修复分牌结果] 为玩家${playerIndex+1}的手牌${handIndex+1}创建结果: ${outcome}, 盈亏=${profit}`);

                    // 更新玩家筹码
                    if (outcome === 'win') {
                        player.chips += betAmount * 2;
                    } else if (outcome === 'blackjack') {
                        player.chips += betAmount * 2.5;
                    } else if (outcome === 'push') {
                        player.chips += betAmount;
                    } else if (outcome === 'surrender') {
                        player.chips += betAmount / 2;
                    }

                    // 添加到修复后的结果数组
                    fixedResults.push(newResult);
                }
            }
        }

        console.log(`[修复分牌结果] 修复完成，原始结果数量: ${results.length}, 修复后结果数量: ${fixedResults.length}`);
        return fixedResults;
    }

    /**
     * 记录结算结果
     * @param {Array} results - 游戏结算结果
     * @private
     */
    _recordResults(results) {
        try {
            console.log('记录结算结果...');

            if (!results) {
                console.error('结算结果为空');
                return;
            }

            if (!Array.isArray(results)) {
                console.error('结算结果不是数组:', typeof results);
                return;
            }

            // 检查是否有分牌情况
            let hasSplitHands = false;
            if (this.gameInstance && this.gameInstance.players) {
                for (const player of this.gameInstance.players) {
                    if (player && player.hands && player.hands.length > 1) {
                        hasSplitHands = true;
                        break;
                    }
                }
            }

            // 检查分牌情况下的结果记录数量
            if (hasSplitHands) {
                // 计算应该有多少条结果记录
                let expectedResults = 0;
                for (const player of this.gameInstance.players) {
                    if (player && player.hands) {
                        // 只计算有下注的手牌
                        for (let i = 0; i < player.hands.length; i++) {
                            const betAmount = Array.isArray(player.bets) ? player.bets[i] : player.currentBet;
                            if (betAmount > 0) {
                                expectedResults++;
                            }
                        }
                    }
                }

                // 如果结果记录数量不足，可能是结果处理有问题
                if (results.length < expectedResults) {
                    console.warn(`检测到分牌情况，但结果记录数量不足，预期${expectedResults}条，实际${results.length}条，可能存在结果处理问题`);
                } else {
                    console.log(`分牌情况下结果记录数量正确，共${results.length}条`);
                }
            }

            console.log(`处理 ${results.length} 条结算记录，结果数据:`, JSON.stringify(results));

            // 处理每个结果
            results.forEach((result, index) => {
                if (!result) {
                    console.error(`结果 #${index} 无效`);
                    return;
                }

                // 确保结果有必要的字段
                const playerIndex = result.playerIndex || 0;
                const playerName = result.playerName || `玩家${playerIndex + 1}`;
                const handIndex = result.handIndex || 0;
                const bet = typeof result.bet === 'number' ? result.bet : 0;
                const originalBet = typeof result.originalBet === 'number' ? result.originalBet : bet;
                const outcome = result.outcome || 'unknown';
                const profit = typeof result.profit === 'number' ? result.profit : 0;

                console.log(`结果 #${index}: 玩家=${playerName}, 结果=${outcome}, 盈亏=${profit}`);

                // 更新统计数据
                switch (outcome) {
                    case 'win':
                        this.result.winCount++;
                        break;
                    case 'lose':
                    case 'bust':
                        this.result.loseCount++;
                        break;
                    case 'push':
                        this.result.pushCount++;
                        break;
                    case 'blackjack':
                        this.result.winCount++;
                        this.result.blackjackCount++;
                        break;
                    case 'surrender':
                        // 确保投降次数正确计数
                        this.result.surrenderCount++;
                        console.log(`记录投降结果: 玩家=${playerName}, 手牌=${handIndex}, 下注=${bet}`);
                        break;
                    default:
                        console.warn(`未知结果类型: ${outcome}`);
                }

                // 获取玩家手牌和庄家手牌
                let playerCards = [];
                let dealerCards = [];

                // 获取玩家手牌
                if (this.gameInstance && this.gameInstance.players && this.gameInstance.players[playerIndex]) {
                    const player = this.gameInstance.players[playerIndex];
                    if (player.hands && player.hands.length > handIndex) {
                        // 如果是分牌情况，记录所有手牌 -- 这是错误的行为，应该只记录当前 handIndex 的手牌
                        // if (result.isSplit) { // 旧的错误逻辑
                        //     playerCards = player.hands;
                        // } else {
                        //     playerCards = player.hands[handIndex];
                        // }
                        // 修正：总是获取当前 handIndex 对应的手牌
                        playerCards = player.hands[handIndex];
                    }
                }

                // 获取庄家手牌
                if (this.gameInstance && this.gameInstance.dealerHand) {
                    dealerCards = this.gameInstance.dealerHand;
                }

                // 获取玩家手牌和庄家手牌的点数
                let currentPlayerPoints = 0;
                let currentDealerPoints = 0;

                // 尝试从 gameInstance 计算当前手牌的点数
                // 注意: playerCards 可能是一个二维数组（如果是分牌且记录所有手牌的情况）或一维数组
                if (this.gameInstance) {
                    if (Array.isArray(playerCards) && playerCards.length > 0) {
                        // 简化处理：如果playerCards是二维数组（分牌时存储了所有手牌），我们取当前handIndex对应的手牌
                        // 或者如果它本身就是一手牌（一维数组），直接使用它
                        const currentHandToCalc = Array.isArray(playerCards[0]) && playerCards.length > handIndex ? playerCards[handIndex] : playerCards;
                        if (Array.isArray(currentHandToCalc) && currentHandToCalc.length > 0) {
                            try {
                                currentPlayerPoints = this.gameInstance.calculateHandValue(currentHandToCalc).value;
                            } catch (e) {
                                console.warn(`在_recordResults中为玩家 ${playerName} 手牌 ${handIndex} 计算点数失败:`, e);
                                currentPlayerPoints = 0; // 计算失败则为0
                            }
                        }
                    }
                    if (Array.isArray(dealerCards) && dealerCards.length > 0) {
                        try {
                            currentDealerPoints = this.gameInstance.calculateHandValue(dealerCards).value;
                        } catch (e) {
                            console.warn(`在_recordResults中为庄家计算点数失败:`, e);
                            currentDealerPoints = 0; // 计算失败则为0
                        }
                    }
                }

                // 记录下注历史
                this.result.betHistory.push({
                    playerIndex: playerIndex,
                    playerName: playerName,
                    handIndex: handIndex,
                    bet: bet,
                    originalBet: originalBet,
                    result: outcome,
                    profit: profit,
                    game: this.result.completedGames + 1,
                    timestamp: Date.now(),
                    playerPoints: currentPlayerPoints, // 记录计算得到的玩家点数
                    dealerPoints: currentDealerPoints, // 记录计算得到的庄家点数
                    // 确保手牌信息被正确复制和简化
                    playerCards: Array.isArray(playerCards) ? playerCards.map(c => (typeof c === 'string' ? c : { rank: c.rank, suit: c.suit })) : [],
                    dealerCards: Array.isArray(dealerCards) ? dealerCards.map(c => (typeof c === 'string' ? c : { rank: c.rank, suit: c.suit })) : [],
                    trueCount: this.gameInstance.trueCount || 0,
                    isDoubleDown: result.isDoubleDown || false,
                    isSplit: result.isSplit || false
                });

                // 更新玩家统计
                if (outcome === 'win' || outcome === 'blackjack') {
                    this.result.playerStats.wins++;
                    this.result.playerStats.totalWinAmount += profit;
                    this.result.playerStats.largestWin = Math.max(this.result.playerStats.largestWin, profit);
                    this.result.playerStats.currentWinStreak++;
                    this.result.playerStats.currentLoseStreak = 0;
                } else if (outcome === 'lose' || outcome === 'bust') {
                    this.result.playerStats.losses++;
                    this.result.playerStats.totalLossAmount += Math.abs(profit);
                    this.result.playerStats.largestLoss = Math.max(this.result.playerStats.largestLoss, Math.abs(profit));
                    this.result.playerStats.currentLoseStreak++;
                    this.result.playerStats.currentWinStreak = 0;
                } else if (outcome === 'push') {
                    this.result.playerStats.pushes++;
                } else if (outcome === 'surrender') {
                    // 投降计入投降次数和亏损金额
                    this.result.playerStats.surrenders++;
                    this.result.playerStats.totalLossAmount += Math.abs(profit);
                    // 投降也算作一种亏损，但不计入连败
                    this.result.playerStats.losses++;
                    this.result.playerStats.currentLoseStreak++;
                    this.result.playerStats.currentWinStreak = 0;
                }

                // 更新连胜/连败纪录
                this.result.playerStats.longestWinStreak = Math.max(this.result.playerStats.longestWinStreak, this.result.playerStats.currentWinStreak);
                this.result.playerStats.longestLoseStreak = Math.max(this.result.playerStats.longestLoseStreak, this.result.playerStats.currentLoseStreak);

                // 更新总下注金额
                this.result.playerStats.totalBetAmount += bet;

                // 增加已玩手牌数
                this.result.playerStats.handsPlayed++;

                // 如果是加倍获胜，记录加倍成功
                if ((outcome === 'win' || outcome === 'blackjack') && result.isDoubleDown) {
                    this.result.doubleWinCount++;
                }

                // 如果是加倍，记录加倍次数
                if (result.isDoubleDown) {
                    // 注意：doubleCount 应该在实际执行加倍动作时累加，而不是在结算时。
                    // 但如果之前的逻辑是这样，暂时保留，或确认是否也需要移到动作发生时。
                    // 查证：doubleCount 在 _playPlayerTurns/playHand 的 'D' case 中已经累加，此处不应再加。
                    // this.result.doubleCount++; // 移除或确认是否重复
                    console.log(`记录加倍结果: 玩家=${playerName}, 手牌=${handIndex}, 下注=${bet}, 原始下注=${originalBet}`);
                }

                // 如果是分牌，记录分牌次数 -- 这里是错误计数的来源
                // if (result.isSplit) { // <--- 移除此段逻辑
                //     this.result.splitCount++;
                //     console.log(`记录分牌结果: 玩家=${playerName}, 手牌=${handIndex}, 下注=${bet}, 原始下注=${originalBet}`);
                // }
            });

            console.log('结算结果记录完成');
        } catch (error) {
            console.error('记录结算结果时出错:', error);
        }
    }

    /**
     * 准备下一局游戏
     * @private
     */
    _prepareNextGame() {
        // 如果游戏本体有准备下一局的方法，直接调用
        if (this.gameInstance.prepareNextGame && typeof this.gameInstance.prepareNextGame === 'function') {
            this.gameInstance.prepareNextGame();
            return;
        }

        // 清理所有玩家手牌
        for (let i = 0; i < this.gameInstance.players.length; i++) {
            const player = this.gameInstance.players[i];
            player.hands = [[]]; // 重置为一个空手牌
            player.currentBet = 0; // 重置下注
            player.originalBet = 0; // 重置原始下注
            player.betStatus = 'waiting'; // 重置下注状态
        }

        // 清理庄家手牌
        this.gameInstance.dealerHand = [];

        // 设置游戏状态为下注阶段
        this.gameInstance.gameState = 'betting';
    }

    /**
     * 启动模拟
     * @returns {Promise} 包含模拟结果的Promise
     */
    start() {
        if (this.isRunning) {
            console.warn('模拟已在运行中');
            return Promise.resolve(this.result);
        }

        console.info('开始模拟...');

        // 初始化游戏实例
        this._initializeGameInstance();

        // 重置结果
        this.result.completedGames = 0;

        // 确保初始筹码曲线正确设置
        const initialChipsArray = Array(this.config.playerCount).fill(this.config.startingChips);
        this.result.chipsCurve = [{
            game: 0,
            chips: initialChipsArray
        }];

        // 记录初始筹码曲线
        console.log('初始化筹码曲线:', {
            playerCount: this.config.playerCount,
            startingChips: this.config.startingChips,
            initialChipsArray: initialChipsArray,
            chipsCurve: this.result.chipsCurve
        });

        // 设置状态
        this.isRunning = true;
        this.isPaused = false;
        this.startTime = Date.now();
        this.currentBatch = 0;

        // 返回Promise，在模拟完成时解析
        return new Promise((resolve) => {
            // 开始第一批模拟
            this._processBatch().then(() => {
                resolve(this.result);
            });
        });
    }

    /**
     * 处理一批模拟
     * @private
     * @returns {Promise} 批处理完成的Promise
     */
    _processBatch() {
        return new Promise((resolve) => {
            // 如果已暂停或停止，则返回
            if (this.isPaused || !this.isRunning) {
                resolve();
                return;
            }

            const batchSize = this.config.batchSize;
            const totalGames = this.config.numberOfGames;
            const remainingGames = totalGames - this.result.completedGames;
            const gamesToProcess = Math.min(batchSize, remainingGames);

            // 如果没有剩余游戏，则完成模拟
            if (gamesToProcess <= 0) {
                this._completeSimulation();
                resolve();
                return;
            }

            console.info(`处理批次 ${this.currentBatch + 1}，模拟 ${gamesToProcess} 局游戏...`);

            // 处理这一批的游戏
            for (let i = 0; i < gamesToProcess; i++) {
                this._simulateOneGame();
            }

            // 更新进度
            if (this.progressCallback) {
                const progress = this.result.completedGames / totalGames;
                const elapsedMs = Date.now() - this.startTime;
                const gamesPerSecond = this.result.completedGames / (elapsedMs / 1000);
                const estimatedTotalMs = (totalGames / gamesPerSecond) * 1000;
                const remainingMs = Math.max(0, estimatedTotalMs - elapsedMs);

                this.progressCallback({
                    completedGames: this.result.completedGames,
                    totalGames: totalGames,
                    progress: progress,
                    elapsedMs: elapsedMs,
                    remainingMs: remainingMs,
                    gamesPerSecond: gamesPerSecond
                });
            }

            // 增加批次计数
            this.currentBatch++;

            // 避免阻塞UI，使用setTimeout继续下一批
            this.batchTimerId = setTimeout(() => {
                this._processBatch().then(resolve);
            }, 0);
        });
    }

    /**
     * 模拟一局游戏
     * @private
     */
    _simulateOneGame() {
        try {
            // 记录开始时间（用于性能分析）
            const startTime = performance.now();

            // 记录游戏前的筹码总额（用于计算本局盈亏）
            let beforeGameChips = 0;

            // 确保游戏实例和玩家存在
            if (!this.gameInstance || !this.gameInstance.players || this.gameInstance.players.length === 0) {
                console.error('游戏实例或玩家数据不完整，重新初始化');
                this._initializeGameInstance();
            }

            // 计算游戏前的筹码总额
            if (this.gameInstance && this.gameInstance.players) {
                beforeGameChips = this.gameInstance.players.reduce((sum, player) => {
                    return sum + (player && typeof player.chips === 'number' ? player.chips : 0);
                }, 0);
            }

            // 记录当前游戏编号
            const gameNumber = this.result.completedGames + 1;
            console.log(`\n========================================`);
            console.log(`开始模拟第 ${gameNumber} 局游戏，当前总筹码: ${beforeGameChips}`);

            // 记录玩家初始状态
            if (this.gameInstance && this.gameInstance.players) {
                this.gameInstance.players.forEach((player, index) => {
                    console.log(`玩家${index+1} 初始状态: 筹码=${player.chips}, 手牌数=${player.hands ? player.hands.length : 0}`);
                });
            }

            // 确保游戏状态正确
            this.gameInstance.gameState = 'betting';

            // 检查是否需要洗牌（使用游戏本体的方法）
            if (this.gameInstance.deck.shouldShuffleByPenetration()) {
                console.log(`[模拟] 根据渗透率(${this.gameInstance.deck.getPenetrationRate() * 100}%)触发洗牌`);
                this.gameInstance.deck.shuffle();
                // 重置计数
                this.gameInstance.runningCount = 0;
            }

            // 计算真数
            let trueCount = 0;
            try {
                // 使用算牌系统计算真数
                if (this.cardCountingSystem && typeof this.cardCountingSystem.getTrueCount === 'function') {
                    // 使用算牌系统的方法获取真数
                    trueCount = this.cardCountingSystem.getTrueCount();
                } else {
                    // 直接计算真数
                    const runningCount = this.gameInstance.runningCount || 0;

                    // 计算剩余牌组数 = 剩余牌数 / 52
                    const remainingCards = this.gameInstance.deck.getRemainingCards() || 52;
                    const remainingDecks = remainingCards / 52;

                    // 计算真数 = 运行计数 / 剩余牌组数
                    trueCount = remainingDecks > 0 ? runningCount / remainingDecks : 0;
                }

                // 确保真数是有效数字
                if (isNaN(trueCount) || !isFinite(trueCount)) {
                    console.warn(`获取到无效真数，使用默认值0`);
                    trueCount = 0;
                }

                // 限制真数的范围，避免极端值
                if (Math.abs(trueCount) > 10) {
                    console.warn(`真数 ${trueCount} 超出合理范围，限制为 ${trueCount > 0 ? 10 : -10}`);
                    trueCount = trueCount > 0 ? 10 : -10;
                }

                // 保留两位小数
                trueCount = Math.round(trueCount * 100) / 100;
            } catch (error) {
                console.error('获取真数时出错:', error);
                trueCount = 0;
            }

            // 记录真数计算详情
            const runningCount = this.gameInstance.runningCount || 0;
            const remainingCards = this.gameInstance.deck.getRemainingCards() || 52;
            const remainingDecks = remainingCards / 52;
            console.log(`真数计算: 运行计数=${runningCount}, 剩余牌数=${remainingCards}, 剩余牌组=${remainingDecks.toFixed(2)}, 真数=${trueCount}, 算牌系统=${this.config.countingSystem}`);

            // 记录真数分布
            const trueCountKey = Math.round(trueCount).toString();
            if (!this.result.trueCountDistribution[trueCountKey]) {
                this.result.trueCountDistribution[trueCountKey] = 0;
            }
            this.result.trueCountDistribution[trueCountKey]++;
            console.log(`当前真数: ${trueCount}`);

            // 将真数保存到游戏实例中，以便在结算时使用
            this.gameInstance.trueCount = trueCount;

            // 1. 下注阶段 - 直接使用游戏本体的自动下注逻辑
            console.log('开始下注阶段...');
            this._placeBets(trueCount);

            // 记录下注情况
            if (this.gameInstance && this.gameInstance.players) {
                const betsInfo = this.gameInstance.players.map((player, index) => {
                    const bet = typeof player.currentBet === 'number' ? player.currentBet : 0;
                    return `玩家${index + 1}: ${bet}`;
                }).join(', ');
                console.log(`下注情况: ${betsInfo}`);
            }

            // 2. 发牌阶段 - 使用游戏本体的发牌逻辑
            console.log('开始发牌阶段...');
            this._dealInitialCards();

            // 记录初始手牌
            if (this.gameInstance && this.gameInstance.players) {
                console.log('初始手牌:');
                this.gameInstance.players.forEach((player, index) => {
                    if (player && player.hands && player.hands.length > 0) {
                        console.log(`玩家${index + 1}: ${JSON.stringify(player.hands[0])}`);
                    }
                });
                console.log(`庄家: ${JSON.stringify(this.gameInstance.dealerHand)}`);
            }

            // 设置游戏状态为playing
            this.gameInstance.gameState = 'playing';

            // 3. 玩家回合 - 使用游戏本体的自动策略
            console.log('开始玩家回合...');
            this._playPlayerTurns();

            // 记录玩家回合后的手牌
            if (this.gameInstance && this.gameInstance.players) {
                console.log('玩家回合结束后手牌:');
                this.gameInstance.players.forEach((player, index) => {
                    if (player && player.hands) {
                        player.hands.forEach((hand, handIndex) => {
                            console.log(`玩家${index + 1} 手牌${handIndex + 1}: ${JSON.stringify(hand)}`);
                        });
                    }
                });
            }

            // 设置游戏状态为dealer
            this.gameInstance.gameState = 'dealer';

            // 4. 庄家回合 - 使用游戏本体的庄家逻辑
            console.log('开始庄家回合...');
            this._playDealerTurn();

            // 记录庄家回合后的手牌
            console.log(`庄家最终手牌: ${JSON.stringify(this.gameInstance.dealerHand)}`);

            // 设置游戏状态为ended
            this.gameInstance.gameState = 'ended';

            // 5. 结算阶段 - 使用游戏本体的结算逻辑
            console.log('开始结算阶段...');

            // 在结算前验证所有分牌手牌的下注金额
            if (this.gameInstance && this.gameInstance.players) {
                for (const player of this.gameInstance.players) {
                    if (player && player.hands && player.hands.length > 0) {
                        const playerIndex = this.gameInstance.players.indexOf(player);
                        const handCount = player.hands.length;

                        // 检测是否有分牌情况
                        const hasSplitHands = handCount > 1;
                        if (hasSplitHands) {
                            console.log(`[结算前验证] 检测到玩家${playerIndex+1}有${handCount}手牌，验证下注金额`);
                        }

                        // 确保bets和initialBets数组存在且长度正确
                        if (!Array.isArray(player.bets)) {
                            console.warn(`[结算前验证] 玩家${playerIndex+1}的bets不是数组，创建新数组`);
                            player.bets = Array(handCount).fill(player.currentBet || 100);
                        } else if (player.bets.length !== handCount) {
                            // 不再输出警告，因为这是正常的调整过程
                            // 调整数组长度
                            while (player.bets.length < handCount) {
                                // 找一个有效的下注金额
                                const validBet = player.bets.find(bet => bet > 0) || 100;
                                player.bets.push(validBet);
                            }
                            // 如果数组过长，截断
                            if (player.bets.length > handCount) {
                                player.bets = player.bets.slice(0, handCount);
                            }
                        }

                        // 确保initialBets数组存在且长度正确
                        if (!Array.isArray(player.initialBets)) {
                            console.warn(`[结算前验证] 玩家${playerIndex+1}的initialBets不是数组，创建新数组`);
                            player.initialBets = Array(handCount).fill(0);
                            // 从bets数组复制值
                            for (let i = 0; i < handCount; i++) {
                                // 对于加倍的手牌，初始下注是当前下注的一半
                                player.initialBets[i] = player.hands[i].doubled ? Math.floor(player.bets[i] / 2) : player.bets[i];
                            }
                        } else if (player.initialBets.length !== handCount) {
                            // 不再输出警告，因为这是正常的调整过程
                            // 调整数组长度
                            while (player.initialBets.length < handCount) {
                                const index = player.initialBets.length;
                                // 对于加倍的手牌，初始下注是当前下注的一半
                                const initialBet = player.hands[index] && player.hands[index].doubled ?
                                    Math.floor(player.bets[index] / 2) : player.bets[index];
                                player.initialBets.push(initialBet);
                            }
                            // 如果数组过长，截断
                            if (player.initialBets.length > handCount) {
                                player.initialBets = player.initialBets.slice(0, handCount);
                            }
                        }

                        // 验证每手牌的下注金额
                        for (let i = 0; i < handCount; i++) {
                            const hand = player.hands[i];

                            // 检查下注金额是否有效
                            if (player.bets[i] <= 0 || isNaN(player.bets[i])) {
                                // 尝试修复无效下注
                                let fixedBet = 0;

                                // 首先尝试从initialBets获取
                                if (player.initialBets[i] > 0) {
                                    fixedBet = hand.doubled ? player.initialBets[i] * 2 : player.initialBets[i];
                                }
                                // 然后尝试从其他手牌获取
                                else if (player.bets.some(bet => bet > 0)) {
                                    const validBet = player.bets.find(bet => bet > 0);
                                    fixedBet = validBet;
                                }
                                // 最后使用默认值
                                else {
                                    fixedBet = 100;
                                }

                                console.warn(`[结算前验证] 修正玩家${playerIndex+1}手牌${i+1}的下注金额: 从${player.bets[i]}到${fixedBet}`);
                                player.bets[i] = fixedBet;
                            }

                            // 检查初始下注金额是否有效
                            if (player.initialBets[i] <= 0 || isNaN(player.initialBets[i])) {
                                // 对于加倍的手牌，初始下注是当前下注的一半
                                const fixedInitialBet = hand.doubled ? Math.floor(player.bets[i] / 2) : player.bets[i];

                                console.warn(`[结算前验证] 修正玩家${playerIndex+1}手牌${i+1}的初始下注金额: 从${player.initialBets[i]}到${fixedInitialBet}`);
                                player.initialBets[i] = fixedInitialBet;
                            }

                            // 记录验证结果
                            console.log(`[结算前验证] 玩家${playerIndex+1}手牌${i+1}: 下注=${player.bets[i]}, 初始下注=${player.initialBets[i]}, 加倍=${hand.doubled ? '是' : '否'}, 分牌=${hand.split ? '是' : '否'}`);
                        }
                    }
                }
            }

            // 检查游戏本体是否有settleGame方法
            if (this.gameInstance.settleGame && typeof this.gameInstance.settleGame === 'function') {
                console.log('使用游戏本体的settleGame方法');
                const results = this.gameInstance.settleGame();
                if (results && Array.isArray(results)) {
                    // 检查分牌情况下结果数量是否正确
                    let hasSplitHands = false;
                    let expectedResults = 0;

                    for (const player of this.gameInstance.players) {
                        if (player && player.hands && player.hands.length > 1) {
                            hasSplitHands = true;
                            // 计算应该有多少条结果记录
                            for (let i = 0; i < player.hands.length; i++) {
                                if (Array.isArray(player.bets) && player.bets[i] > 0) {
                                    expectedResults++;
                                }
                            }
                        }
                    }

                    // 检查结果数量是否正确
                    if (hasSplitHands) {
                        console.log(`[结算验证] 分牌情况下结果记录: 预期${expectedResults}条，实际${results.length}条`);

                        // 检查每个玩家的每手牌是否都有对应的结果记录
                        let missingResults = false;
                        let duplicateResults = false;

                        for (const player of this.gameInstance.players) {
                            if (player && player.hands && player.hands.length > 1) {
                                const playerIndex = this.gameInstance.players.indexOf(player);

                                // 检查每手牌的结果
                                for (let handIndex = 0; handIndex < player.hands.length; handIndex++) {
                                    // 查找当前手牌的结果记录
                                    const handResults = results.filter(r =>
                                        r.playerIndex === playerIndex && r.handIndex === handIndex);

                                    if (handResults.length === 0 && player.bets[handIndex] > 0) {
                                        console.warn(`[结算验证] 玩家${playerIndex+1}的手牌${handIndex+1}没有结果记录`);
                                        missingResults = true;
                                    } else if (handResults.length > 1) {
                                        console.warn(`[结算验证] 玩家${playerIndex+1}的手牌${handIndex+1}有${handResults.length}条结果记录，可能重复`);
                                        duplicateResults = true;
                                    }
                                }
                            }
                        }

                        // 如果有缺失或重复的结果记录，尝试修复
                        if (missingResults || duplicateResults || results.length !== expectedResults) {
                            console.warn(`[结算验证] 检测到分牌结果异常，尝试修复`);
                            const fixedResults = this._fixSplitHandResults(results);
                            this._recordResults(fixedResults);
                        } else {
                            console.log(`[结算验证] 分牌结果记录正常，无需修复`);
                            this._recordResults(results);
                        }
                    } else {
                        this._recordResults(results);
                    }
                } else {
                    console.warn('游戏本体的settleGame方法没有返回有效结果，使用自定义结算逻辑');
                    this._settleGame();
                }
            } else {
                console.log('使用自定义结算逻辑');
                this._settleGame();
            }

            // 增加已完成局数
            this.result.completedGames++;

            // 计算游戏后的筹码总额和本局盈亏
            let afterGameChips = 0;
            if (this.gameInstance && this.gameInstance.players) {
                afterGameChips = this.gameInstance.players.reduce((sum, player) => {
                    return sum + (player && typeof player.chips === 'number' ? player.chips : 0);
                }, 0);
            }

            const gameProfit = afterGameChips - beforeGameChips;
            console.log(`\n游戏结束，当前总筹码: ${afterGameChips}, 本局盈亏: ${gameProfit}`);

            // 记录每个玩家的详细结果
            if (this.gameInstance && this.gameInstance.players) {
                console.log(`\n玩家详细结果:`);
                this.gameInstance.players.forEach((player, index) => {
                    const playerProfit = player.chips - (player.initialChips || this.config.startingChips);
                    console.log(`玩家${index+1}: 筹码=${player.chips}, 盈亏=${playerProfit}, 手牌数=${player.hands ? player.hands.length : 0}`);

                    // 记录每手牌的结果
                    if (player.hands && player.hands.length > 0) {
                        player.hands.forEach((hand, handIndex) => {
                            const handValue = this.gameInstance.calculateHandValue(hand);
                            const betAmount = Array.isArray(player.bets) ? player.bets[handIndex] : player.currentBet;
                            const originalBet = Array.isArray(player.initialBets) ? player.initialBets[handIndex] : player.originalBet;

                            console.log(`  手牌${handIndex+1}: 牌=${hand.map(c => c.rank + c.suit).join(', ')}, 点数=${handValue.value}, 下注=${betAmount}, 原始下注=${originalBet}, 加倍=${hand.doubled ? '是' : '否'}, 分牌=${hand.split ? '是' : '否'}, 投降=${hand.surrendered ? '是' : '否'}`);
                        });
                    }
                });
            }

            console.log(`========================================\n`);

            // 更新总盈亏
            this.result.netProfit = afterGameChips - this.result.initialTotalChips;

            // 记录筹码曲线
            try {
                if (this.gameInstance && this.gameInstance.players) {
                    // 确保只记录有效的筹码值
                    const chipsArray = this.gameInstance.players.map(p => {
                        if (p && typeof p.chips === 'number' && !isNaN(p.chips)) {
                            return p.chips;
                        }
                        return this.config.startingChips; // 如果无效，使用初始筹码
                    });

                    // 计算总筹码
                    const totalChips = chipsArray.reduce((sum, chips) => sum + chips, 0);

                    // 更新最终筹码
                    this.result.finalChips = totalChips;

                    // 更新净盈亏
                    this.result.netProfit = totalChips - this.result.initialTotalChips;

                    // 更新每局平均盈亏
                    if (this.result.completedGames > 0) {
                        this.result.profitPerGame = this.result.netProfit / this.result.completedGames;
                    }

                    // 添加到筹码曲线
                    this.result.chipsCurve.push({
                        game: this.result.completedGames,
                        chips: chipsArray,
                        totalChips: totalChips,
                        netProfit: this.result.netProfit
                    });

                    // 每10局记录一次筹码变化，避免日志过多
                    if (this.result.completedGames % 10 === 0 || this.result.completedGames === 1) {
                        console.log(`游戏 #${this.result.completedGames} 筹码状态: ${totalChips}, 总盈亏: ${this.result.netProfit}, 每局平均盈亏: ${this.result.profitPerGame.toFixed(2)}`);
                    }

                    // 更新最大回撤
                    this._updateMaxDrawdown(chipsArray);

                    // 计算盈亏百分比
                    if (this.result.initialTotalChips > 0) {
                        this.result.profitPercentage = (this.result.netProfit / this.result.initialTotalChips) * 100;
                    }
                } else {
                    console.warn(`游戏 #${this.result.completedGames} 无法记录筹码曲线：游戏实例或玩家数据不完整`);
                }
            } catch (error) {
                console.error('记录筹码曲线时出错:', error);
            }

            // 准备下一局 - 重置游戏状态
            this._prepareNextGame();

            // 记录性能数据
            const endTime = performance.now();
            const duration = endTime - startTime;
            this._recordPerformanceData(duration);

            // 计算并更新胜率
            this._updateWinRate();

        } catch (error) {
            console.error('模拟一局游戏时出错:', error);
            // 出错时尝试恢复到可用状态
            this._recoverFromError();

            // 仍然增加已完成局数，避免无限循环
            this.result.completedGames++;
        }
    }

    /**
     * 更新最大回撤
     * @param {Array<number>} chipsArray - 当前筹码数组
     * @private
     */
    _updateMaxDrawdown(chipsArray) {
        try {
            // 计算当前总筹码
            const currentTotalChips = chipsArray.reduce((sum, chips) => sum + chips, 0);

            // 如果是第一次计算或当前总筹码创新高，更新最高点
            if (!this._highestChips || currentTotalChips > this._highestChips) {
                this._highestChips = currentTotalChips;
                this._currentDrawdown = 0;

                // 将最高筹码值保存到结果对象中
                if (!this.result.playerStats) {
                    this.result.playerStats = {};
                }
                this.result.playerStats.highestChips = this._highestChips;
                console.log(`更新历史最高筹码: ${this._highestChips}`);
            } else if (currentTotalChips < this._highestChips) {
                // 计算当前回撤
                this._currentDrawdown = this._highestChips - currentTotalChips;

                // 更新最大回撤
                if (this._currentDrawdown > this.result.maxDrawdown) {
                    this.result.maxDrawdown = this._currentDrawdown;
                    console.log(`更新最大回撤: ${this.result.maxDrawdown}, 最高点: ${this._highestChips}, 当前: ${currentTotalChips}`);
                }
            }
        } catch (error) {
            console.error('更新最大回撤时出错:', error);
        }
    }

    /**
     * 更新胜率和其他比率
     * @private
     */
    _updateWinRate() {
        try {
            // 计算总结果数
            const totalResults = this.result.winCount + this.result.loseCount + this.result.pushCount;

            // 计算胜率
            if (totalResults > 0) {
                this.result.winRate = this.result.winCount / totalResults;
            } else {
                this.result.winRate = 0;
            }

            // 计算黑杰克率
            if (this.result.playerStats.handsPlayed > 0) {
                this.result.blackjackRate = this.result.blackjackCount / this.result.playerStats.handsPlayed;
            } else {
                this.result.blackjackRate = 0;
            }

            // 计算加倍成功率
            if (this.result.doubleCount > 0) {
                this.result.doubleSuccessRate = this.result.doubleWinCount / this.result.doubleCount;
            } else {
                this.result.doubleSuccessRate = 0;
            }

            // 计算净盈亏
            if (this.gameInstance && this.gameInstance.players) {
                let currentTotalChips = 0;
                for (const player of this.gameInstance.players) {
                    if (player && typeof player.chips === 'number' && !isNaN(player.chips)) {
                        currentTotalChips += player.chips;
                    }
                }

                // 更新净盈亏
                this.result.netProfit = currentTotalChips - this.result.initialTotalChips;

                // 更新每局平均盈亏
                if (this.result.completedGames > 0) {
                    this.result.profitPerGame = this.result.netProfit / this.result.completedGames;
                }
            }

            // 每10局记录一次统计数据
            if (this.result.completedGames % 10 === 0 || this.result.completedGames === 1) {
                console.log(`统计数据 - 胜: ${this.result.winCount}, 负: ${this.result.loseCount}, 平: ${this.result.pushCount}, 胜率: ${(this.result.winRate * 100).toFixed(2)}%`);
                console.log(`当前净盈亏: ${this.result.netProfit}, 每局平均盈亏: ${this.result.profitPerGame.toFixed(2)}`);
            }
        } catch (error) {
            console.error('更新胜率时出错:', error);
        }
    }

    /**
     * 记录性能数据
     * @param {number} duration - 本局游戏耗时(毫秒)
     * @private
     */
    _recordPerformanceData(duration) {
        // 累计总耗时
        this._totalSimulationTime += duration;

        // 计算平均每局耗时
        const avgTimePerGame = this._totalSimulationTime / this.result.completedGames;

        // 更新每秒模拟局数
        this.result.gamesPerSecond = 1000 / avgTimePerGame;
    }

    /**
     * 从错误中恢复
     * @private
     */
    _recoverFromError() {
        try {
            // 重置所有玩家手牌
            for (let i = 0; i < this.gameInstance.players.length; i++) {
                const player = this.gameInstance.players[i];
                player.hands = [[]]; // 重置为一个空手牌
                player.currentBet = 0; // 重置下注
                player.originalBet = 0; // 重置原始下注
            }
            // 清理庄家手牌
            this.gameInstance.dealerHand = [];

            // 确保游戏状态正确
            this.gameInstance.gameState = 'betting';

            console.log('已从错误中恢复，继续模拟');
        } catch (recoverError) {
            console.error('从错误恢复时出错:', recoverError);
            // 如果恢复也失败，可能需要重新初始化游戏实例
            this._initializeGameInstance();
        }
    }









    /**
     * 完成模拟
     * @private
     */
    _completeSimulation() {
        console.info('模拟完成，计算最终结果...');

        this.isRunning = false;

        try {
            // 计算模拟总时间
            const totalTime = Math.max(1, Date.now() - this.startTime); // 确保至少为1毫秒，避免除以零
            this.result.simulationTime = totalTime;

            const totalTimeSeconds = totalTime / 1000;
            const totalTimeFormatted = totalTimeSeconds >= 60
                ? `${Math.floor(totalTimeSeconds / 60)}分${Math.floor(totalTimeSeconds % 60)}秒`
                : `${totalTimeSeconds.toFixed(1)}秒`;

            console.log(`模拟总耗时: ${totalTimeFormatted}`);

            // 计算每秒模拟局数
            this.result.gamesPerSecond = totalTime > 0 ?
                this.result.completedGames / (totalTime / 1000) : 0;

            console.log(`每秒模拟局数: ${this.result.gamesPerSecond.toFixed(1)}`);

            // 计算最终筹码和总盈亏
            this.result.finalChips = 0;

            // 确保gameInstance和players存在
            if (this.gameInstance && this.gameInstance.players && this.gameInstance.players.length > 0) {
                // 收集每个玩家的最终筹码
                const playerChips = [];

                for (const player of this.gameInstance.players) {
                    if (player && typeof player.chips === 'number' && !isNaN(player.chips)) {
                        this.result.finalChips += player.chips;
                        playerChips.push(player.chips);
                    } else {
                        playerChips.push(this.config.startingChips);
                    }
                }

                console.log(`玩家最终筹码: [${playerChips.join(', ')}], 总计: ${this.result.finalChips}`);
            } else {
                console.warn('无法计算最终筹码：游戏实例或玩家数据不完整');
                this.result.finalChips = this.result.initialTotalChips || 0; // 使用初始值避免NaN
            }

            // 确保初始总筹码是有效数字
            if (isNaN(this.result.initialTotalChips) || this.result.initialTotalChips <= 0) {
                console.warn(`初始总筹码无效 (${this.result.initialTotalChips})，使用默认值`);
                this.result.initialTotalChips = this.config.startingChips * this.config.playerCount;
            }

            console.log(`初始总筹码: ${this.result.initialTotalChips}`);

            // 计算净盈亏
            this.result.netProfit = this.result.finalChips - this.result.initialTotalChips;
            console.log(`总盈亏: ${this.result.netProfit}`);

            // 计算每局平均盈亏
            this.result.profitPerGame = this.result.completedGames > 0 ?
                this.result.netProfit / this.result.completedGames : 0;
            console.log(`每局平均盈亏: ${this.result.profitPerGame.toFixed(2)}`);

            // 计算盈亏百分比
            this.result.profitPercentage = this.result.initialTotalChips > 0 ?
                (this.result.netProfit / this.result.initialTotalChips) * 100 : 0;
            console.log(`盈亏百分比: ${this.result.profitPercentage.toFixed(2)}%`);

            // 计算胜率
            const totalResults = this.result.winCount + this.result.loseCount + this.result.pushCount;
            this.result.winRate = totalResults > 0 ? this.result.winCount / totalResults : 0;
            console.log(`胜率: ${(this.result.winRate * 100).toFixed(2)}%, 胜: ${this.result.winCount}, 负: ${this.result.loseCount}, 平: ${this.result.pushCount}`);

            // 计算黑杰克率
            this.result.blackjackRate = this.result.playerStats.handsPlayed > 0 ?
                this.result.blackjackCount / this.result.playerStats.handsPlayed : 0;
            console.log(`黑杰克率: ${(this.result.blackjackRate * 100).toFixed(2)}%, 黑杰克次数: ${this.result.blackjackCount}`);

            // 计算加倍成功率
            this.result.doubleSuccessRate = this.result.doubleCount > 0 ?
                this.result.doubleWinCount / this.result.doubleCount : 0;
            console.log(`加倍成功率: ${(this.result.doubleSuccessRate * 100).toFixed(2)}%, 加倍次数: ${this.result.doubleCount}, 加倍成功: ${this.result.doubleWinCount}`);

            // 计算分牌次数和投降次数
            console.log(`分牌次数 (global for overview/detailed): ${this.result.splitCount}, 投降次数: ${this.result.surrenderCount}`);

            // 确保分牌次数计算正确 - 使用更精确的方法重新计算
            if (this.result.betHistory && this.result.betHistory.length > 0) {
                // 获取所有分牌记录
                const allSplitRecords = this.result.betHistory.filter(bet =>
                    bet.isSplit || bet.actionType === '分牌' || bet.isSplitAction);

                // 按玩家和游戏局ID分组，确保每个玩家在每局游戏中的分牌操作只计算一次
                const splitsByPlayerAndGame = {};

                allSplitRecords.forEach(bet => {
                    const key = `${bet.playerIndex}-${bet.game || bet.gameId}`;
                    if (!splitsByPlayerAndGame[key]) {
                        splitsByPlayerAndGame[key] = true;
                    }
                });

                // 重新计算分牌次数
                const recalculatedSplitCount = Object.keys(splitsByPlayerAndGame).length;

                // 如果重新计算的分牌次数与当前记录的不同，更新分牌次数
                if (recalculatedSplitCount !== this.result.splitCount) {
                    console.log(`分牌次数不一致，重新计算: ${this.result.splitCount} -> ${recalculatedSplitCount}`);
                    this.result.splitCount = recalculatedSplitCount;
                }
            }

            // 确保 playerStats.splits 反映总的分牌动作次数，供"玩家统计"选项卡使用
            this.result.playerStats.splits = this.result.splitCount;
            console.log(`最终分牌次数: ${this.result.playerStats.splits}`);

            // 确保所有计算结果都是有效数字
            this._validateNumericResults();

            // 确保最高筹码值被保存到结果对象中
            if (this._highestChips) {
                if (!this.result.playerStats) {
                    this.result.playerStats = {};
                }
                this.result.playerStats.highestChips = this._highestChips;
                console.log(`最终确认历史最高筹码: ${this._highestChips}`);
            }

            // 汇总最终结果
            console.log('最终结果汇总:', {
                completedGames: this.result.completedGames,
                simulationTime: `${totalTimeFormatted}`,
                gamesPerSecond: this.result.gamesPerSecond.toFixed(1),
                initialChips: this.result.initialTotalChips,
                finalChips: this.result.finalChips,
                highestChips: this.result.playerStats?.highestChips || this.result.finalChips,
                netProfit: this.result.netProfit,
                profitPercentage: `${this.result.profitPercentage.toFixed(2)}%`,
                profitPerGame: this.result.profitPerGame.toFixed(2),
                winRate: `${(this.result.winRate * 100).toFixed(2)}%`,
                blackjackRate: `${(this.result.blackjackRate * 100).toFixed(2)}%`,
                maxDrawdown: this.result.maxDrawdown,
                maxDrawdownPercentage: this.result.initialTotalChips > 0 ?
                    `${((this.result.maxDrawdown / this.result.initialTotalChips) * 100).toFixed(2)}%` : 'N/A'
            });
        } catch (error) {
            console.error('完成模拟计算时出错:', error);
            // 确保所有结果都有有效值
            this._ensureValidResults();
        }

        // 回调通知完成
        if (this.completeCallback) {
            this.completeCallback(this.result);
        }
    }

    /**
     * 验证所有数值结果，确保没有NaN或无效值
     * @private
     */
    _validateNumericResults() {
        // 检查并修复主要数值结果
        const numericFields = [
            'finalChips', 'netProfit', 'profitPerGame', 'blackjackRate',
            'winRate', 'doubleSuccessRate', 'maxDrawdown', 'gamesPerSecond'
        ];

        for (const field of numericFields) {
            if (isNaN(this.result[field]) || !isFinite(this.result[field])) {
                console.warn(`结果字段 ${field} 的值无效 (${this.result[field]})，设置为0`);
                this.result[field] = 0;
            }
        }

        // 检查并修复玩家统计数据
        if (this.result.playerStats) {
            const statFields = [
                'totalBetAmount', 'totalWinAmount', 'totalLossAmount',
                'largestWin', 'largestLoss', 'longestWinStreak', 'longestLoseStreak'
            ];

            for (const field of statFields) {
                if (isNaN(this.result.playerStats[field]) || !isFinite(this.result.playerStats[field])) {
                    console.warn(`玩家统计字段 ${field} 的值无效 (${this.result.playerStats[field]})，设置为0`);
                    this.result.playerStats[field] = 0;
                }
            }
        }
    }

    /**
     * 确保所有结果都有有效值（出错时的恢复方法）
     * @private
     */
    _ensureValidResults() {
        // 设置基本结果字段的默认值
        this.result.finalChips = this.result.initialTotalChips || (this.config.startingChips * this.config.playerCount);
        this.result.netProfit = 0;
        this.result.profitPerGame = 0;
        this.result.blackjackRate = 0;
        this.result.winRate = 0;
        this.result.doubleSuccessRate = 0;
        this.result.maxDrawdown = 0;
        this.result.gamesPerSecond = 0;

        // 确保玩家统计数据有效
        if (!this.result.playerStats) {
            this.result.playerStats = {
                handsPlayed: 0,
                wins: 0,
                losses: 0,
                pushes: 0,
                blackjacks: 0,
                surrenders: 0,
                doubles: 0,
                doubleWins: 0,
                splits: 0,
                totalBetAmount: 0,
                totalWinAmount: 0,
                totalLossAmount: 0,
                largestWin: 0,
                largestLoss: 0,
                longestWinStreak: 0,
                longestLoseStreak: 0,
                currentWinStreak: 0,
                currentLoseStreak: 0
            };
        }

        console.warn('已恢复默认结果值');
    }

    /**
     * 暂停模拟
     */
    pause() {
        if (!this.isRunning || this.isPaused) return;

        console.info('暂停模拟');
        this.isPaused = true;

        // 清除批处理定时器
        if (this.batchTimerId) {
            clearTimeout(this.batchTimerId);
            this.batchTimerId = null;
        }
    }

    /**
     * 恢复模拟
     * @returns {Promise} 模拟完成的Promise
     */
    resume() {
        if (!this.isRunning || !this.isPaused) {
            return Promise.resolve(this.result);
        }

        console.info('恢复模拟');
        this.isPaused = false;

        // 返回Promise，在模拟完成时解析
        return new Promise((resolve) => {
            // 继续处理批次
            this._processBatch().then(() => {
                resolve(this.result);
            });
        });
    }

    /**
     * 停止模拟
     */
    stop() {
        if (!this.isRunning) return;

        console.info('停止模拟');

        // 清除批处理定时器
        if (this.batchTimerId) {
            clearTimeout(this.batchTimerId);
            this.batchTimerId = null;
        }

        // 完成当前模拟，使用已有结果
        this._completeSimulation();
    }
}

// 导出到全局命名空间
window.SimulationEngine = SimulationEngine;
window.SimulationConfig = SimulationConfig;
window.SimulationResult = SimulationResult;