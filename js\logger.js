/**
 * 优化的日志系统
 * 提供不同级别的日志输出，可以在生产环境中轻松禁用调试日志
 */
(function() {
    // 日志级别定义
    const LogLevel = {
        ERROR: 0,   // 只显示错误
        WARN: 1,    // 显示警告和错误
        INFO: 2,    // 显示信息、警告和错误
        DEBUG: 3,   // 显示所有日志，包括调试信息
        TRACE: 4    // 显示所有日志，包括跟踪信息
    };

    // 默认配置
    const config = {
        level: LogLevel.ERROR,     // 默认日志级别设置为ERROR，只显示错误信息
        enabled: true,            // 是否启用日志
        showTimestamp: false,     // 是否显示时间戳
        groupByContext: false,    // 是否按上下文分组
        maxEntries: 1000,         // 内存中保留的最大日志条目数
        persistLogs: false        // 是否持久化日志
    };

    // 内存日志存储
    const memoryLogs = [];

    // 格式化日志消息
    function formatMessage(level, context, message, data) {
        let formattedMessage = '';

        // 添加时间戳
        if (config.showTimestamp) {
            const now = new Date();
            formattedMessage += `[${now.toISOString()}] `;
        }

        // 添加级别和上下文
        formattedMessage += `[${level}]${context ? ` [${context}]` : ''}: `;

        // 添加消息
        formattedMessage += message;

        return formattedMessage;
    }

    // 记录日志到内存
    function logToMemory(level, message) {
        if (memoryLogs.length >= config.maxEntries) {
            memoryLogs.shift(); // 移除最旧的日志
        }
        memoryLogs.push({
            timestamp: new Date(),
            level,
            message
        });
    }

    // 创建日志记录器
    function createLogger(context = '') {
        return {
            error: function(message, ...data) {
                if (!config.enabled || config.level < LogLevel.ERROR) return;

                const formattedMessage = formatMessage('ERROR', context, message, data);
                console.error(formattedMessage, ...data);
                logToMemory('ERROR', formattedMessage);
            },

            warn: function(message, ...data) {
                if (!config.enabled || config.level < LogLevel.WARN) return;

                const formattedMessage = formatMessage('WARN', context, message, data);
                console.warn(formattedMessage, ...data);
                logToMemory('WARN', formattedMessage);
            },

            info: function(message, ...data) {
                if (!config.enabled || config.level < LogLevel.INFO) return;

                const formattedMessage = formatMessage('INFO', context, message, data);
                console.info(formattedMessage, ...data);
                logToMemory('INFO', formattedMessage);
            },

            debug: function(message, ...data) {
                if (!config.enabled || config.level < LogLevel.DEBUG) return;

                const formattedMessage = formatMessage('DEBUG', context, message, data);
                console.debug(formattedMessage, ...data);
                logToMemory('DEBUG', formattedMessage);
            },

            trace: function(message, ...data) {
                if (!config.enabled || config.level < LogLevel.TRACE) return;

                const formattedMessage = formatMessage('TRACE', context, message, data);
                console.debug(formattedMessage, ...data);
                logToMemory('TRACE', formattedMessage);
            },

            group: function(label) {
                if (!config.enabled || !config.groupByContext) return;
                console.group(label || context);
            },

            groupEnd: function() {
                if (!config.enabled || !config.groupByContext) return;
                console.groupEnd();
            }
        };
    }

    // 导出日志系统
    window.Logger = {
        LogLevel,

        // 配置日志系统
        configure: function(options) {
            Object.assign(config, options);
            return this;
        },

        // 创建日志记录器
        getLogger: function(context) {
            return createLogger(context);
        },

        // 获取当前配置
        getConfig: function() {
            return {...config};
        },

        // 获取内存中的日志
        getLogs: function() {
            return [...memoryLogs];
        },

        // 清除内存中的日志
        clearLogs: function() {
            memoryLogs.length = 0;
        },

        // 设置日志级别
        setLevel: function(level) {
            config.level = level;
            return this;
        },

        // 启用/禁用日志
        enable: function(enabled = true) {
            config.enabled = enabled;
            return this;
        }
    };

    // 创建默认日志记录器
    window.log = createLogger('App');

    // 在生产环境中自动设置为仅显示错误
    if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
        config.level = LogLevel.ERROR;
    }
})();
