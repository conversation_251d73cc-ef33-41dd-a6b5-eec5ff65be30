class Deck {
    constructor(numberOfDecks = 6) {
        this.numberOfDecks = numberOfDecks;
        this.cards = [];
        this.usedCards = [];
        this.shuffleCount = 0;  // 洗牌次数（仅传统模式有效）
        this.totalRunningCount = 0;  // 总流水数（仅传统模式有效）
        this.isSnailMode = false; // 是否为蜗牛洗牌模式
        this.totalDealtCards = 0; // 总共发出的牌（蜗牛模式专用）
        this.penetrationRate = 0.65; // 默认渗透率为65%
        this.init();
    }

    // 初始化牌组
    init() {
        const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

        // 清空现有的牌
        this.cards = [];
        this.usedCards = [];
        this.totalDealtCards = 0;

        // 创建新牌
        for (let d = 0; d < this.numberOfDecks; d++) {
            for (let suit of suits) {
                for (let rank of ranks) {
                    this.cards.push(new Card(suit, rank));
                }
            }
        }

        // 检查是否使用蜗牛洗牌
        this.isSnailMode = document.getElementById('snail-shuffle')?.checked || false;

        this.shuffle();
    }

    // 洗牌
    shuffle() {
        // 更新洗牌模式
        this.isSnailMode = document.getElementById('snail-shuffle')?.checked || false;

        // 如果是重新洗牌（不是初始化时的洗牌），且不是蜗牛模式，增加洗牌次数
        if (this.usedCards.length > 0 && !this.isSnailMode) {
            this.shuffleCount++;
        }

        // 将用过的牌放回牌组
        this.cards = [...this.cards, ...this.usedCards];
        this.usedCards = [];

        // 重置流水数（仅传统模式关心此值）
        if (!this.isSnailMode) {
            this.totalRunningCount = 0;
        }

        // 添加防循环标记，避免递归调用
        if (this._isShuffling) {
            console.warn('检测到洗牌递归调用，使用传统洗牌');
            this.traditionalShuffle();
            return;
        }

        this._isShuffling = true;

        try {
            if (this.isSnailMode) {
                this.snailShuffle();
            } else {
                this.traditionalShuffle();
            }
        } finally {
            // 无论成功失败都清除标记
            this._isShuffling = false;
        }

        console.log(`执行${this.isSnailMode ? '蜗牛' : '传统'}洗牌，牌数：${this.cards.length}`);
    }

    // 优化后的传统洗牌（Fisher-Yates 算法）
    traditionalShuffle() {
        // 高效缓存数组长度，避免重复访问
        const length = this.cards.length;

        // 使用临时变量减少数组访问次数
        let temp, j;

        // 一次性遍历实现洗牌
        for (let i = length - 1; i > 0; i--) {
            // 只计算一次随机索引
            j = Math.floor(Math.random() * (i + 1));

            // 使用临时变量交换元素，减少解构赋值的开销
            temp = this.cards[i];
            this.cards[i] = this.cards[j];
            this.cards[j] = temp;
        }

        // 洗牌后立即执行切牌操作，增加随机性
        this.cut();
    }

    // 添加切牌操作，进一步增加随机性
    cut() {
        const length = this.cards.length;
        // 确定随机切点，避免极端位置
        const cutPoint = Math.floor(length * 0.3) + Math.floor(Math.random() * (length * 0.4));

        // 执行切牌
        if (cutPoint > 0 && cutPoint < length) {
            const top = this.cards.slice(0, cutPoint);
            const bottom = this.cards.slice(cutPoint);
            this.cards = [...bottom, ...top];
        }
    }

    // 优化后的蜗牛洗牌算法
    snailShuffle() {
        // 1. 创建临时数组复制原始牌组
        const originalDeck = this.cards;

        // 2. 简化蜗牛洗牌算法，提高性能
        // 使用缓存提高性能
        const originalLength = originalDeck.length;
        const pileCount = Math.min(7, Math.ceil(originalLength / 20)); // 根据牌数确定堆数

        // 预分配数组空间，避免动态扩容
        const piles = new Array(pileCount);
        for (let i = 0; i < pileCount; i++) {
            piles[i] = [];
            // 预估每堆大小
            piles[i].length = Math.ceil(originalLength / pileCount);
            piles[i].length = 0; // 重置实际长度为0，但保留内存空间
        }

        // 高效分配牌到各堆
        let i = 0, currentPile = 0;
        while (i < originalLength) {
            piles[currentPile].push(originalDeck[i]);
            i++;
            currentPile = (currentPile + 1) % pileCount;
        }

        // 3. 每堆内部高效洗牌（使用优化后的Fisher-Yates算法）
        for (let p = 0; p < pileCount; p++) {
            const pile = piles[p];
            const pileLength = pile.length;

            // 使用临时变量减少数组访问
            let temp, j;
            for (let i = pileLength - 1; i > 0; i--) {
                j = Math.floor(Math.random() * (i + 1));
                temp = pile[i];
                pile[i] = pile[j];
                pile[j] = temp;
            }
        }

        // 4. 使用优化算法合并各堆
        // 预分配结果数组
        const shuffledDeck = new Array(originalLength);
        let outputIndex = 0;

        // 跟踪每个牌堆的当前位置
        const pileIndices = new Array(pileCount).fill(0);
        const pileLengths = piles.map(pile => pile.length);

        // 从各堆顶部交替取牌直到所有堆都为空
        let allEmpty = false;
        while (!allEmpty) {
            allEmpty = true;

            for (let p = 0; p < pileCount; p++) {
                if (pileIndices[p] < pileLengths[p]) {
                    shuffledDeck[outputIndex++] = piles[p][pileIndices[p]];
                    pileIndices[p]++;
                    allEmpty = false;
                }
            }
        }

        // 5. 简化验证，只检查数量是否一致
        if (shuffledDeck.length !== originalLength) {
            console.error(`洗牌结果数量错误: 原始 ${originalLength}, 结果 ${shuffledDeck.length}`);
            this.traditionalShuffle(); // 回退到传统洗牌
            return;
        }

        // 直接使用洗牌后的结果
        this.cards = shuffledDeck;

        // 执行切牌，增加随机性
        this.cut();
    }

    // 统计各种牌的数量
    countCardTypes(cards) {
        const counts = {};

        cards.forEach(card => {
            const key = `${card.suit}-${card.rank}`;
            counts[key] = (counts[key] || 0) + 1;
        });

        return counts;
    }

    // 验证牌组中没有重复牌
    verifyNoDuplicates() {
        const cardMap = new Map();
        let hasDuplicate = false;

        this.cards.forEach((card, index) => {
            const cardKey = `${card.suit}-${card.rank}`;
            if (cardMap.has(cardKey)) {
                console.error(`发现重复牌：${cardKey}，位置: ${cardMap.get(cardKey)} 和 ${index}`);
                hasDuplicate = true;
            } else {
                cardMap.set(cardKey, index);
            }
        });

        if (hasDuplicate) {
            console.error('蜗牛洗牌产生了重复牌，回退到传统洗牌');
            this.traditionalShuffle();
        }
    }

    // 发牌
    deal() {
        // 检查是否需要根据渗透率洗牌
        if (this.shouldShuffleByPenetration()) {
            console.log(`根据渗透率(${this.penetrationRate * 100}%)触发洗牌`);
            this.shuffle();
        } else if (this.cards.length === 0) {
            // 如果没有牌了，需要重新洗牌
            this.shuffle();
        } else if (this.isSnailMode && this.usedCards.length >= 20) {
            // 蜗牛洗牌模式：当用过的牌达到一定数量，循环放回牌库
            this.recycleUsedCards();
        }

        const card = this.cards.pop();

        // 确保卡牌默认是明牌
        if (card) {
            card.setHidden(false);
        }

        this.usedCards.push(card);

        // 蜗牛模式：增加总发牌数
        if (this.isSnailMode) {
            this.totalDealtCards++;
        }

        // 更新总流水数（仅对传统模式有意义）
        if (!this.isSnailMode && !card.hidden) {
            this.totalRunningCount += card.getRunningCount();
        }

        return card;
    }

    // 蜗牛洗牌模式：循环使用过的牌
    recycleUsedCards() {
        if (this.usedCards.length === 0) return;

        console.log(`蜗牛洗牌循环: 放回${this.usedCards.length}张牌到牌库`);

        // 取出一批用过的牌
        const cardsToRecycle = this.usedCards.splice(0, this.usedCards.length);

        // 洗一下这批牌
        for (let i = cardsToRecycle.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [cardsToRecycle[i], cardsToRecycle[j]] = [cardsToRecycle[j], cardsToRecycle[i]];
        }

        // 随机插入到当前牌库中
        const insertPoint = Math.floor(Math.random() * (this.cards.length / 2)) + Math.floor(this.cards.length / 4);
        this.cards.splice(insertPoint, 0, ...cardsToRecycle);

        // 不增加洗牌次数，因为这只是循环使用
        console.log(`蜗牛洗牌循环完成，当前牌库数量: ${this.cards.length}`);
    }

    // 获取总牌数
    getTotalCards() {
        return this.numberOfDecks * 52;
    }

    // 获取已使用的牌数（根据模式返回不同的结果）
    getUsedCardsCount() {
        if (this.isSnailMode) {
            return this.totalDealtCards; // 蜗牛模式：返回总共发出的牌数
        } else {
            return this.usedCards.length; // 传统模式：返回当前未被洗回的牌数
        }
    }

    // 获取剩余牌数
    getRemainingCards() {
        return this.cards.length;
    }

    // 计算流水数（如果是蜗牛模式则返回0）
    getRunningCount() {
        return this.isSnailMode ? 0 : this.totalRunningCount;
    }

    // 计算真数（如果是蜗牛模式则返回0）
    getTrueCount() {
        if (this.isSnailMode) {
            return 0;
        }

        const remainingDecks = Math.max(1, this.cards.length / 52);
        return Math.round((this.totalRunningCount / remainingDecks) * 100) / 100;
    }

    // 获取当前算牌系统名称
    getCurrentCountingSystem() {
        if (window.cardCountingSystem) {
            return window.cardCountingSystem.getCurrentSystemName();
        }
        return 'Hi-Lo';
    }

    // 获取洗牌次数（如果是蜗牛模式则返回0）
    getShuffleCount() {
        return this.isSnailMode ? 0 : this.shuffleCount;
    }

    // 重置牌组和统计数据
    reset() {
        this.shuffleCount = 0;
        this.totalRunningCount = 0;
        this.totalDealtCards = 0;
        this.init();
    }

    // 设置渗透率
    setPenetrationRate(rate) {
        // 确保渗透率在有效范围内（0.1-0.9）
        this.penetrationRate = Math.max(0.1, Math.min(0.9, rate));
        console.log(`渗透率设置为: ${this.penetrationRate * 100}%`);
        return this.penetrationRate;
    }

    // 获取渗透率
    getPenetrationRate() {
        return this.penetrationRate;
    }

    // 获取渗透率百分比文本
    getPenetrationRateText() {
        return `${Math.round(this.penetrationRate * 100)}%`;
    }

    // 检查是否应该根据渗透率洗牌
    shouldShuffleByPenetration() {
        if (this.isSnailMode) return false; // 蜗牛模式不使用渗透率

        const totalCards = this.getTotalCards();
        const usedCards = this.getUsedCardsCount();
        const usedPercentage = usedCards / totalCards;

        // 当已使用的牌超过渗透率时，应该洗牌
        return usedPercentage >= this.penetrationRate;
    }
}