/* 下注历史和玩家统计UI样式 */

/* 通用面板样式 */
.history-panel, .player-stats-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;
    background-color: #1f2937;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    border: 1px solid #3b82f6;
    overflow: hidden;
}

/* 面板标题样式 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #2563eb;
    border-bottom: 1px solid #1e40af;
}

.panel-header h3 {
    margin: 0;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

/* 关闭按钮样式 */
.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1;
}

.close-button:hover {
    color: #f87171;
}

/* 内容区域样式 */
.history-content, .stats-content {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(80vh - 60px);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 下拉选择器样式 */
.player-filter, .player-selector {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#player-filter-select, #stats-player-select {
    padding: 8px 10px;
    border-radius: 5px;
    border: 1px solid #4b5563;
    background-color: #374151;
    color: white;
    font-size: 0.9rem;
    width: 200px;
}

/* 表格样式 */
.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    color: #d1d5db;
    table-layout: fixed; /* 固定表格布局 */
    font-size: 1rem;
}

.history-table th, .history-table td {
    padding: 10px 15px;
    text-align: center; /* 居中对齐 */
    border-bottom: 1px solid #4b5563;
    white-space: normal; /* 允许文本换行 */
    overflow: visible; /* 确保内容不被裁剪 */
    height: auto; /* 自动调整行高 */
    min-height: 36px; /* 最小行高 */
    box-sizing: border-box;
}

.history-table th {
    background-color: #374151;
    font-weight: 600;
    color: #e5e7eb;
    height: 42px; /* 增加表头高度 */
}

.history-table tr:nth-child(even) {
    background-color: #283141;
}

.history-table tr:hover {
    background-color: #3b4252;
}

/* 结果颜色样式 */
.result-win, .profit-positive, .positive-value {
    color: #34d399 !important;
}

.result-lose, .profit-negative, .negative-value {
    color: #f87171 !important;
}

.result-push, .neutral-value {
    color: #fbbf24 !important;
}

/* 统计信息样式 */
.history-stats, .stats-grid {
    margin-top: 20px;
    width: 100%;
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    color: #d1d5db;
}

.stats-table th, .stats-table td {
    padding: 8px 12px;
    text-align: center;
    border: 1px solid #4b5563;
}

.stats-table th {
    background-color: #374151;
    font-weight: 600;
    color: #e5e7eb;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat-card {
    background-color: #283141;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid #3b4252;
}

.stat-label {
    font-size: 0.9rem;
    color: #9ca3af;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
}

/* 按钮样式 */
.clear-history-button, .action-button, .inline-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.clear-history-button:hover, .action-button:hover, .inline-button:hover {
    background-color: #2563eb;
}

.inline-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 32px;
}

/* 已存在的功能按钮样式统一 */
#betting-history-btn, #player-stats-btn {
    background-color: #38bdf8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

#betting-history-btn:hover, #player-stats-btn:hover {
    background-color: #0ea5e9;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .history-panel, .player-stats-panel {
        width: 95%;
        max-height: 90vh;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .history-table th, .history-table td {
        padding: 8px 10px;
        font-size: 0.85rem;
    }
}

/* 添加历史记录时间戳的样式 */
.history-timestamp {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: rgba(30, 30, 30, 0.8);
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#history-controls {
    display: flex;
    justify-content: center;
    margin: 10px 0;
    position: relative;
}

#history-controls button {
    margin: 0 5px;
    padding: 5px 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#history-controls button:hover {
    background-color: #45a049;
}

#history-controls button:disabled,
#history-controls button.disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
}