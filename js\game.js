// 创建游戏日志记录器
const gameLogger = window.Logger ? window.Logger.getLogger('Game') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

// 游戏历史记录管理类
class GameHistory {
    constructor() {
        this.records = [];  // 历史记录数组
        this.currentIndex = -1;  // 当前查看的历史记录索引
        this.activeGameState = null;  // 当前进行中的游戏状态
    }

    // 添加历史记录
    addRecord(gameState) {
        this.records.push(gameState);
    }

    // 清除历史记录
    clearHistory() {
        this.records = [];
        this.currentIndex = -1;
        this.activeGameState = null;
    }

    // 保存当前游戏状态
    saveActiveGame(gameState) {
        this.activeGameState = JSON.parse(JSON.stringify(gameState));
    }

    // 获取当前查看的历史记录
    getCurrentRecord() {
        return this.currentIndex >= 0 && this.currentIndex < this.records.length ?
            this.records[this.currentIndex] : null;
    }

    // 获取上一局记录
    getPreviousRecord() {
        // 如果当前不在查看历史记录
        if (this.currentIndex === -1) {
            // 设置为最后一条记录的索引
            this.currentIndex = this.records.length - 1;
            console.log('查看最后一局历史记录，设置索引为:', this.currentIndex);
        }
        // 如果还能往前查看
        else if (this.currentIndex > 0) {
            this.currentIndex--;
            console.log('查看前一局历史记录，设置索引为:', this.currentIndex);
        }

        // 确保currentIndex是有效的
        if (this.currentIndex >= 0 && this.currentIndex < this.records.length) {
            return this.getCurrentRecord();
        }
        return null;
    }

    // 获取下一局记录
    getNextRecord() {
        if (this.currentIndex >= 0 && this.currentIndex < this.records.length - 1) {
            this.currentIndex++;
            console.log('查看下一局历史记录，设置索引为:', this.currentIndex);
        } else if (this.currentIndex === this.records.length - 1) {
            // 已经是最后一条记录，无法继续前进
            console.log('已经是最后一条记录，无法继续前进');
        }

        // 确保currentIndex是有效的
        if (this.currentIndex >= 0 && this.currentIndex < this.records.length) {
            return this.getCurrentRecord();
        }
        return null;
    }

    // 检查是否可以查看上一局
    canViewPrevious() {
        // 如果没有历史记录，不能查看
        if (this.records.length === 0) {
            return false;
        }

        // 如果当前不在查看历史记录，可以查看最后一局
        if (this.currentIndex === -1) {
            return true;
        }

        // 如果正在查看历史记录，只要不是第一局就可以继续往前查看
        return this.currentIndex > 0;
    }

    // 检查是否可以查看下一局
    canViewNext() {
        // 如果没有历史记录，或者不在查看历史记录，不能查看下一局
        if (this.records.length === 0 || this.currentIndex === -1) {
            return false;
        }

        // 如果正在查看历史记录，只要不是最后一局就可以继续往后查看
        return this.currentIndex < this.records.length - 1;
    }

    // 检查是否可以返回当前游戏
    canReturnToActive() {
        return this.currentIndex !== -1 && this.activeGameState !== null;
    }

    // 返回当前游戏状态
    getActiveGameState() {
        return this.activeGameState ? JSON.parse(JSON.stringify(this.activeGameState)) : null;
    }

    // 重置为当前游戏状态
    resetToActive() {
        this.currentIndex = -1;
        return this.getActiveGameState();
    }
}

class Game {
    constructor(numberOfDecks = 8) {
        // 初始化游戏历史记录
        this.gameHistory = new GameHistory();

        // 初始化牌库
        this.deck = new Deck(numberOfDecks);

        // 初始化基本属性
        this.players = [];
        this.dealerHand = [];
        this.currentPlayerIndex = 0;
        this.gameState = 'betting'; // 'betting', 'playing', 'dealer', 'ended'
        this.autoDelay = 600; // 默认自动模式延迟，与UI界面保持一致
        this.dealerCardDelay = 500; // 庄家发牌延迟，默认500毫秒

        // 默认下注相关
        this.defaultBet = 50;
        this.minBet = 10;
        this.maxBet = 500;
        this.currentBet = this.defaultBet;
        this.lastBets = [];  // 保存上一局所有玩家的下注金额

        // 标记是否正在查看历史记录
        this.isViewingHistory = false;

        // 自动暂停标志
        this.isAutoPaused = false;

        // 测试模式标志
        this.isTestMode = false;

        // 初始化玩家和庄家
        this.players = [];
        this.dealerHand = [];

        // 初始化牌堆
        this.deck = new Deck(numberOfDecks);
        this.deck.shuffle();

        // 游戏历史记录
        this.gameHistory = new GameHistory();

        // 其他初始化...
        this._isUpdating = false;  // 是否正在更新UI
        this.lastAutoActionTime = 0;  // 上次自动操作的时间
        this.isTestMode = false;  // 添加测试模式标志

        // 筹码系统相关设置
        this.minBet = 10;    // 最小下注额
        this.maxBet = 50000;   // 最大下注额
        this.defaultBet = 100; // 默认下注额
        this.betStep = 10;   // 下注增减步长
        this.betConfiguration = {
            minBet: 10,
            maxBet: 50000,
            defaultBet: 100,
            betStep: 10
        };
        this.currentBet = this.defaultBet; // 当前选择的下注额
        this.isAutoBetting = false; // 是否开启自动下注
        this.autoBetAmounts = [];   // 各玩家的自动下注金额

        // 后台自动运行相关变量
        this.backgroundAutoInterval = null;  // 后台自动模式计时器
        this.backgroundAutoTimeout = null;   // 后台自动模式超时处理
        this.backgroundStartTime = 0;        // 后台自动模式启动时间
        this.isTabActive = !document.hidden;  // 当前标签是否活跃
        this.inBackgroundMode = false;       // 是否在后台模式
        this.lastHeartbeatTime = Date.now(); // 上次心跳时间
        this.actionsInBackground = 0;        // 后台执行的操作数
        this.lastLogPerformanceTime = 0;     // 上次记录性能的时间
        this.totalOperations = 0;            // 总操作数

        // 自动补充筹码设置
        this.autoRefillChips = {
            enabled: false,      // 是否启用自动补充筹码
            threshold: 1000,     // 筹码阈值，低于此值时自动补充
            amount: 10000        // 补充金额
        };

        // 确保下注控制按钮在初始化时隐藏
        const bettingControls = document.getElementById('betting-controls');
        if (bettingControls) {
            bettingControls.classList.add('hidden');
        }

        // 初始化复选框事件监听
        const allowManagement = document.getElementById('allow-management');
        if (allowManagement) {
            allowManagement.addEventListener('change', () => {
                this.updateButtonStates();
            });
        }

        // 添加自动模式开关监听
        const autoModeSwitch = document.getElementById('auto-mode');
        if (autoModeSwitch) {
            autoModeSwitch.addEventListener('change', (e) => {
                window.isAutoMode = e.target.checked;
                gameLogger.debug('自动模式:', window.isAutoMode ? '开启' : '关闭');

                // 控制暂停/继续按钮的显示状态
                const pauseBtn = document.getElementById('pause-auto');
                if (pauseBtn) {
                    pauseBtn.style.display = window.isAutoMode ? 'inline-block' : 'none';
                    pauseBtn.textContent = '暂停自动'; // 重置按钮文本
                }

                // 重置暂停状态
                this.isAutoPaused = false;

                if (window.isAutoMode && this.gameState === 'playing') {
                    gameLogger.debug('游戏状态为playing，执行自动操作');
                    // 启用自动模式时的首次操作也使用额外延迟
                    const firstActionDelay = 1000;
                    gameLogger.debug(`首次自动操作延迟: ${firstActionDelay}ms (额外延迟以便查看牌面)`);
                    setTimeout(() => this.executeAutoAction(), firstActionDelay);

                    // 启动BackgroundRunner
                    if (window.BackgroundRunner && typeof window.BackgroundRunner.start === 'function') {
                        window.BackgroundRunner.start(() => {
                            if (window.isAutoMode && this.gameState === 'playing' && !this.isAutoPaused) {
                                this.executeAutoAction();
                            }
                        });
                    }
                } else if (!window.isAutoMode) {
                    // 停止BackgroundRunner
                    if (window.BackgroundRunner && typeof window.BackgroundRunner.stop === 'function') {
                        window.BackgroundRunner.stop();
                    }
                    this.stopBackgroundAutoMode();
                }
            });
        }

        // 添加暂停/继续自动模式按钮监听
        const pauseAutoBtn = document.getElementById('pause-auto');
        if (pauseAutoBtn) {
            pauseAutoBtn.addEventListener('click', () => {
                this.toggleAutoPause();
            });
        }

        // 添加策略建议开关监听
        const strategyHintSwitch = document.getElementById('strategy-hint-switch');
        if (strategyHintSwitch) {
            strategyHintSwitch.addEventListener('change', () => {
                this.updateStrategyHint();
            });
        }

        // 添加测试模式开关监听
        const testModeSwitch = document.getElementById('test-mode');
        if (testModeSwitch) {
            testModeSwitch.addEventListener('change', (e) => {
                this.isTestMode = e.target.checked;
                gameLogger.debug('测试模式:', this.isTestMode ? '开启' : '关闭');
                this.updateTestModeUI();
            });
        }

        // 添加庄家软17点停牌开关监听
        const dealerStandSoft17Switch = document.getElementById('dealer-stand-soft17');
        if (dealerStandSoft17Switch) {
            dealerStandSoft17Switch.addEventListener('change', (e) => {
                gameLogger.debug('庄家软17点停牌设置:', e.target.checked ? '开启' : '关闭');
            });
        }

        // 添加页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            const isHidden = document.hidden;
            this.isTabActive = !isHidden;

            gameLogger.debug('页面可见性变化:', isHidden ? '隐藏' : '可见');
            gameLogger.debug('游戏状态:', this.gameState, '自动模式:', window.isAutoMode);

            if (isHidden) {
                // 页面隐藏时，如果自动模式开启且游戏状态为playing
                if (window.isAutoMode && this.gameState === 'playing') {
                    gameLogger.debug('页面隐藏，启动后台自动模式');
                    this.startBackgroundAutoMode();
                }
            } else {
                // 页面可见时，停止后台自动模式
                if (this.inBackgroundMode) {
                    gameLogger.debug('页面可见，停止后台自动模式');
                    this.stopBackgroundAutoMode();

                    // 记录性能数据
                    const elapsedTime = Date.now() - this.backgroundStartTime;
                    if (elapsedTime > 0 && this.actionsInBackground > 0) {
                        const actionsPerMinute = (this.actionsInBackground * 60000) / elapsedTime;
                        gameLogger.debug(`后台模式性能: ${this.actionsInBackground}操作 / ${(elapsedTime/1000).toFixed(1)}秒 = ${actionsPerMinute.toFixed(1)}操作/分钟`);
                    }

                    // 如果游戏仍在进行中且自动模式开启，继续执行自动操作
                    if (window.isAutoMode && this.gameState === 'playing') {
                        gameLogger.debug('恢复自动操作');
                        // 从后台恢复时使用更长延迟，让玩家有时间看清当前状态
                        const resumeDelay = 1000;
                        gameLogger.debug(`恢复自动操作延迟: ${resumeDelay}ms`);
                        setTimeout(() => this.executeAutoAction(), resumeDelay);
                    }
                }
            }
        });

        // 添加窗口焦点变化监听
        window.addEventListener('focus', () => {
            if (window.BackgroundRunner) {
                window.BackgroundRunner.forceActivate();
            }
        });

        window.addEventListener('blur', () => {
            if (window.isAutoMode && this.gameState === 'playing') {
                gameLogger.debug('窗口失去焦点，确保后台自动模式');
                this.startBackgroundAutoMode();
            }
        });

        // 设置全局游戏实例
        window.game = this;

        // 添加一个变量跟踪当前选中的筹码值
        this.selectedChipValue = 0;
    }

    // 开始后台自动模式
    startBackgroundAutoMode() {
        // 如果自动模式没有开启或游戏状态不是playing，不启动后台模式
        if (!window.isAutoMode || this.gameState !== 'playing') {
            gameLogger.debug('不能启动后台模式:', !window.isAutoMode ? '自动模式未开启' : '游戏状态不是playing');
            return;
        }

        // 避免重复启动
        if (this.inBackgroundMode) {
            return;
        }

        // 记录启动时间和状态
        this.backgroundStartTime = Date.now();
        this.inBackgroundMode = true;
        this.actionsInBackground = 0;
        this.lastLogPerformanceTime = Date.now();

        // 使用优化后的BackgroundRunner提供的保活机制
        if (window.BackgroundRunner && typeof window.BackgroundRunner.start === 'function') {
            gameLogger.debug('通过BackgroundRunner启动后台自动模式');

            // 设置回调函数并启动
            window.BackgroundRunner.setCallback(() => {
                if (window.isAutoMode && this.gameState === 'playing' && !this.isAutoPaused) {
                    this.executeAutoAction();
                }
            });

            if (!window.BackgroundRunner.isRunning) {
                window.BackgroundRunner.start();
            } else {
                // 确保BackgroundRunner处于活跃状态
                window.BackgroundRunner.forceActivate();
            }
        }

        // 立即执行一次自动操作
        setTimeout(() => this.executeAutoAction(), 50);

        gameLogger.debug('后台自动模式已启动');
    }

    // 停止后台自动模式
    stopBackgroundAutoMode() {
        // 如果没有在后台模式中，直接返回
        if (!this.inBackgroundMode) {
            return;
        }

        this.inBackgroundMode = false;

        // 输出性能统计
        if (this.actionsInBackground > 0) {
            const elapsedTime = Date.now() - this.backgroundStartTime;
            const actionsPerMinute = (this.actionsInBackground * 60000) / elapsedTime;
            gameLogger.debug(`后台模式性能统计: ${this.actionsInBackground}操作 / ${(elapsedTime/1000).toFixed(1)}秒 = ${actionsPerMinute.toFixed(1)}操作/分钟`);
        }

        gameLogger.debug('后台自动模式已停止');
    }

    // 执行自动操作
    executeAutoAction() {
        // 用于跟踪操作的唯一ID
        const operationId = Date.now() + Math.random().toString(36).substring(2, 5);

        try {
            // 检查是否可以执行自动操作
            if (!window.isAutoMode || this.gameState !== 'playing' || this.isAutoPaused) {
                gameLogger.debug(`[${operationId}] 无法执行自动操作: 自动模式未开启或游戏状态不是playing或已暂停`);
                return;
            }

            // 避免在其他操作过程中执行
            if (this._movingToNext || this._hittingCard || this._standing || this._splitting || this._doubling) {
                gameLogger.debug(`[${operationId}] 无法执行自动操作: 正在执行其他操作`);
                gameLogger.debug(`[${operationId}] 操作状态: 移动=${this._movingToNext}, 要牌=${this._hittingCard}, 停牌=${this._standing}, 分牌=${this._splitting}, 加倍=${this._doubling}`);

                // 如果正在执行其他操作，稍后重试
                if (window.isAutoMode) {
                    gameLogger.debug(`[${operationId}] 将在300毫秒后重试自动操作`);
                    setTimeout(() => this.executeAutoAction(), 300);
                }
                return;
            }

            // 获取当前玩家和手牌
            const currentPlayer = this.getCurrentPlayer();
            const currentHand = this.getCurrentHand();
            if (!currentPlayer || !currentHand) {
                gameLogger.debug(`[${operationId}] 无法执行自动操作: 无法获取当前玩家或手牌`);
                return;
            }

            // 创建状态快照，用于后续验证
            const stateSnapshot = {
                gameState: this.gameState,
                currentPlayerIndex: this.currentPlayerIndex,
                currentHandIndex: currentPlayer.currentHandIndex,
                handLength: currentHand.length,
                handValue: this.calculateHandValue(currentHand).value
            };

            // 记录当前玩家和手牌信息
            gameLogger.debug(`[${operationId}] 当前玩家: ${currentPlayer.name}, 索引: ${this.currentPlayerIndex}, 手牌索引: ${currentPlayer.currentHandIndex}`);
            const handInfo = currentHand.map(card => `${card.rank}${card.suit}`).join(',');
            gameLogger.debug(`[${operationId}] 当前手牌: [${handInfo}], 是否分牌: ${currentHand.split ? '是' : '否'}, 手牌长度: ${currentHand.length}`);

            // 记录自动操作时间和计数
            const now = Date.now();
            this.lastAutoActionTime = now;

            // 在后台模式下增加计数
            if (document.hidden && this.inBackgroundMode) {
                this.actionsInBackground++;

                // 定期输出性能统计
                if (now - this.lastLogPerformanceTime > 30000) {
                    const elapsedTime = now - this.backgroundStartTime;
                    const actionsPerMinute = (this.actionsInBackground * 60000) / elapsedTime;
                    gameLogger.debug(`[${operationId}] 后台模式性能: ${this.actionsInBackground}操作 / ${(elapsedTime/1000).toFixed(1)}秒 = ${actionsPerMinute.toFixed(1)}操作/分钟`);
                    this.lastLogPerformanceTime = now;
                }
            }

            // 检查是否已经爆牌或21点
            const points = this.calculateHandValue(currentHand);
            if (points.value > 21) {
                gameLogger.debug(`[${operationId}] 当前手牌已爆牌(${points.value})，自动移动到下一手`);
                this.moveToNext();
                return;
            }

            if (points.value === 21) {
                gameLogger.debug(`[${operationId}] 当前手牌已达21点，自动停牌`);
                this.stand();
                return;
            }

            // 获取庄家明牌
            const dealerUpcard = this.dealerHand[0];
            if (!dealerUpcard) {
                gameLogger.warn(`[${operationId}] 无法获取庄家明牌，默认执行停牌`);
                this.stand();
                return;
            }

            // 记录庄家明牌信息
            gameLogger.debug(`[${operationId}] 庄家明牌: ${dealerUpcard.rank}${dealerUpcard.suit}, 点数: ${dealerUpcard.getValue()}`);

            // 检查getAutoAction函数是否存在
            if (typeof window.getAutoAction !== 'function') {
                gameLogger.error(`[${operationId}] 策略函数不存在，默认执行停牌`);
                this.stand();
                return;
            }

            // 更新策略提示显示
            this.updateStrategyHint();

            // 添加较长延迟确保UI和状态完全同步
            setTimeout(() => {
                try {
                    // 再次检查游戏状态是否已变化
                    if (this.gameState !== stateSnapshot.gameState ||
                        this.currentPlayerIndex !== stateSnapshot.currentPlayerIndex ||
                        currentPlayer.currentHandIndex !== stateSnapshot.currentHandIndex ||
                        currentHand.length !== stateSnapshot.handLength) {
                        gameLogger.warn(`[${operationId}] 游戏状态已变化，取消自动操作`);
                        gameLogger.debug(`[${operationId}] 状态变化:
                            游戏状态: ${stateSnapshot.gameState} -> ${this.gameState},
                            玩家索引: ${stateSnapshot.currentPlayerIndex} -> ${this.currentPlayerIndex},
                            手牌索引: ${stateSnapshot.currentHandIndex} -> ${currentPlayer.currentHandIndex},
                            手牌长度: ${stateSnapshot.handLength} -> ${currentHand.length}`);
                        return;
                    }

                    // 检查手牌值是否已变化（可能由于其他异步操作）
                    const currentValue = this.calculateHandValue(currentHand).value;
                    if (currentValue !== stateSnapshot.handValue) {
                        gameLogger.warn(`[${operationId}] 手牌值已变化，取消自动操作 (${stateSnapshot.handValue} -> ${currentValue})`);
                        return;
                    }

                    // 直接获取策略建议
                    const action = window.getAutoAction(currentHand, dealerUpcard, this);

                    // 记录策略建议
                    gameLogger.debug(`[${operationId}] 策略建议: ${action}`);

                    // 执行特定动作前再次检查状态
                    const executeAction = (actionFn, actionName) => {
                        gameLogger.debug(`[${operationId}] 执行操作: ${actionName}`);
                        try {
                            actionFn.call(this);
                        } catch (error) {
                            gameLogger.error(`[${operationId}] 执行${actionName}操作时出错:`, error);
                            // 尝试安全恢复
                            setTimeout(() => {
                                if (this.gameState === 'playing') {
                                    this.stand();
                                }
                            }, 300);
                        }
                    };

                    // 根据策略建议执行操作
                    switch (action) {
                        case 'H': // 要牌
                            executeAction(this.hit, '要牌');
                            break;
                        case 'S': // 停牌
                            executeAction(this.stand, '停牌');
                            break;
                        case 'D': // 加倍
                            // 尝试加倍，如果失败则要牌
                            if (currentHand.length === 2 && !currentHand.doubled) {
                                executeAction(this.double, '加倍');
                            } else {
                                gameLogger.debug(`[${operationId}] 无法加倍，改为要牌`);
                                executeAction(this.hit, '要牌(替代加倍)');
                            }
                            break;
                        case 'P': // 分牌
                            // 尝试分牌，如果失败则查询新策略
                            if (currentHand.length === 2 &&
                                currentHand[0].getValue() === currentHand[1].getValue() &&
                                currentPlayer.canSplit()) {
                                executeAction(this.split, '分牌');
                            } else {
                                gameLogger.debug(`[${operationId}] 无法分牌，重新获取策略建议(排除分牌选项)`);
                                // 排除分牌选项重新获取策略
                                const newAction = window.getAutoAction(currentHand, dealerUpcard, this, true);
                                gameLogger.debug(`[${operationId}] 新策略建议: ${newAction}`);

                                // 执行新策略
                                if (newAction === 'H') {
                                    executeAction(this.hit, '要牌(替代分牌)');
                                } else if (newAction === 'S') {
                                    executeAction(this.stand, '停牌(替代分牌)');
                                } else if (newAction === 'D' && currentHand.length === 2 && !currentHand.doubled) {
                                    executeAction(this.double, '加倍(替代分牌)');
                                } else {
                                    // 默认要牌
                                    executeAction(this.hit, '要牌(默认替代分牌)');
                                }
                            }
                            break;
                        case 'R': // 投降
                            // 尝试投降，如果失败则停牌
                            if (currentHand.length === 2 && !currentHand.doubled && !currentHand.split) {
                                executeAction(this.surrender, '投降');
                            } else {
                                gameLogger.debug(`[${operationId}] 无法投降，改为根据点数决定`);
                                // 根据点数决定停牌或要牌
                                if (points.value >= 16) {
                                    executeAction(this.stand, '停牌(替代投降)');
                                } else {
                                    executeAction(this.hit, '要牌(替代投降)');
                                }
                            }
                            break;
                        default:
                            // 默认停牌
                            gameLogger.debug(`[${operationId}] 未知策略 ${action}，默认执行停牌`);
                            executeAction(this.stand, '停牌(默认)');
                            break;
                    }
                } catch (error) {
                    gameLogger.error(`[${operationId}] 执行自动操作时出错:`, error);
                    gameLogger.error(error.stack);

                    // 如果游戏仍在进行中，尝试停牌作为安全操作
                    if (this.gameState === 'playing') {
                        setTimeout(() => this.stand(), 300);
                    }
                }
            }, 100); // 延迟100毫秒，确保UI和状态已完全同步
        } catch (error) {
            gameLogger.error(`[${operationId}] 初始化自动操作时出错:`, error);
            gameLogger.error(error.stack);
        }
    }

    // 执行策略建议 (简化版，仅作为备用)
    executeStrategyAction(action) {
        // 简化的备用方法，仅在需要时使用
        if (!action) return;

        const operationId = Date.now() + Math.random().toString(36).substring(2, 5);
        gameLogger.debug(`[${operationId}] 使用备用策略执行: ${action}`);

        try {
            switch (action) {
                case 'H':
                    this.hit();
                    break;
                case 'S':
                    this.stand();
                    break;
                case 'D':
                    this.double();
                    break;
                case 'P':
                    this.split();
                    break;
                case 'R':
                    this.surrender();
                    break;
                default:
                    this.stand();
                    break;
            }
        } catch (error) {
            gameLogger.error(`[${operationId}] 执行备用策略时出错:`, error);
            gameLogger.error(error.stack);
        }
    }

    // 开始新游戏
    startNewGame(numberOfDecks = 8, resetPlayers = true, resetDeck = false, resetStats = false) {
        gameLogger.debug('开始新游戏，当前下注记录:', this.lastBets);

        // 如果需要重置统计数据，先清除所有玩家的统计
        if (resetStats) {
            this.resetAllStats();
        }

        // 如果不是测试模式，或者测试模式下但没有设置任何手牌，才重置玩家状态
        const hasCustomHands = this.isTestMode && (
            this.dealerHand.length > 0 ||
            this.players.some(p => p.hands.some(h => h.length > 0))
        );

        if (resetPlayers && !hasCustomHands) {
            // 重置玩家状态
            this.players.forEach(player => {
                player.reset();
            });

            // 重置庄家手牌
            this.dealerHand = [];
        } else if (this.isTestMode && hasCustomHands) {
            gameLogger.debug('测试模式：保留自定义设置的手牌');

            // 在测试模式下不重置手牌，但需要重置其他状态
            this.gameState = 'playing';
            this.currentPlayerIndex = 0;
            this.selectedChipValue = 0;

            // 确保所有玩家都有下注，以便能够进行游戏
            this.players.forEach(player => {
                for (let i = 0; i < player.hands.length; i++) {
                    if (player.hands[i].length > 0 && player.bets[i] <= 0) {
                        // 为有牌但没下注的手牌设置默认下注
                        player.bets[i] = 100;
                        gameLogger.debug(`为玩家${player.name}的第${i+1}手牌设置默认下注100`);
                    }
                }
                player.betStatus = 'confirmed';
            });

            // 直接跳过下注阶段，进入游戏阶段
            this.updateUI();
            return;
        }

        // 重置游戏状态
        this.gameState = 'betting';
        this.currentPlayerIndex = 0;
        this.selectedChipValue = 0;

        // 如果需要重置牌堆
        if (resetDeck) {
            this.deck = new Deck(numberOfDecks);
            this.deck.shuffle();
        }

        // 开始下注阶段
        this.startBettingPhase();

        gameLogger.debug('新游戏已开始，保留的下注记录:', this.lastBets);
    }

    // 开始庄家回合
    async startDealerPlay() {
        if (this.gameState !== 'dealer') return;

        // 显示庄家的暗牌
        for (const card of this.dealerHand) {
            card.setHidden(false);
        }

        // 立即更新UI以显示庄家的明牌
        this.updateUI();
        gameLogger.debug('庄家牌全部显示');

        // 等待固定时间再开始庄家操作，无论autoDelay设置如何
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 开始庄家回合
        try {
            await this.dealerPlay();
        } catch (error) {
            console.error('庄家回合出错:', error);
        }
    }

    // 庄家要牌
    async dealerPlay() {
        if (this.gameState !== 'dealer') {
            gameLogger.debug('不是庄家回合，当前状态:', this.gameState);
            return;
        }

        gameLogger.debug('开始庄家回合');
        let dealerPoints = this.calculateHandValue(this.dealerHand);

        // 获取庄家软17点停牌设置
        const standOnSoft17 = document.getElementById('dealer-stand-soft17').checked;

        // 使用设置的庄家发牌延迟时间，不受autoDelay和窗口状态影响
        gameLogger.debug(`庄家发牌延迟: ${this.dealerCardDelay}ms`);

        // 庄家要牌直到17点或以上
        // 修改规则：当standOnSoft17为false时，所有软牌（包括软17、软18、软19、软20）都要继续要牌
        while (dealerPoints.value < 17 ||
              (dealerPoints.isSoft && !standOnSoft17)) {
            // 检查剩余牌数
            this.checkDeckAndShuffle();

            const card = this.deck.deal();
            if (!card) {
                console.error('没有牌可抽了');
                break;
            }

            this.dealerHand.push(card);
            dealerPoints = this.calculateHandValue(this.dealerHand);

            // 立即更新UI，使用性能优化版本
            this.updateOptimizedHandDisplay('dealer-cards', this.dealerHand);

            // 更新庄家点数显示
            const dealerPointsElement = window.PerformanceUtils.elementCache.get('dealer-points') || document.getElementById('dealer-points');
            if (dealerPointsElement) {
                window.PerformanceUtils.elementCache.cache.set('dealer-points', dealerPointsElement);
                dealerPointsElement.textContent = this.getFormattedPoints(this.dealerHand);

                if (dealerPoints.value > 21) {
                    dealerPointsElement.classList.add('bust');
                } else {
                    dealerPointsElement.classList.remove('bust');
                }
            }

            // 使用固定延迟，即使在窗口后台时也保持相同的延迟
            await new Promise(resolve => setTimeout(resolve, this.dealerCardDelay));
        }

        // 等待固定时间再进行结算，使用settlementDelay设置
        const delay = 300; // 固定为300毫秒
        await new Promise(resolve => setTimeout(resolve, delay));
        this.determineWinner();
    }

    // 判定胜负
    determineWinner() {
        // 设置游戏状态为结束
        this.gameState = 'ended';

        const dealerPoints = this.calculateHandValue(this.dealerHand);
        const dealerBust = this.isBust(this.dealerHand);
        const dealerBlackjack = this.isBlackjack(this.dealerHand);

        let results = [];

        // 保存当前局的初始下注记录（而非当前下注，因为当前下注可能已经加倍）
        // 使用bets数组的第一个元素，这是初始下注值
        this.lastBets = this.players.map(player => {
            // 如果有多个手牌（分牌情况），只取第一手的初始下注
            // 对于加倍的情况，应该记录原始下注而不是加倍后的下注
            const initialBet = player.bets[0] ? (player.hands[0] && player.hands[0].doubled ? player.bets[0] / 2 : player.bets[0]) : 0;
            return initialBet;
        });
        console.log('保存本局初始下注记录:', this.lastBets);

        // 遍历所有玩家的所有手牌
        for (const player of this.players) {
            // 如果玩家没有手牌或手牌为空，跳过结算
            if (!player.hands || player.hands.length === 0 || player.hands[0].length === 0) {
                continue;
            }

            player.hands.forEach((hand, handIndex) => {
                // 获取此手牌的下注金额
                const betAmount = player.bets[handIndex] || 0;

                // 如果没有下注，跳过结算
                if (betAmount <= 0) {
                    return;
                }

                // 如果这手牌已经投降，返还一半筹码
                if (hand.surrendered) {
                    const returnAmount = player.returnBet(handIndex, 0.5);
                    // 计算亏损金额（投降损失一半下注）
                    const lossAmount = -betAmount / 2;

                    // 更新玩家统计数据
                    player.stats.losses++;
                    player.stats.surrenders++;
                    player.stats.chipProfit += lossAmount;  // 将亏损计入总收益
                    player.stats.totalScore += lossAmount;  // 更新历史兼容的总分

                    // 保存手牌的盈亏信息，用于历史记录
                    hand.profit = lossAmount;

                    // 更新最高/最低筹码记录
                    if (player.chips > player.stats.maxChips) {
                        player.stats.maxChips = player.chips;
                    }
                    if (player.chips < player.stats.minChips) {
                        player.stats.minChips = player.chips;
                    }

                    // 如果bettingHistory存在，记录游戏结果
                    if (window.bettingHistory && typeof window.bettingHistory.recordGame === 'function') {
                        // 确保传递正确的筹码数量
                        const currentChips = player.chips;
                        console.log(`记录投降结果 - 玩家${player.name}当前筹码: ${currentChips}`);

                        window.bettingHistory.recordGame(
                            this.players.indexOf(player),
                            player.name,
                            betAmount,
                            '投降',
                            lossAmount,
                            betAmount, // 原始下注金额
                            '投降', // 操作类型
                            hand, // 玩家手牌
                            this.dealerHand, // 庄家手牌
                            handIndex, // 手牌索引
                            this.deck.getTrueCount(), // 当前真数
                            currentChips // 当前剩余筹码
                        );
                    }

                    results.push({
                        text: `${player.name}${player.hands.length > 1 ? ` 手牌${handIndex + 1}` : ''}: 已投降 (返还${returnAmount}筹码, 亏损${Math.abs(lossAmount)}筹码)`,
                        class: 'special'
                    });
                    return;
                }

                const playerPoints = this.calculateHandValue(hand);
                const playerBust = this.isBust(hand);
                const playerBlackjack = this.isBlackjack(hand);
                const isDoubleDown = hand.doubled;
                const isSplitHand = hand.split;

                let result = '';
                let resultClass = '';
                let multiplier = 0; // 赔率

                if (playerBust) {
                    // 爆牌，玩家输
                    multiplier = 0;
                    result = `爆牌！`;
                    resultClass = 'special';
                } else if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                    // 玩家BlackJack，1.5倍赔率（总返还为下注金额的2.5倍）
                    // 这里的multiplier是总返还倍率，包括本金
                    multiplier = 2.5;
                    result = 'BlackJack！胜利！';
                    resultClass = 'win';
                } else if (dealerBust) {
                    // 庄家爆牌，玩家赢
                    multiplier = 2;
                    result = `庄家爆牌！胜利！`;
                    resultClass = 'win';
                } else if (dealerBlackjack) {
                    // 庄家BlackJack，玩家输
                    // 注意：分牌后的21点不算BlackJack，所以与庄家的BlackJack对比时应该判定为输
                    // 无论玩家是否有BlackJack或普通21点，只要庄家有BlackJack，玩家就输
                    // 唯一例外是玩家也有BlackJack且不是分牌手牌，这种情况在前面的条件中已经处理
                    multiplier = 0;
                    result = '庄家BlackJack！';
                    resultClass = 'lose';
                } else if (playerPoints.value > dealerPoints.value) {
                    // 玩家点数大于庄家，玩家赢
                    multiplier = 2;
                    result = playerPoints.value === 21 ? '21点！胜利！' : '胜利！';
                    resultClass = 'win';
                } else if (playerPoints.value < dealerPoints.value) {
                    // 玩家点数小于庄家，玩家输
                    multiplier = 0;
                    result = '失败！';
                    resultClass = 'lose';
                } else {
                    // 点数相同，平局，返还下注
                    multiplier = 1;
                    result = '平局！';
                    resultClass = 'push';
                }

                // 计算实际赔付金额
                let winAmount = 0;
                if (multiplier > 0) {
                    // 返还筹码并获取实际赔付金额
                    const returnAmount = player.returnBet(handIndex, multiplier);
                    // 计算纯收益（已排除本金）
                    // 对于BlackJack，multiplier=2.5，所以returnAmount=betAmount*2.5，纯收益=betAmount*1.5
                    winAmount = multiplier === 1 ? 0 : returnAmount - betAmount;
                } else {
                    // 输牌情况，不通过returnBet处理，直接设置下注为0并计算亏损
                    // 这里不直接调用returnBet(handIndex, 0)，因为returnBet已经更新为只标记下注为0
                    player.bets[handIndex] = 0;
                    winAmount = -betAmount;
                }

                // 更新玩家统计
                if (multiplier > 1) {
                    player.stats.wins++;
                    if (playerBlackjack && !isSplitHand) {
                        player.stats.blackjacks++;
                    }
                } else if (multiplier === 1) {
                    // 平局计入统计
                    player.stats.pushes++;
                } else {
                    player.stats.losses++;
                }

                // 更新筹码盈亏
                player.stats.chipProfit += winAmount;

                // 更新总得分（历史兼容，现在改为记录筹码盈亏）
                player.stats.totalScore += winAmount;

                // 更新最高/最低筹码记录
                if (player.chips > player.stats.maxChips) {
                    player.stats.maxChips = player.chips;
                }
                if (player.chips < player.stats.minChips) {
                    player.stats.minChips = player.chips;
                }

                // 记录下注历史
                let resultText = '输';
                if (multiplier > 1) {
                    resultText = playerBlackjack ? '黑杰克' : '赢';
                } else if (multiplier === 1) {
                    resultText = '平';
                } else if (playerBust) {
                    resultText = '爆牌';
                } else if (hand.surrendered) {
                    resultText = '投降';
                }

                // 如果bettingHistory存在，记录游戏结果
                if (window.bettingHistory && typeof window.bettingHistory.recordGame === 'function') {
                    // 确定操作类型
                    let actionType = '普通';
                    if (isDoubleDown) {
                        actionType = '加倍';
                    } else if (isSplitHand || hand.split) {
                        actionType = '分牌';
                        // 确保所有分牌手牌都被标记为分牌
                        if (!hand.split) {
                            hand.split = true;
                            console.log(`标记手牌为分牌: 玩家${player.name} 手牌${handIndex + 1}`);
                        }

                        // 如果是分牌的第一手牌(handIndex=0)，确保也被标记为分牌
                        if (handIndex === 0 && player.hands.length > 1) {
                            // 检查是否有其他手牌是分牌手牌
                            const hasOtherSplitHands = player.hands.some((h, idx) => idx > 0 && h.split);
                            if (hasOtherSplitHands) {
                                hand.split = true;
                                console.log(`确保第一手牌也被标记为分牌: 玩家${player.name} 手牌1`);
                            }
                        }
                    } else if (hand.surrendered) {
                        actionType = '投降';
                    }

                    // 计算原始下注金额
                    let originalBetAmount = betAmount;
                    if (isDoubleDown) {
                        // 如果是加倍，原始下注是当前下注的一半
                        originalBetAmount = Math.round(betAmount / 2);
                    }

                    // 确保传递正确的筹码数量
                    const currentChips = player.chips;
                    console.log(`记录游戏结果 - 玩家${player.name}当前筹码: ${currentChips}, 结果: ${resultText}, 盈亏: ${winAmount}`);

                    window.bettingHistory.recordGame(
                        this.players.indexOf(player),
                        player.name,
                        betAmount,
                        resultText,
                        winAmount,
                        originalBetAmount, // 传递原始下注金额
                        actionType, // 传递操作类型
                        hand, // 玩家手牌
                        this.dealerHand, // 庄家手牌
                        handIndex, // 手牌索引
                        this.deck.getTrueCount(), // 当前真数
                        currentChips // 当前剩余筹码
                    );
                }

                // 构建结果文本
                let resultText2 = `${player.name}`;
                if (player.hands.length > 1) {
                    resultText2 += ` 手牌${handIndex + 1}`;
                }
                resultText2 += `: `;
                // 如果是加倍的手牌，添加加倍标记
                if (isDoubleDown) {
                    resultText2 += '【加倍】';
                }
                resultText2 += `${playerPoints.value}点`;

                // 根据resultClass正确显示结果标识
                if (resultClass === 'win') {
                    if (dealerBust) {
                        resultText2 += ` vs ${dealerPoints.value}点 庄家爆牌！胜利！`;
                    } else if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                        resultText2 += ` vs ${dealerPoints.value}点 BlackJack！胜利！`;
                    } else if (playerPoints.value === 21) {
                        resultText2 += ` vs ${dealerPoints.value}点 21点！胜利！`;
                    } else {
                        resultText2 += ` vs ${dealerPoints.value}点 胜利！`;
                    }
                } else if (resultClass === 'lose') {
                    if (!playerBlackjack && dealerBlackjack && !isSplitHand) {
                        resultText2 += ` vs ${dealerPoints.value}点 庄家BlackJack！`;
                    } else {
                        resultText2 += ` vs ${dealerPoints.value}点 失败！`;
                    }
                } else if (resultClass === 'push') {
                    resultText2 += ` vs ${dealerPoints.value}点 平局！`;
                } else if (resultClass === 'special') {
                    if (playerBust) {
                        resultText2 += ` 爆牌！`;
                    } else {
                        resultText2 += ` vs ${dealerPoints.value}点 ${result}`;
                    }
                } else {
                    // 如果没有有效的resultClass，回退到使用result
                    if (playerBust) {
                        resultText2 += ` 爆牌！`;
                    } else if (dealerBust) {
                        resultText2 += ` vs ${dealerPoints.value}点 庄家爆牌！胜利！`;
                    } else {
                        resultText2 += ` vs ${dealerPoints.value}点 ${result || '未知结果'}`;
                    }
                }

                // 显示筹码变化
                if (winAmount > 0) {
                    resultText2 += ` +${winAmount}筹码`;
                } else if (winAmount < 0) {
                    resultText2 += ` ${winAmount}筹码`;
                } else {
                    resultText2 += ` (收支平衡)`;
                }

                results.push({
                    text: resultText2,
                    class: resultClass
                });

                // 保存手牌结果状态，用于历史记录显示
                hand.resultState = {
                    multiplier: multiplier,
                    result: result,
                    resultClass: resultClass,
                    winAmount: winAmount,
                    playerValue: playerPoints.value,
                    dealerValue: dealerPoints.value,
                    isBlackjack: playerBlackjack,
                    isDealerBlackjack: dealerBlackjack,
                    isBust: playerBust,
                    isDealerBust: dealerBust,
                    isDoubleDown: isDoubleDown,
                    isSplitHand: isSplitHand
                };
            });
        }

        // 显示结果
        const combinedText = results.map(r => {
            return `<span class="${r.class}">${r.text}</span>`;
        }).join('\n');

        // 如果没有结果，显示默认信息
        if (results.length === 0) {
            this.showResultBanner('<span class="special">本局无有效结算</span>', 'special', true);
        } else {
            this.showResultBanner(combinedText, 'special', true);
        }

        // 保存当前游戏状态到历史记录
        this.gameHistory.addRecord(this.createGameState());
        this.updateUI();

        // 延迟2.5秒后结束游戏并开始新一局
        setTimeout(() => {
            // 先调用endGame保存下注记录
            this.endGame();
            // 然后开始新游戏
            this.startNewGame(this.deck.numberOfDecks, false, false);
        }, 2500);
    }

    // 计算手牌点数
    calculateHandValue(hand) {
        let sum = 0;
        let aces = 0;
        let isSoft = false;

        // 确保hand是有效数组
        if (!hand || !Array.isArray(hand) || hand.length === 0) {
            return { value: 0, isSoft: false };
        }

        // 先计算所有非A牌的点数，并统计A的数量
        for (let card of hand) {
            if (!card.hidden) {
                if (card.rank === 'A') {
                    aces++;
                } else {
                    sum += card.getValue();
                }
            }
        }

        // 初始时所有A都算作1点
        sum += aces;

        // 尝试将一张A升级为11点（最多只有一张A可以算作11点）
        // 这样可以确保软牌的判断更加准确，特别是对于多张A的情况
        if (aces > 0 && sum + 10 <= 21) {
            sum += 10;
            isSoft = true;  // 如果有A按11点算，就是软手
        }

        return { value: sum, isSoft: isSoft };
    }

    // 检查是否爆牌
    isBust(hand) {
        return this.calculateHandValue(hand).value > 21;
    }

    // 检查是否BlackJack
    isBlackjack(hand) {
        // 只有初始发牌的两张牌21点才算BlackJack，分牌后的21点不算
        return hand.length === 2 && this.calculateHandValue(hand).value === 21 && !hand.split;
    }

    // 检查是否软牌
    isSoftHand(hand) {
        return this.calculateHandValue(hand).isSoft;
    }

        // 显示结果横幅
    showResultBanner(text, className = '', multiLine = false, keepVisible = false) {
        const banner = document.getElementById('result-banner');
        if (!banner) return;

        // 移除现有的切换按钮容器（如果存在）
        const existingContainer = document.querySelector('.history-toggle-container');
        if (existingContainer) {
            document.body.removeChild(existingContainer);
        }

        // 保存已有的隐藏状态
        let wasHidden = banner.classList.contains('history-hidden');

        // 设置横幅内容和样式
        banner.innerHTML = text;
        banner.className = 'result-banner ' + className;
        banner.style.whiteSpace = multiLine ? 'pre-line' : 'nowrap';
        banner.style.display = 'block';

        // 如果之前是隐藏状态，恢复隐藏状态
        if (wasHidden) {
            banner.classList.add('history-hidden');
        }

        // 在历史记录查看模式下，添加显示/隐藏按钮
        if (this.isViewingHistory) {
            // 创建切换按钮容器
            const toggleContainer = document.createElement('div');
            toggleContainer.className = 'history-toggle-container';
            toggleContainer.style.display = wasHidden ? 'block' : 'none';

            // 创建新的按钮 - 用于独立容器
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'history-result-toggle';
            toggleBtn.textContent = '显示结算结果';
            toggleBtn.style.background = 'linear-gradient(135deg, #4f46e5, #4338ca)';

            // 添加点击事件处理
            toggleBtn.addEventListener('click', (e) => {
                // 阻止事件冒泡，确保点击按钮不会同时触发横幅的点击事件
                e.stopPropagation();
                e.preventDefault();

                // 给按钮添加短暂的点击反馈动画
                toggleBtn.style.transform = 'translateX(-50%) scale(0.95)';
                setTimeout(() => {
                    toggleBtn.style.transform = 'translateX(-50%)';
                }, 100);

                // 显示结果内容
                banner.classList.remove('history-hidden');
                banner.style.opacity = '1';
                banner.style.maxHeight = '80vh';
                banner.style.padding = '35px 50px';
                banner.style.display = 'block';
                toggleContainer.style.display = 'none';
            });

            // 创建横幅内的按钮
            const bannerBtn = document.createElement('button');
            bannerBtn.className = 'history-result-toggle';
            bannerBtn.textContent = '隐藏结算结果';
            bannerBtn.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
            bannerBtn.style.position = 'absolute';
            bannerBtn.style.top = '-40px';

            // 添加横幅内按钮的点击事件
            bannerBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();

                // 给按钮添加短暂的点击反馈动画
                this.style.transform = 'translateX(-50%) scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateX(-50%)';
                }, 100);

                // 隐藏结果内容，显示独立按钮
                banner.classList.add('history-hidden');
                toggleContainer.style.display = 'block';
            });

            // 将按钮添加到对应容器
            banner.appendChild(bannerBtn);
            toggleContainer.appendChild(toggleBtn);
            document.body.appendChild(toggleContainer);
        }

        if (!keepVisible && !this.isViewingHistory) {
            setTimeout(() => {
                banner.style.display = 'none';
            }, 2500); // 修改为2.5秒
        }
    }

    // 查看上一局
    viewPreviousGame() {
        console.log('查看上一局');

        // 如果当前在进行中的游戏，保存状态
        if (this.gameHistory.currentIndex === -1) {
            console.log('保存当前游戏状态');
            this.gameHistory.saveActiveGame(this.createGameState());
        }

        // 清空所有卡牌容器
        this._clearAllHandContainers();

        // 清除现有的结算横幅但保存其状态
        const banner = document.getElementById('result-banner');
        let wasHidden = false;
        if (banner) {
            // 保存横幅状态
            wasHidden = banner.classList.contains('history-hidden');
            banner.style.display = 'none';
            banner.innerHTML = '';
            // 保持hidden类但去掉其他类
            banner.className = 'result-banner';
            if (wasHidden) {
                banner.classList.add('history-hidden');
            }
        }

        // 获取上一局记录
        const gameState = this.gameHistory.getPreviousRecord();
        if (gameState) {
            console.log('加载历史记录，指针:', this.gameHistory.currentIndex);

            // 确保isViewingHistory标志在加载游戏状态前被设置
            this.isViewingHistory = true;

            // 强制设置游戏状态为ended，以确保结算横幅显示
            if (gameState.gameState === 'ended') {
                console.log('历史记录是已结束的游戏，确保显示结算横幅');
            } else {
                console.log('强制将历史记录游戏状态设置为ended以显示结算横幅');
                gameState.gameState = 'ended';
            }

            this.loadGameState(gameState);

            // 将记录的时间戳显示在页面上
            this._showHistoryTimestamp(gameState.timestamp);

            console.log('是否查看历史记录:', this.isViewingHistory ? '是' : '否');
        }

        this.updateHistoryButtons();
    }

    // 查看下一局
    viewNextGame() {
        console.log('查看下一局');

        // 清空所有卡牌容器
        this._clearAllHandContainers();

        // 清除现有的结算横幅但保存其状态
        const banner = document.getElementById('result-banner');
        let wasHidden = false;
        if (banner) {
            // 保存横幅状态
            wasHidden = banner.classList.contains('history-hidden');
            banner.style.display = 'none';
            banner.innerHTML = '';
            // 保持hidden类但去掉其他类
            banner.className = 'result-banner';
            if (wasHidden) {
                banner.classList.add('history-hidden');
            }
        }

        const gameState = this.gameHistory.getNextRecord();
        if (gameState) {
            console.log('加载历史记录，指针:', this.gameHistory.currentIndex);

            // 确保isViewingHistory标志在加载游戏状态前被设置
            this.isViewingHistory = true;

            // 强制设置游戏状态为ended，以确保结算横幅显示
            if (gameState.gameState === 'ended') {
                console.log('历史记录是已结束的游戏，确保显示结算横幅');
            } else {
                console.log('强制将历史记录游戏状态设置为ended以显示结算横幅');
                gameState.gameState = 'ended';
            }

            this.loadGameState(gameState);

            // 将记录的时间戳显示在页面上
            this._showHistoryTimestamp(gameState.timestamp);

            console.log('是否查看历史记录:', this.isViewingHistory ? '是' : '否');
        }

        this.updateHistoryButtons();
    }

    // 显示历史记录时间戳
    _showHistoryTimestamp(timestamp) {
        try {
            // 如果没有时间戳但正在查看历史记录，使用当前时间
            if (!timestamp && this.isViewingHistory) {
                timestamp = Date.now();
                console.log('没有时间戳，使用当前时间');
            } else if (!timestamp) {
                console.log('没有时间戳，不显示时间戳');
                return;
            }

            console.log('显示历史记录时间戳:', new Date(timestamp).toLocaleString());

            // 创建或获取时间戳显示元素
            let timestampElement = document.getElementById('history-timestamp');
            if (!timestampElement) {
                timestampElement = document.createElement('div');
                timestampElement.id = 'history-timestamp';
                timestampElement.className = 'history-timestamp';

                // 添加到历史记录控制区域
                const historyControls = document.getElementById('history-controls');
                if (historyControls) {
                    historyControls.appendChild(timestampElement);
                } else {
                    // 没有找到控制区域，添加到页面顶部
                    document.body.insertBefore(timestampElement, document.body.firstChild);
                }
            }

            // 格式化时间
            const date = new Date(timestamp);
            const formattedTime = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

            // 显示时间戳
            timestampElement.textContent = `查看历史记录: ${formattedTime}`;
            timestampElement.style.display = 'block';
        } catch (error) {
            console.error('显示历史记录时间戳失败:', error);
        }
    }

    // 返回当前局
    returnToActiveGame() {
        console.log('返回当前游戏');

        // 清空所有卡牌容器
        this._clearAllHandContainers();

        // 隐藏时间戳显示
        const timestampElement = document.getElementById('history-timestamp');
        if (timestampElement) {
            timestampElement.style.display = 'none';
        }

        // 完全清除现有的结算横幅（不保存状态，因为返回到当前游戏）
        const banner = document.getElementById('result-banner');
        if (banner) {
            banner.style.display = 'none';
            banner.innerHTML = '';
            banner.className = 'result-banner'; // 重置所有类
        }

        // 设置为非历史记录查看模式
        this.isViewingHistory = false;

        const gameState = this.gameHistory.resetToActive();
        if (gameState) {
            console.log('恢复当前游戏状态');
            this.loadGameState(gameState);
        }

        this.updateHistoryButtons();
    }

    // 更新历史导航按钮状态
    updateHistoryButtons() {
        const prevButton = document.getElementById('prev-hand');
        const nextButton = document.getElementById('next-hand');
        const currentButton = document.getElementById('current-hand');

        if (!prevButton || !nextButton || !currentButton) {
            return;
        }

        // 更新按钮状态
        prevButton.disabled = !this.gameHistory.canViewPrevious();
        nextButton.disabled = !this.gameHistory.canViewNext();
        currentButton.disabled = !this.gameHistory.canReturnToActive();

        // 确保按钮的样式反映禁用状态
        if (prevButton.disabled) {
            prevButton.classList.add('disabled');
        } else {
            prevButton.classList.remove('disabled');
        }

        if (nextButton.disabled) {
            nextButton.classList.add('disabled');
        } else {
            nextButton.classList.remove('disabled');
        }

        if (currentButton.disabled) {
            currentButton.classList.add('disabled');
        } else {
            currentButton.classList.remove('disabled');
        }

        console.log('按钮状态更新:', {
            currentIndex: this.gameHistory.currentIndex,
            recordsCount: this.gameHistory.records.length,
            hasActiveGame: !!this.gameHistory.getActiveGameState(),
            buttons: {
                prev: prevButton.disabled ? '禁用' : '启用',
                next: nextButton.disabled ? '禁用' : '启用',
                current: currentButton.disabled ? '禁用' : '启用'
            }
        });
    }

    // 加载游戏状态
    loadGameState(gameState) {
        console.log('加载游戏状态:', gameState.timestamp || '未知时间');
        if (!gameState) {
            console.error('无效的游戏状态');
            return;
        }

        // 标记是在查看历史记录
        this.isViewingHistory = this.gameHistory.currentIndex !== -1;
        console.log('是否查看历史记录:', this.isViewingHistory ? '是' : '否', '当前索引:', this.gameHistory.currentIndex);

        // 首先清空所有现有的玩家和手牌容器元素
        this._clearAllHandContainers();

        // 恢复玩家状态
        this.players = gameState.players.map(playerState => {
            const player = new Player(playerState.name);

            // 恢复手牌
            player.hands = playerState.hands.map(handState => {
                // 检查新版本数据结构
                if (handState.cards && Array.isArray(handState.cards)) {
                    // 新数据结构
                    const hand = handState.cards.map(cardState => {
                        const card = new Card(cardState.suit, cardState.rank);
                        card.hidden = cardState.hidden;
                        return card;
                    });

                    // 恢复手牌的特殊状态
                    hand.surrendered = handState.surrendered;
                    hand.doubled = handState.doubled;
                    hand.split = handState.split;
                    hand.profit = handState.profit;

                    // 新增：从 resultState 恢复结果状态
                    if (handState.resultState) {
                        hand.resultState = handState.resultState;
                    }

                    return hand;
                } else {
                    // 兼容旧数据结构
                    const hand = handState.filter(card => typeof card === 'object' && card.suit).map(cardState => {
                        const card = new Card(cardState.suit, cardState.rank);
                        card.hidden = cardState.hidden;
                        return card;
                    });

                    // 尝试从第一张牌恢复特殊状态
                    if (handState[0]) {
                        hand.surrendered = handState[0].surrendered;
                        hand.doubled = handState[0].doubled;
                        hand.split = handState[0].split;
                        hand.profit = handState[0].profit;

                        // 新增：恢复结果状态
                        if (handState[0].resultState) {
                            hand.resultState = handState[0].resultState;
                        }
                    }

                    return hand;
                }
            });

            player.currentHandIndex = playerState.currentHandIndex;
            player.gameState = playerState.gameState;
            player.stats = { ...playerState.stats };

            // 恢复筹码和下注信息
            if (playerState.chips !== undefined) {
                player.chips = playerState.chips;
            }

            if (playerState.bets && Array.isArray(playerState.bets)) {
                player.bets = [...playerState.bets];
            }

            if (playerState.initialBets && Array.isArray(playerState.initialBets)) {
                player.initialBets = [...playerState.initialBets];
            }

            if (playerState.betStatus) {
                player.betStatus = playerState.betStatus;
            }

            return player;
        });

        // 恢复庄家手牌
        this.dealerHand = gameState.dealerHand.map(cardState => {
            const card = new Card(cardState.suit, cardState.rank);
            card.hidden = cardState.hidden;
            return card;
        });

        // 恢复游戏状态
        this.gameState = gameState.gameState;
        this.currentPlayerIndex = gameState.currentPlayerIndex;

        // 恢复牌堆状态
        if (gameState.deck) {
            this.deck = new Deck(gameState.deck.numberOfDecks);
            this.deck.cards = gameState.deck.cards.map(cardState => {
                const card = new Card(cardState.suit, cardState.rank);
                card.hidden = cardState.hidden;
                return card;
            });
            this.deck.usedCards = gameState.deck.usedCards.map(cardState => {
                const card = new Card(cardState.suit, cardState.rank);
                card.hidden = cardState.hidden;
                return card;
            });
        }

        console.log('游戏状态加载完成');
        console.log('当前游戏状态:', this.gameState);
        console.log('玩家数量:', this.players.length);
        console.log('庄家手牌数量:', this.dealerHand.length);
        console.log('牌堆剩余牌数:', this.deck.cards.length);

        // 更改刷新机制，强制完全重建UI
        this._forceRebuildUI();

        // 先清除结算横幅，无论游戏状态如何
        const banner = document.getElementById('result-banner');
        if (banner) {
            banner.style.display = 'none';
            banner.innerHTML = '';
        }

        // 如果是已结束状态或正在查看历史记录，显示结果
        if (this.gameState === 'ended' || this.isViewingHistory) {
            console.log('加载已结束的游戏状态或查看历史记录，显示结果');

            // 如果正在查看历史记录，强制将游戏状态设为ended，以确保结算横幅显示
            if (this.isViewingHistory && this.gameState !== 'ended') {
                console.log('强制将游戏状态设置为ended以显示结算横幅');
                this.gameState = 'ended';
            }

            // 确保在查看历史记录时，结算横幅始终可见
            const keepVisible = this.isViewingHistory;
            console.log('是否保持结算横幅可见:', keepVisible ? '是' : '否');

            // 使用setTimeout确保UI更新后再显示结果横幅
            setTimeout(() => {
                // 强制显示结果，使用递归尝试直到成功
                const showResultWithRetry = (attempts = 0) => {
                    if (attempts > 3) return; // 最多尝试3次

                    try {
                        this.showGameResult(keepVisible);
                    } catch (error) {
                        console.error('显示结果时出错，将重试:', error);
                        setTimeout(() => showResultWithRetry(attempts + 1), 100);
                    }
                };

                showResultWithRetry();
            }, 200);
        }
    }

    // 清空所有手牌容器元素，用于历史记录查看时重置
    _clearAllHandContainers() {
        // 清空庄家手牌容器
        const dealerCardsContainer = document.getElementById('dealer-cards');
        if (dealerCardsContainer) {
            const cardsDiv = dealerCardsContainer.querySelector('.cards');
            if (cardsDiv) {
                cardsDiv.innerHTML = '';
            }
        }

        // 清空玩家区域
        const playersContainer = document.getElementById('players-container');
        if (playersContainer) {
            playersContainer.innerHTML = '';
        }
    }

    // 强制完全重建UI，用于历史记录查看
    _forceRebuildUI() {
        try {
            // 禁用节流和优化，强制完全重建
            this._lastPlayerCount = null;
            this._lastGameState = null;
            this._isUpdating = false;
            this._uiUpdateScheduled = false;

            // 强制同步更新，不使用requestAnimationFrame
            this._performUIUpdate();

            // 记录历史记录相关信息
            if (this.isViewingHistory) {
                console.log('正在查看历史记录, 索引:', this.gameHistory.currentIndex, '总记录数:', this.gameHistory.records.length);
            }
        } catch (error) {
            console.error('强制重建UI时出错:', error);
        }
    }

    // 显示游戏结果（从determineWinner方法中提取的结果显示逻辑）
    showGameResult(keepVisible = false) {
        console.log('显示游戏结果，保持显示:', keepVisible);

        // 修改此处判断，允许在查看历史记录时显示结果，即使游戏状态可能不是ended
        if (this.gameState !== 'ended' && !this.isViewingHistory) {
            console.log('游戏尚未结束且不是查看历史记录，不显示结果');
            return;
        }

        // 如果正在查看历史记录，强制将游戏状态设为ended
        if (this.isViewingHistory && this.gameState !== 'ended') {
            console.log('查看历史记录时强制设置游戏状态为ended');
            this.gameState = 'ended';
        }

        const dealerValue = this.calculateHandValue(this.dealerHand);
        const dealerBust = this.isBust(this.dealerHand);
        const dealerBlackjack = this.isBlackjack(this.dealerHand);

        let results = [];

        this.players.forEach((player, playerIndex) => {
            // 如果玩家没有手牌或手牌为空，跳过得分统计
            if (!player.hands || player.hands.length === 0 || player.hands[0].length === 0) {
                console.log('玩家没有手牌，跳过:', player.name);
                return;
            }

            player.hands.forEach((hand, handIndex) => {
                // 获取当前手牌的下注金额和初始下注
                const betAmount = player.initialBets[handIndex] || 0;
                const isDoubleDown = hand.doubled;
                const isSplitHand = hand.split;

                // 如果这手牌已经投降，显示投降结果
                if (hand.surrendered) {
                    // 获取投降后返还的金额
                    const returnAmount = betAmount * 0.5;
                    // 获取亏损金额
                    const lossAmount = -betAmount * 0.5;

                    results.push({
                        text: `${player.name}${player.hands.length > 1 ? ` 手牌${handIndex + 1}` : ''}: 已投降 (返还${returnAmount}筹码, 亏损${Math.abs(lossAmount)}筹码)`,
                        class: 'special'
                    });
                    return;
                }

                const playerValue = this.calculateHandValue(hand);
                const playerBust = this.isBust(hand);
                const playerBlackjack = this.isBlackjack(hand);

                let result = '';
                let resultClass = '';

                // 使用存储的结果状态信息，如果存在
                let winAmount = hand.profit !== undefined ? hand.profit : 0;

                if (hand.resultState) {
                    result = hand.resultState.result || '';
                    resultClass = hand.resultState.resultClass || '';
                    if (hand.resultState.winAmount !== undefined) {
                        winAmount = hand.resultState.winAmount;
                    }

                    // 兼容性修复：如果加载的历史记录没有正确的结果类，根据胜负情况推断
                    if (!resultClass || resultClass === '') {
                        if (winAmount > 0) {
                            resultClass = 'win';
                            if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                                result = 'BlackJack！胜利！';
                            } else if (dealerBust) {
                                result = '庄家爆牌！胜利！';
                            } else if (playerPoints.value === 21) {
                                result = '21点！胜利！';
                            } else {
                                result = '胜利！';
                            }
                        } else if (winAmount < 0) {
                            if (playerBust) {
                                resultClass = 'special';
                                result = '爆牌！';
                            } else if (dealerBlackjack) {
                                resultClass = 'lose';
                                result = '庄家BlackJack！';
                            } else {
                                resultClass = 'lose';
                                result = '失败！';
                            }
                        } else {
                            resultClass = 'push';
                            result = '平局！';
                        }
                    }
                } else if (winAmount === 0 && hand.profit === undefined) {
                    if (playerBust) {
                        result = `爆牌！`;
                        resultClass = 'special';
                        winAmount = -betAmount * (isDoubleDown ? 2 : 1);
                    } else if (dealerBust) {
                        result = `庄家爆牌！胜利！`;
                        resultClass = 'win';
                        winAmount = betAmount * (isDoubleDown ? 1 : 1);
                    } else if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                        result = 'BlackJack！胜利！';
                        resultClass = 'win';
                        // BlackJack赔率为1赔1.5，即总返还为下注金额的2.5倍
                        // 纯收益为下注金额的1.5倍
                        winAmount = betAmount * 1.5;
                    } else if (dealerBlackjack) {
                        // 庄家BlackJack，玩家输
                        // 注意：分牌后的21点不算BlackJack，所以与庄家的BlackJack对比时应该判定为输
                        // 无论玩家是否有BlackJack或普通21点，只要庄家有BlackJack，玩家就输
                        // 唯一例外是玩家也有BlackJack且不是分牌手牌，这种情况在前面的条件中已经处理
                        result = '庄家BlackJack！';
                        resultClass = 'lose';
                        winAmount = -betAmount;
                    } else if (playerValue.value > dealerValue.value) {
                        result = playerValue.value === 21 ? '21点！胜利！' : '胜利！';
                        resultClass = 'win';
                        winAmount = betAmount * (isDoubleDown ? 1 : 1);
                    } else if (playerValue.value < dealerValue.value) {
                        result = '失败！';
                        resultClass = 'lose';
                        winAmount = -betAmount * (isDoubleDown ? 2 : 1);
                    } else {
                        result = '平局！';
                        resultClass = 'push';
                        winAmount = 0;
                    }
                } else {
                    // 根据盈亏确定结果文本和样式
                    if (winAmount > 0) {
                        result = playerBlackjack ? 'BlackJack！胜利！' : '胜利！';
                        resultClass = 'win';
                    } else if (winAmount < 0) {
                        if (playerBust) {
                            result = '爆牌！';
                            resultClass = 'special';
                        } else {
                            result = '失败！';
                            resultClass = 'lose';
                        }
                    } else {
                        result = '平局！';
                        resultClass = 'push';
                    }
                }

                // 构建结果文本
                let resultText = `${player.name}${player.hands.length > 1 ? ` 手牌${handIndex + 1}` : ''}: `;
                // 如果是加倍的手牌，添加加倍标记
                if (isDoubleDown) {
                    resultText += '【加倍】';
                }
                resultText += `${playerValue.value}点`;

                // 根据resultClass正确显示结果标识
                if (resultClass === 'win') {
                    if (dealerBust) {
                        resultText += ` vs ${dealerValue.value}点 庄家爆牌！胜利！`;
                    } else if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                        resultText += ` vs ${dealerValue.value}点 BlackJack！胜利！`;
                    } else if (playerValue && playerValue.value === 21) {
                        resultText += ` vs ${dealerValue.value}点 21点！胜利！`;
                    } else {
                        resultText += ` vs ${dealerValue.value}点 胜利！`;
                    }
                } else if (resultClass === 'lose') {
                    if (dealerBlackjack) {
                        resultText += ` vs ${dealerValue.value}点 庄家BlackJack！`;
                    } else {
                        resultText += ` vs ${dealerValue.value}点 失败！`;
                    }
                } else if (resultClass === 'push') {
                    resultText += ` vs ${dealerValue.value}点 平局！`;
                } else if (resultClass === 'special') {
                    if (playerBust) {
                        resultText += ` 爆牌！`;
                    } else {
                        resultText += ` vs ${dealerValue.value}点 ${result}`;
                    }
                } else {
                    // 如果没有有效的resultClass，回退到使用result
                    if (playerBust) {
                        resultText += ` 爆牌！`;
                    } else if (dealerBust) {
                        resultText += ` vs ${dealerValue.value}点 庄家爆牌！胜利！`;
                    } else {
                        resultText += ` vs ${dealerValue.value}点 ${result || '未知结果'}`;
                    }
                }

                // 显示筹码变化
                if (winAmount > 0) {
                    resultText += ` +${winAmount}筹码`;
                } else if (winAmount < 0) {
                    resultText += ` ${winAmount}筹码`;
                } else {
                    resultText += ` (收支平衡)`;
                }

                results.push({
                    text: resultText,
                    class: resultClass
                });
            });
        });

        // 显示结果
        const combinedText = results.map(r => {
            return `<span class="${r.class}">${r.text}</span>`;
        }).join('\n');

        console.log('显示结果横幅:', combinedText, '保持显示:', keepVisible);

        // 在查看历史记录时，结算横幅应该保持显示
        // 在正常游戏结束时，结算横幅应该在一段时间后消失
        try {
            // 检查是否是查看历史记录时的结算横幅，并记录历史记录状态
            const isHistoryResult = this.isViewingHistory;

            // 获取当前横幅元素检查是否处于隐藏状态
            const banner = document.getElementById('result-banner');
            const wasHidden = banner && banner.classList.contains('history-hidden');

            // 如果正在查看历史记录，强制保持横幅显示，并应用历史记录特有的样式
            if (isHistoryResult) {
                let className = 'special history-result';
                // 保持隐藏状态如果原来是隐藏的
                if (wasHidden) {
                    className += ' history-hidden';
                }
                // 为历史记录显示添加特殊标记
                this.showResultBanner(combinedText, className, true, true);
            } else {
                // 正常游戏结束显示
                this.showResultBanner(combinedText, 'special', true, keepVisible);
            }
        } catch (error) {
            console.error('显示结算横幅时出错:', error);
            // 尝试创建基本的横幅信息作为备选方案
            const basicText = `<span class="special">游戏已结束 (加载结果出错)</span>`;
            this.showResultBanner(basicText, 'special', true, true);
        }
    }

    // 获取格式化的点数显示文本
    getFormattedPoints(hand) {
        if (!hand || !Array.isArray(hand) || hand.length === 0) {
            return '0';
        }

        try {
            // 直接计算当前手牌的点数
            const points = this.calculateHandValue(hand);

            // 检查是否爆牌
            if (points.value > 21) {
                return `${points.value} 爆牌`;
            }

            // 检查是否为BlackJack
            if (this.isBlackjack(hand)) {
                return 'BlackJack';
            }

            // 如果是分牌后的手牌，且点数为21，直接返回21
            if (hand.split && points.value === 21) {
                return '21';
            }

            // 如果是软手(有一张A当11点计算)，显示两种可能的点数
            if (points.isSoft) {
                // 一种计算方式是A当1点
                const smallValue = points.value - 10;
                return `${smallValue}/${points.value}`;
            }

            // 其他情况直接返回总点数
            return points.value.toString();
        } catch (error) {
            console.error('计算点数时出错:', error);
            return '?';
        }
    }

    // 更新牌库信息显示
    updateDeckInfo() {
        // 检查是否是蜗牛洗牌模式
        if (this.deck.isSnailMode) {
            // 蜗牛洗牌模式下，更新HTML结构，只显示基本信息
            const deckInfo = document.querySelector('.deck-info');
            if (deckInfo) {
                const totalCards = this.deck.numberOfDecks * 52;
                const usedCards = this.deck.getUsedCardsCount();
                const remainingCards = this.deck.getRemainingCards();

                deckInfo.innerHTML = `
                    <span>牌库数量: <span id="deck-count">${this.deck.numberOfDecks}</span></span>
                    <span>已发牌数: <span id="cards-used">${usedCards}</span></span>
                    <span>剩余牌数: <span id="cards-remaining">${remainingCards}</span></span>
                    <span>蜗牛洗牌模式</span>
                `;
            }
            return;
        }

        // 传统洗牌模式下，使用缓存获取DOM元素
        const elementIds = [
            'deck-count', 'cards-used', 'cards-remaining',
            'shuffle-threshold', 'running-count', 'true-count', 'shuffle-count', 'counting-system'
        ];

        // 计算所有值
        const values = {
            'deck-count': this.deck.numberOfDecks,
            'cards-used': this.deck.getUsedCardsCount(),
            'cards-remaining': this.deck.getRemainingCards(),
            'shuffle-threshold': Math.ceil(this.deck.getTotalCards() * this.deck.getPenetrationRate()),
            'running-count': this.deck.getRunningCount(),
            'true-count': this.deck.getTrueCount(),
            'shuffle-count': this.deck.getShuffleCount(),
            'counting-system': this.deck.getCurrentCountingSystem()
        };

        // 检查是否需要重建整个牌库信息区域
        const deckInfo = document.querySelector('.deck-info');
        if (deckInfo && deckInfo.innerHTML.includes('蜗牛洗牌模式')) {
            // 如果之前是蜗牛洗牌模式，现在切换到传统模式，需要重建HTML结构
            deckInfo.innerHTML = `
                <span>牌库数量: <span id="deck-count">${this.deck.numberOfDecks}</span></span>
                <span>已发牌数: <span id="cards-used">${this.deck.getUsedCardsCount()}</span></span>
                <span>剩余牌数: <span id="cards-remaining">${this.deck.getRemainingCards()}</span></span>
                <span>洗牌阈值: <span id="shuffle-threshold">${values['shuffle-threshold']}</span></span>
                <span>算牌系统: <span id="counting-system">${values['counting-system']}</span></span>
                <span>流水数: <span id="running-count">${values['running-count']}</span></span>
                <span>真数: <span id="true-count">${values['true-count']}</span></span>
                <span>洗牌次数: <span id="shuffle-count">${values['shuffle-count']}</span></span>
            `;
            return;
        }

        // 批量更新DOM元素，只在值变化时更新
        elementIds.forEach(id => {
            const element = window.PerformanceUtils.elementCache.get(id) || document.getElementById(id);
            if (element) {
                // 缓存元素
                window.PerformanceUtils.elementCache.cache.set(id, element);

                // 只在内容不同时更新DOM
                const newValue = String(values[id]);
                if (element.textContent !== newValue) {
                    element.textContent = newValue;
                }
            }
        });
    }

    // 更新UI
    updateUI() {
        // 使用requestAnimationFrame同步UI更新到浏览器渲染周期
        if (!this._uiUpdateScheduled) {
            this._uiUpdateScheduled = true;

            requestAnimationFrame(() => {
                this._performUIUpdate();
                this._uiUpdateScheduled = false;
            });
        }
    }

    // 实际执行UI更新的方法
    _performUIUpdate() {
        if (this._isUpdating) return;
        this._isUpdating = true;

        try {
            // 创建DocumentFragment以批量更新DOM
            const fragment = document.createDocumentFragment();

            // 1. 使用elementCache更新牌库信息
            this.updateDeckInfo();

            // 2. 使用优化的方法更新庄家手牌
            this.updateOptimizedHandDisplay('dealer-cards', this.dealerHand);

            // 3. 更新庄家点数显示，使用缓存DOM元素
            const dealerPoints = window.PerformanceUtils.elementCache.get('dealer-points') || document.getElementById('dealer-points');
            if (dealerPoints) {
                window.PerformanceUtils.elementCache.cache.set('dealer-points', dealerPoints);

                const pointsText = this.getFormattedPoints(this.dealerHand);
                // 只在点数变化时更新文本内容
                if (dealerPoints.textContent !== pointsText) {
                    dealerPoints.textContent = pointsText;
                }

                // 处理爆牌状态
                const dealerIsBust = this.calculateHandValue(this.dealerHand).value > 21;
                if (dealerIsBust && !dealerPoints.classList.contains('bust')) {
                    dealerPoints.classList.add('bust');
                } else if (!dealerIsBust && dealerPoints.classList.contains('bust')) {
                    dealerPoints.classList.remove('bust');
                }
            }

            // 4. 更新按钮状态
            this.updateButtonStates();

            // 5. 更新历史按钮状态
            this.updateHistoryButtons();

            // 6. 更新测试模式UI
            this.updateTestModeUI();

            // 7. 更新策略提示
            this.updateStrategyHint();

            // 8. 优化玩家区域更新，使用DocumentFragment
            const playersContainer = window.PerformanceUtils.elementCache.get('players-container') || document.getElementById('players-container');
            if (playersContainer) {
                window.PerformanceUtils.elementCache.cache.set('players-container', playersContainer);

                // 只有在玩家数量变化或游戏状态变化时才重建整个玩家区域
                const shouldRebuildPlayers =
                    !this._lastPlayerCount ||
                    this._lastPlayerCount !== this.players.length ||
                    this._lastGameState !== this.gameState;

                if (shouldRebuildPlayers) {
                    this.updatePlayersDisplay();
                    this._lastPlayerCount = this.players.length;
                    this._lastGameState = this.gameState;
                } else {
                    // 只更新活跃玩家区域和变化的部分
                    this.updateActivePlayerDisplay();
                }
            }

            // 9. 更新总盈亏
            const totalProfit = this.players.reduce((sum, player) => sum + player.stats.chipProfit, 0);
            const totalScoreElement = window.PerformanceUtils.elementCache.get('total-score') || document.getElementById('total-score');
            if (totalScoreElement) {
                window.PerformanceUtils.elementCache.cache.set('total-score', totalScoreElement);

                // 只在金额变化时更新显示
                const scoreText = totalProfit > 0 ? '+' + totalProfit : String(totalProfit);
                if (totalScoreElement.textContent !== scoreText) {
                    // 清除之前的类
                    totalScoreElement.classList.remove('positive', 'negative');

                    // 添加正负值的类
                    if (totalProfit > 0) {
                        totalScoreElement.classList.add('positive');
                        totalScoreElement.textContent = scoreText;
                    } else if (totalProfit < 0) {
                        totalScoreElement.classList.add('negative');
                        totalScoreElement.textContent = totalProfit;
                    } else {
                        totalScoreElement.textContent = '0';
                    }
                }
            }
        } catch (error) {
            console.error('更新UI时出错:', error);
        } finally {
            this._isUpdating = false;
        }
    }

    // 只更新活跃玩家的显示
    updateActivePlayerDisplay() {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) return;

        const currentPlayerIndex = this.currentPlayerIndex;
        const currentHandIndex = currentPlayer.currentHandIndex;

        // 获取当前玩家的DOM元素
        const playerElement = document.getElementById(`player-${currentPlayerIndex}`);
        if (!playerElement) return;

        // 首先移除所有玩家的活跃状态和手牌活跃状态
        document.querySelectorAll('.player-section').forEach(section => {
            section.classList.remove('active');
            section.querySelectorAll('.hand-container').forEach(container => {
                container.classList.remove('active');
            });
            // 移除所有当前操作指示器
            const indicator = section.querySelector('.current-player-indicator');
            if (indicator) {
                indicator.remove();
            }
        });

        // 确保当前玩家有活跃状态和当前操作指示器
        if (this.gameState === 'playing' && currentPlayer.gameState !== 'ended') {
            // 添加活跃状态
            playerElement.classList.add('active');

            // 检查并添加当前操作指示器
            if (!playerElement.querySelector('.current-player-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'current-player-indicator';
                indicator.textContent = '当前操作';
                playerElement.appendChild(indicator);
            }
        }

        // 更新玩家的手牌显示
        currentPlayer.hands.forEach((hand, handIndex) => {
            const handContainer = document.getElementById(`player-${currentPlayerIndex}-hand-${handIndex}`);
            if (!handContainer) return;

            // 更新手牌是否活跃
            const isActive = this.gameState === 'playing' && currentPlayerIndex === this.currentPlayerIndex && handIndex === currentHandIndex;

            // 先移除活跃状态，然后只给当前手牌添加活跃状态
            handContainer.classList.remove('active');
            if (isActive) {
                handContainer.classList.add('active');
            }

            // 更新手牌卡牌
            const cardsContainer = handContainer.querySelector('.cards');
            if (cardsContainer) {
                this.updateOptimizedHandDisplay(`player-${currentPlayerIndex}-hand-${handIndex}`, hand);
            }

            // 更新手牌点数
            const pointsDisplay = handContainer.querySelector('.points-display');
            if (pointsDisplay) {
                const pointsText = this.getFormattedPoints(hand);
                if (pointsDisplay.textContent !== pointsText) {
                    pointsDisplay.textContent = pointsText;
                }

                // 更新爆牌状态
                const isBust = this.calculateHandValue(hand).value > 21;
                pointsDisplay.classList.toggle('bust', isBust);
            }

            // 更新下注显示
            const betDisplay = handContainer.querySelector('.bet-display .bet-value');
            if (betDisplay) {
                const betAmount = currentPlayer.bets[handIndex] || 0;
                if (betDisplay.textContent !== String(betAmount)) {
                    betDisplay.textContent = betAmount;
                }
            }
        });

        // 更新玩家筹码显示
        const chipsDisplay = playerElement.querySelector('.player-stats .chips');
        if (chipsDisplay) {
            const chipsText = `筹码: ${currentPlayer.chips}`;
            if (chipsDisplay.textContent !== chipsText) {
                chipsDisplay.innerHTML = `<span class="stat-label">筹码:</span> ${currentPlayer.chips}`;
            }
        }
    }

    // 优化玩家区域更新
    updatePlayersDisplay() {
        const playersContainer = window.PerformanceUtils.elementCache.get('players-container') || document.getElementById('players-container');
        if (!playersContainer) return;
        window.PerformanceUtils.elementCache.cache.set('players-container', playersContainer);

        // 使用DocumentFragment减少DOM操作
        const fragment = document.createDocumentFragment();

        // 先移除所有现有的当前操作指示器
        const existingIndicators = document.querySelectorAll('.current-player-indicator');
        existingIndicators.forEach(indicator => {
            indicator.remove();
        });

        this.players.forEach((player, playerIndex) => {
            const playerSection = document.createElement('div');
            playerSection.className = 'player-section';
            playerSection.id = `player-${playerIndex}`;

            // 只有在游戏状态为playing且是当前玩家时才添加active类
            if (this.gameState === 'playing' && playerIndex === this.currentPlayerIndex && player.gameState !== 'ended') {
                playerSection.classList.add('active');

                // 添加当前玩家标识
                const indicator = document.createElement('div');
                indicator.className = 'current-player-indicator';
                indicator.textContent = '当前操作';
                playerSection.appendChild(indicator);
            }

            // 玩家信息
            const playerInfo = document.createElement('h2');
            playerInfo.textContent = player.name;
            playerSection.appendChild(playerInfo);

            // 玩家统计信息 - 预先构建
            const statsDiv = this._buildPlayerStatsElement(player);
            playerSection.appendChild(statsDiv);

            // 玩家手牌
            player.hands.forEach((hand, handIndex) => {
                const handContainer = this._buildHandContainer(player, hand, playerIndex, handIndex);
                playerSection.appendChild(handContainer);
            });

            fragment.appendChild(playerSection);
        });

        // 清空现有内容并一次性添加新内容
        playersContainer.innerHTML = '';
        playersContainer.appendChild(fragment);
    }

    // 构建玩家统计元素
    _buildPlayerStatsElement(player) {
        const statsDiv = document.createElement('div');
        statsDiv.className = 'player-stats';

        // 创建第一行（筹码余额）- 让它独占一行
        const row1 = document.createElement('div');
        row1.className = 'player-stats-row';

        // 计算实际筹码余额
        const currentBets = player.bets.reduce((sum, bet) => sum + bet, 0);
        const realChips = this.gameState === 'betting' ? player.chips + currentBets : player.chips;

        row1.innerHTML = `
            <div class="stat-item chips"><span class="stat-label">筹码:</span> ${realChips}</div>
        `;

        // 创建第二行（胜场和负场）
        const row2 = document.createElement('div');
        row2.className = 'player-stats-row';
        row2.innerHTML = `
            <div class="stat-item win"><span class="stat-label">胜:</span> ${player.stats.wins}</div>
            <div class="stat-item loss"><span class="stat-label">负:</span> ${player.stats.losses}</div>
        `;

        // 创建第三行（胜率和总分）
        const row3 = document.createElement('div');
        row3.className = 'player-stats-row';
        row3.innerHTML = `
            <div class="stat-item rate"><span class="stat-label">胜率:</span> ${player.getWinRate()}%</div>
            <div class="stat-item score"><span class="stat-label">总收:</span> ${player.stats.totalScore.toFixed(1)}</div>
        `;

        // 添加三行到统计div
        statsDiv.appendChild(row1);
        statsDiv.appendChild(row2);
        statsDiv.appendChild(row3);

        return statsDiv;
    }

    // 构建手牌容器元素
    _buildHandContainer(player, hand, playerIndex, handIndex) {
        try {
            const handContainer = document.createElement('div');
            handContainer.className = 'hand-container';
            handContainer.id = `player-${playerIndex}-hand-${handIndex}`;

            // 先移除所有手牌容器的活跃状态
            document.querySelectorAll('.hand-container').forEach(container => {
                container.classList.remove('active');
            });

            // 只有在游戏状态为playing且是当前玩家的当前手牌时才添加active类
            if (this.gameState === 'playing' &&
                playerIndex === this.currentPlayerIndex &&
                handIndex === player.currentHandIndex &&
                player.gameState !== 'ended') {
                handContainer.classList.add('active');
            }

            // 检查手牌是否可分牌
            if (hand.length === 2 && !hand.doubled) {
                // 获取两张牌的点数或牌面
                if (hand[0] && hand[1]) {
                    const value1 = hand[0].getValue();
                    const value2 = hand[1].getValue();
                    const rank1 = hand[0].rank;
                    const rank2 = hand[1].rank;

                    // 检查是否可分牌（相同点数或AA）
                    if ((value1 === value2) || (rank1 === 'A' && rank2 === 'A')) {
                        // 检查玩家是否有足够的筹码
                        const betAmount = player.bets[handIndex] || 0;
                        if (betAmount > 0 && player.chips >= betAmount && player.hands.length < 4) {
                            // 添加可分牌样式类
                            handContainer.classList.add('can-split');
                        }
                    }
                }
            }

            // 手牌标题栏
            const handHeaderContainer = document.createElement('div');
            handHeaderContainer.className = 'hand-header';

            // 显示下注金额 - 移到前面，确保在左侧显示
            const betAmount = player.bets[handIndex] || 0;
            if (betAmount > 0) {
                const betDisplay = document.createElement('div');
                betDisplay.className = 'bet-display';

                // 检查是否为加倍或分牌手牌，添加相应标识
                let betValueHTML = `<span class="bet-value">${betAmount}`;

                // 获取当前手牌
                const currentHand = player.hands[handIndex];

                // 添加加倍标识
                if (currentHand && currentHand.doubled) {
                    betValueHTML += `<span class="bet-badge double-badge">加倍</span>`;
                }

                // 添加分牌标识
                if (currentHand && currentHand.split) {
                    betValueHTML += `<span class="bet-badge split-badge">分牌</span>`;
                }

                // 添加投降标识
                if (currentHand && currentHand.surrendered) {
                    betValueHTML += `<span class="bet-badge surrender-badge">投降</span>`;
                }

                betValueHTML += `</span>`;

                betDisplay.innerHTML = `<span class="bet-label">下注:</span> ${betValueHTML}`;
                handHeaderContainer.appendChild(betDisplay);
            }

            // 手牌编号
            if (player.hands.length > 1) {
                const handTitle = document.createElement('div');
                handTitle.className = 'hand-title';
                handTitle.textContent = `手牌 ${handIndex + 1}`;
                handHeaderContainer.appendChild(handTitle);
            }

            handContainer.appendChild(handHeaderContainer);

            // 卡牌容器 - 使用DocumentFragment优化
            const cardsContainer = document.createElement('div');
            cardsContainer.className = 'cards';

            // 创建DocumentFragment
            const cardsFragment = document.createDocumentFragment();

            // 添加所有卡牌
            hand.forEach(card => {
                if (card) {
                    const cardElement = card.createCardElement();
                    cardsFragment.appendChild(cardElement);
                }
            });

            // 将所有卡牌一次性添加到容器
            cardsContainer.appendChild(cardsFragment);
            handContainer.appendChild(cardsContainer);

            // 点数显示
            const pointsContainer = document.createElement('div');
            pointsContainer.className = 'points-container';

            const pointsDisplay = document.createElement('div');
            pointsDisplay.className = 'points-display';

            // 计算当前点数并显示
            const points = this.calculateHandValue(hand);
            const pointsText = this.getFormattedPoints(hand);
            pointsDisplay.textContent = pointsText;

            // 检查并添加爆牌样式
            if (points.value > 21) {
                pointsDisplay.classList.add('bust');
            }

            pointsContainer.appendChild(pointsDisplay);
            handContainer.appendChild(pointsContainer);

            const handInfo = hand.map(c => c.toString()).join(',');
            console.log(`构建手牌容器: P${playerIndex+1}:H${handIndex+1}, 牌=${handInfo}, 点数=${pointsText}(${points.value})`);

            return handContainer;
        } catch (error) {
            console.error('构建手牌容器时出错:', error);
            // 创建一个简单的回退容器
            const fallbackContainer = document.createElement('div');
            fallbackContainer.className = 'hand-container error';
            fallbackContainer.id = `player-${playerIndex}-hand-${handIndex}-error`;
            fallbackContainer.textContent = '手牌加载错误';
            return fallbackContainer;
        }
    }

    // 获取当前玩家
    getCurrentPlayer() {
        if (this.currentPlayerIndex < 0 || this.currentPlayerIndex >= this.players.length) {
            return null;
        }
        return this.players[this.currentPlayerIndex];
    }

    // 获取当前手牌
    getCurrentHand() {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            console.log('获取当前手牌：没有当前玩家');
            return null;
        }
        if (currentPlayer.currentHandIndex < 0 || currentPlayer.currentHandIndex >= currentPlayer.hands.length) {
            console.log('获取当前手牌：手牌索引无效', currentPlayer.currentHandIndex);
            return null;
        }
        return currentPlayer.hands[currentPlayer.currentHandIndex];
    }

    // 移动到下一个玩家或手牌 - 优化版
    moveToNext() {
        // 增加防重入锁，避免短时间内重复调用
        if (this._movingToNext) {
            if (window.Logger) {
                window.Logger.getLogger('Game').debug('已有moveToNext操作正在进行中，跳过重复调用');
            } else {
                console.log('已有moveToNext操作正在进行中，跳过重复调用');
            }
            return;
        }

        this._movingToNext = true;

        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            this._movingToNext = false;
            return;
        }

        // 记录当前玩家和手牌索引
        const currentPlayerIndex = this.currentPlayerIndex;
        const currentHandIndex = currentPlayer.currentHandIndex;
        const totalHands = currentPlayer.hands.length;

        // 如果当前手牌还有下一手
        if (currentPlayer.currentHandIndex < currentPlayer.hands.length - 1) {
            currentPlayer.currentHandIndex++;
        }
        // 如果是最后一手，移动到下一个玩家
        else {
            currentPlayer.currentHandIndex = 0;
            this.currentPlayerIndex++;
        }

        // 如果所有玩家都完成了操作，进入庄家回合
        if (this.currentPlayerIndex >= this.players.length) {
            this.gameState = 'dealer';
            this.currentPlayerIndex = 0;

            // 立即更新UI
            this.updateUI();

            // 使用自适应延迟时间开始庄家回合，但减少延迟时间
            const dealerStartDelay = window.isAutoMode ?
                this.getAdaptiveDelay(this.autoDelay * 0.7, 'dealerTurn') : // 减少70%的延迟时间
                100; // 手动模式下使用更短的延迟

            // 使用requestAnimationFrame确保UI更新后再执行下一步
            requestAnimationFrame(() => {
                setTimeout(() => {
                    this._movingToNext = false;
                    this.startDealerPlay();
                }, dealerStartDelay);
            });
            return;
        }

        // 获取新的当前手牌
        const currentHand = this.getCurrentHand();
        if (!currentHand) {
            console.error('移动后找不到当前手牌');
            this._movingToNext = false;
            return;
        }

        // 移除刚分牌后标记（如果存在）
        if (currentHand.hasOwnProperty('justSplit')) {
            delete currentHand.justSplit;
        }

        // 获取手牌状态
        const isSplitHand = currentHand.hasOwnProperty('split') && currentHand.split === true;
        const isAceSplitHand = currentHand.hasOwnProperty('isAceSplit') && currentHand.isAceSplit === true;
        const isAASplitAA = isAceSplitHand && currentHand.length === 2 &&
                         currentHand[0].rank === 'A' && currentHand[1].rank === 'A';

        // 判断当前玩家索引是否变化，如果变化了说明切换到了下一个玩家
        const playerChanged = currentPlayerIndex !== this.currentPlayerIndex;

        // 使用elementCache优化DOM操作
        const cache = window.PerformanceUtils ? window.PerformanceUtils.elementCache : null;

        // 如果玩家变化了，需要更新玩家的活跃状态
        if (playerChanged) {
            // 首先移除所有玩家的活跃状态和手牌活跃状态
            document.querySelectorAll('.player-section').forEach(section => {
                section.classList.remove('active');
                section.querySelectorAll('.hand-container').forEach(container => {
                    container.classList.remove('active');
                });
                // 移除所有当前操作指示器
                const indicator = section.querySelector('.current-player-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });

            // 获取上一个玩家容器和当前玩家容器
            const previousPlayerContainer = cache ?
                cache.get(`player-${currentPlayerIndex}`) :
                document.getElementById(`player-${currentPlayerIndex}`);

            const newPlayerContainer = cache ?
                cache.get(`player-${this.currentPlayerIndex}`) :
                document.getElementById(`player-${this.currentPlayerIndex}`);

            if (previousPlayerContainer) {
                // 确保移除上一个玩家的所有活跃状态
                previousPlayerContainer.classList.remove('active');
                const previousHandContainers = previousPlayerContainer.querySelectorAll('.hand-container');
                previousHandContainers.forEach(container => {
                    container.classList.remove('active');
                });
            }

            if (newPlayerContainer) {
                // 添加当前玩家的活跃状态
                newPlayerContainer.classList.add('active');

                // 添加当前手牌的活跃状态
                const currentHandContainer = newPlayerContainer.querySelector(`.hand-container:nth-child(${currentPlayer.currentHandIndex + 1})`);
                if (currentHandContainer) {
                    currentHandContainer.classList.add('active');
                }

                // 添加当前玩家的当前操作指示器
                if (this.gameState === 'playing' && currentPlayer.gameState !== 'ended') {
                    // 检查是否已存在指示器，如果存在则不重复添加
                    if (!newPlayerContainer.querySelector('.current-player-indicator')) {
                        const indicator = document.createElement('div');
                        indicator.className = 'current-player-indicator';
                        indicator.textContent = '当前操作';
                        newPlayerContainer.appendChild(indicator);
                    }
                }
            }
        } else {
            // 如果只是切换了手牌，只更新手牌的活跃状态
            const playerContainer = cache ?
                cache.get(`player-${this.currentPlayerIndex}`) :
                document.getElementById(`player-${this.currentPlayerIndex}`);

            if (playerContainer) {
                // 移除所有手牌的活跃状态
                const activeHands = playerContainer.querySelectorAll('.hand-container.active');
                activeHands.forEach(container => {
                    container.classList.remove('active');
                });

                // 添加当前手牌的活跃状态
                const currentHandContainer = playerContainer.querySelector(`.hand-container:nth-child(${currentPlayer.currentHandIndex + 1})`);
                if (currentHandContainer) {
                    currentHandContainer.classList.add('active');
                }
            }
        }

        // 立即更新UI，确保状态正确显示
        this.updateUI();

        // 计算当前手牌点数
        const handValueObj = this.calculateHandValue(currentHand);

        // 处理自动操作场景 - 优化版
        const handleSpecialCases = () => {
            // 手动模式下使用更短的延迟
            const manualModeDelay = 30; // 手动模式下使用更短的延迟

            // 检查手牌是否需要自动停牌
            if (currentHand && (currentHand.needAutoStand || (isAceSplitHand && !isAASplitAA && currentHand.length >= 2))) {
                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay * 0.7, 'stand') : manualModeDelay; // 减少70%的延迟时间

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        this._movingToNext = false;
                        if (this.gameState === 'playing') {
                            this.stand();
                        }
                    }, delay);
                });
                return true;
            }

            // 处理BlackJack自动移动
            if (currentHand && this.isBlackjack(currentHand)) {
                currentHand.isBlackjack = true;

                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay * 0.7, 'blackjack') : manualModeDelay; // 减少70%的延迟时间

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        this._movingToNext = false;
                        this.moveToNext();
                    }, delay);
                });
                return true;
            }

            // 处理21点自动移动
            if (currentHand && handValueObj.value === 21 && !this.isBlackjack(currentHand)) {
                currentHand.is21 = true;

                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay * 0.7, 'blackjack') : manualModeDelay; // 减少70%的延迟时间

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        this._movingToNext = false;
                        this.moveToNext();
                    }, delay);
                });
                return true;
            }

            // 处理爆牌自动移动
            if (currentHand && this.isBust(currentHand)) {
                currentHand.isBust = true;

                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay * 0.7, 'bust') : manualModeDelay; // 减少70%的延迟时间

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        this._movingToNext = false;
                        this.moveToNext();
                    }, delay);
                });
                return true;
            }

            // 对于AA分牌后的AA手牌，在自动模式下直接执行分牌
            if (window.isAutoMode && isAASplitAA) {
                const delay = this.getAdaptiveDelay(this.autoDelay * 0.7, 'split'); // 减少70%的延迟时间

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        this._movingToNext = false;
                        this.split();
                    }, delay);
                });
                return true;
            }

            return false;
        };

        // 处理特殊情况后，检查自动模式
        if (!handleSpecialCases()) {
            // 如果开启了自动模式并且没有特殊情况需要处理
            if (window.isAutoMode && currentHand && !currentHand.isBlackjack && !currentHand.is21 && !currentHand.isBust) {
                // 为不同情况设置不同的延迟，但减少延迟时间
                let situation = 'default';
                if (playerChanged) {
                    situation = 'playerChange';
                } else if (isSplitHand) {
                    situation = 'split';
                } else if (currentPlayer.hands.length > 1) {
                    // 多手牌情况，但没有变更玩家
                    situation = 'handChange';
                }

                // 计算延迟：增加手牌切换时的延迟
                const baseDelay = this.autoDelay * 0.7; // 基本延迟的70%
                // 对于玩家变化和分牌情况，增加额外延迟确保UI更新和状态同步
                const extraDelay = (playerChanged || isSplitHand) ? 100 : 0;
                const delay = this.getAdaptiveDelay(baseDelay, situation) + extraDelay;

                // 使用requestAnimationFrame确保UI更新后再执行下一步
                requestAnimationFrame(() => {
                    // 记录操作ID以便于调试
                    const operationId = Date.now() + Math.random().toString(36).substring(2, 5);
                    gameLogger.debug(`[${operationId}] 即将执行自动操作，延迟: ${delay}ms, 情况: ${situation}`);

                    setTimeout(() => {
                        this._movingToNext = false;
                        if (window.isAutoMode && this.gameState === 'playing') {
                            // 确保当前状态有效后再执行自动操作
                            const currentPlayerValid = this.getCurrentPlayer() === currentPlayer;
                            const currentHandValid = this.getCurrentHand() === currentHand;

                            if (currentPlayerValid && currentHandValid) {
                                gameLogger.debug(`[${operationId}] 执行自动操作，状态一致性检查通过`);
                                this.executeAutoAction();
                            } else {
                                gameLogger.warn(`[${operationId}] 取消自动操作，状态已变化`);
                            }
                        }
                    }, delay);
                });

                // 如果在后台模式，确保BackgroundRunner处于激活状态
                if (document.hidden && window.BackgroundRunner && typeof window.BackgroundRunner.forceActivate === 'function') {
                    window.BackgroundRunner.forceActivate();
                }
            } else {
                // 手动模式下立即释放锁，不使用setTimeout
                this._movingToNext = false;
            }
        }
    }

    // 更新按钮状态
    updateButtonStates() {
        // 首先移除所有可分牌提示标识
        this._removeAllSplitIndicators();

        // 获取当前玩家和手牌
        const currentPlayer = this.getCurrentPlayer();
        const currentHand = this.getCurrentHand();

        // 检查所有玩家的所有手牌，为可分牌的手牌添加提示标识
        this.players.forEach((player, playerIndex) => {
            player.hands.forEach((hand, handIndex) => {
                // 检查手牌是否可分牌
                if (hand.length === 2 && !hand.doubled) {
                    // 获取两张牌的点数或牌面
                    if (hand[0] && hand[1]) {
                        const value1 = hand[0].getValue();
                        const value2 = hand[1].getValue();
                        const rank1 = hand[0].rank;
                        const rank2 = hand[1].rank;

                        // 检查是否可分牌（相同点数或AA）
                        if ((value1 === value2) || (rank1 === 'A' && rank2 === 'A')) {
                            // 检查玩家是否有足够的筹码
                            const betAmount = player.bets[handIndex] || 0;
                            if (betAmount > 0 && player.chips >= betAmount && player.hands.length < 4) {
                                // 无论是否是当前手牌，都添加可分牌样式类
                                const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
                                const handContainer = document.getElementById(handContainerId);
                                if (handContainer) {
                                    handContainer.classList.add('can-split');
                                }

                                // 只有当前玩家的当前手牌才添加提示标识
                                if (this.gameState === 'playing' &&
                                    playerIndex === this.currentPlayerIndex &&
                                    handIndex === player.currentHandIndex) {
                                    // 添加可分牌提示标识
                                    this._addSplitIndicator(playerIndex, handIndex);
                                }
                            }
                        }
                    }
                }
            });
        });

        // 操作按钮列表
        const buttons = {
            'hit': document.getElementById('hit'),
            'stand': document.getElementById('stand'),
            'double': document.getElementById('double'),
            'split': document.getElementById('split'),
            'surrender': document.getElementById('surrender'),
            'add-player': document.getElementById('add-player'),
            'remove-player': document.getElementById('remove-player'),
            'player-count': document.getElementById('player-count'),
            'new-game': document.getElementById('new-game'),
        };

        // 检查是否允许在游戏中管理玩家
        const allowManagement = document.getElementById('allow-management')?.checked || false;

        try {
            // 获取当前手牌
            const currentHand = this.getCurrentHand();
            const currentPlayer = this.getCurrentPlayer();

            // 如果是下注阶段，禁用所有游戏操作按钮
            if (this.gameState === 'betting') {
                buttons['hit']?.setAttribute('disabled', 'disabled');
                buttons['stand']?.setAttribute('disabled', 'disabled');
                buttons['double']?.setAttribute('disabled', 'disabled');
                buttons['split']?.setAttribute('disabled', 'disabled');
                buttons['surrender']?.setAttribute('disabled', 'disabled');

                // 允许添加和移除玩家
                buttons['add-player']?.removeAttribute('disabled');
                buttons['remove-player']?.removeAttribute('disabled');
                buttons['player-count']?.removeAttribute('disabled');
                buttons['new-game']?.removeAttribute('disabled');
                return;
            }

            // 根据游戏状态和当前手牌情况更新按钮状态
            if (this.gameState === 'playing' && currentHand && currentPlayer) {
                // 添加和移除玩家按钮
                if (allowManagement) {
                    buttons['add-player']?.removeAttribute('disabled');
                    buttons['remove-player']?.removeAttribute('disabled');
                    buttons['player-count']?.removeAttribute('disabled');
                } else {
                    buttons['add-player']?.setAttribute('disabled', 'disabled');
                    buttons['remove-player']?.setAttribute('disabled', 'disabled');
                    buttons['player-count']?.setAttribute('disabled', 'disabled');
                }

                // 要牌和停牌按钮始终可用
                buttons['hit']?.removeAttribute('disabled');
                buttons['stand']?.removeAttribute('disabled');

                // 加倍按钮 - 只允许初始2张牌时加倍，分牌后的手牌也可以加倍
                const canDouble = currentHand.length === 2 && !currentHand.doubled;
                if (canDouble) {
                    // 当前玩家是否有足够的筹码进行加倍
                    const currentBet = currentPlayer.bets[currentPlayer.currentHandIndex] || 0;

                    if (currentBet > 0 && currentPlayer.chips >= currentBet) {
                        buttons['double']?.removeAttribute('disabled');
                        buttons['double']?.classList.remove('disabled');
                        gameLogger.debug('启用加倍按钮: 手牌=2, 未加倍, 筹码充足');
                    } else {
                        buttons['double']?.setAttribute('disabled', 'disabled');
                        buttons['double']?.classList.add('disabled');
                        gameLogger.debug('禁用加倍按钮: 筹码不足, 当前下注=' + currentBet + ', 玩家筹码=' + currentPlayer.chips);
                    }
                } else {
                    buttons['double']?.setAttribute('disabled', 'disabled');
                    buttons['double']?.classList.add('disabled');
                    if (currentHand.length !== 2) {
                        gameLogger.debug('禁用加倍按钮: 手牌长度=' + currentHand.length + ' (不是2张牌)');
                    } else if (currentHand.doubled) {
                        gameLogger.debug('禁用加倍按钮: 已经加倍过');
                    }
                }

                // 分牌按钮
                // 先检查手牌数量限制，避免不必要的canSplit调用
                if (currentPlayer.hands.length >= 4) {
                    buttons['split']?.setAttribute('disabled', 'disabled');
                    buttons['split']?.classList.add('disabled');
                    buttons['split']?.classList.remove('can-split'); // 移除霓虹特效
                    console.log('禁用分牌按钮: 已达到最大手牌数量限制(4手牌)');
                } else {
                    const canSplit = currentPlayer.canSplit() && currentHand.length === 2 && !currentHand.doubled;
                    if (canSplit) {
                        // 当前玩家是否有足够的筹码进行分牌
                        const currentBet = currentPlayer.bets[currentPlayer.currentHandIndex] || 0;

                        if (currentBet > 0 && currentPlayer.chips >= currentBet) {
                            buttons['split']?.removeAttribute('disabled');
                            buttons['split']?.classList.remove('disabled');
                            buttons['split']?.classList.add('can-split'); // 添加霓虹特效
                            console.log('启用分牌按钮: 手牌可分牌, 筹码充足, 添加霓虹特效');
                        } else {
                            buttons['split']?.setAttribute('disabled', 'disabled');
                            buttons['split']?.classList.add('disabled');
                            buttons['split']?.classList.remove('can-split'); // 移除霓虹特效
                            console.log('禁用分牌按钮: 筹码不足, 当前下注=' + currentBet + ', 玩家筹码=' + currentPlayer.chips);
                        }
                    } else {
                        buttons['split']?.setAttribute('disabled', 'disabled');
                        buttons['split']?.classList.add('disabled');
                        buttons['split']?.classList.remove('can-split'); // 移除霓虹特效
                        if (!currentPlayer.canSplit()) {
                            console.log('禁用分牌按钮: 手牌不可分牌');
                        } else if (currentHand.length !== 2) {
                            console.log('禁用分牌按钮: 手牌数量不为2');
                        } else if (currentHand.doubled) {
                            console.log('禁用分牌按钮: 已加倍不能分牌');
                        }
                    }
                }

                // 投降按钮
                if (currentHand.length === 2 && !currentHand.doubled && !currentHand.split) {
                    buttons['surrender']?.removeAttribute('disabled');
                    buttons['surrender']?.classList.remove('disabled');
                } else {
                    buttons['surrender']?.setAttribute('disabled', 'disabled');
                    buttons['surrender']?.classList.add('disabled');
                }
            } else {
                // 不是playing状态，禁用所有游戏操作按钮
                buttons['hit']?.setAttribute('disabled', 'disabled');
                buttons['stand']?.setAttribute('disabled', 'disabled');
                buttons['double']?.setAttribute('disabled', 'disabled');
                buttons['split']?.setAttribute('disabled', 'disabled');
                buttons['surrender']?.setAttribute('disabled', 'disabled');

                // 始终允许开始新游戏
                buttons['new-game']?.removeAttribute('disabled');

                // 如果是ended状态，允许添加和移除玩家
                if (this.gameState === 'ended') {
                    buttons['add-player']?.removeAttribute('disabled');
                    buttons['remove-player']?.removeAttribute('disabled');
                    buttons['player-count']?.removeAttribute('disabled');
                } else {
                    // 不是ended状态，禁用添加和移除玩家
                    buttons['add-player']?.setAttribute('disabled', 'disabled');
                    buttons['remove-player']?.setAttribute('disabled', 'disabled');
                    buttons['player-count']?.setAttribute('disabled', 'disabled');
                }
            }
        } catch (error) {
            console.error('更新按钮状态时出错:', error);
        }
    }

    // 结束游戏
    endGame() {
        console.log('游戏结束，当前下注记录:', this.lastBets);

        // 确保lastBets非空
        if (!this.lastBets || this.lastBets.length === 0) {
            console.warn('下注记录为空，可能影响重复下注功能');
        }

        // 重置游戏状态
        this.gameState = 'betting';
        this.currentPlayerIndex = 0;
        this.selectedChipValue = 0;

        // 记录每个玩家的当前筹码数
        const playerChips = this.players.map(player => ({
            name: player.name,
            chips: player.chips
        }));
        console.log('游戏结束前玩家筹码状态:', playerChips);

        // 清除所有玩家的下注状态和手牌
        this.players.forEach(player => {
            player.reset();
        });

        // 清除庄家的手牌
        this.dealerHand = [];

        // 保存游戏记录
        this.saveToHistory();

        // 开始新的一局
        this.startBettingPhase();

        console.log('游戏结束后保留的下注记录:', this.lastBets);

        // 验证玩家筹码是否正确保留
        this.players.forEach((player, index) => {
            if (index < playerChips.length && player.name === playerChips[index].name) {
                if (player.chips !== playerChips[index].chips) {
                    console.error(`警告：玩家${player.name}的筹码数量不一致！重置前:${playerChips[index].chips}, 重置后:${player.chips}`);
                    // 修正筹码数量
                    player.chips = playerChips[index].chips;
                } else {
                    console.log(`玩家${player.name}的筹码数量正确保留: ${player.chips}`);
                }
            }
        });
    }

    // 设置游戏状态
    setGameState(newState) {
        this.gameState = newState;
        console.log('游戏状态改变为:', newState);

        // 在状态改变时检查是否需要执行自动操作
        if (window.isAutoMode && newState === 'playing') {
            console.log('自动模式已开启，执行自动操作...');
            setTimeout(() => this.executeAutoAction(), 500);
        }

        this.updateUI();
    }

    // 添加玩家
    addPlayer(name = null) {
        const allowManagement = document.getElementById('allow-management');
        // 检查是否允许在游戏中管理玩家
        if (!allowManagement.checked && this.gameState !== 'ended' && this.gameState !== 'betting') {
            alert('只能在游戏结束后添加玩家！');
            return;
        }

        if (this.players.length >= 6) {
            alert('最多只能添加6位玩家！');
            return;
        }
        const playerName = name || `玩家${this.players.length + 1}`;
        const player = new Player(playerName);

        // 如果游戏正在进行中，将新玩家的状态设置为ended
        if (this.gameState === 'playing' || this.gameState === 'dealer') {
            player.gameState = 'ended';
            player.hands = [[]];  // 确保手牌为空
        }

        this.players.push(player);
        this.updateUI();
    }

    // 移除玩家
    removePlayer() {
        const allowManagement = document.getElementById('allow-management');
        // 检查是否允许在游戏中管理玩家
        if (!allowManagement.checked && this.gameState !== 'ended' && this.gameState !== 'betting') {
            alert('只能在游戏结束后移除玩家！');
            return;
        }

        if (this.players.length <= 1) {
            alert('至少需要保留1位玩家！');
            return;
        }
        this.players.pop();
        this.updateUI();
    }

    // 更新玩家数量
    updatePlayerCount(count) {
        const allowManagement = document.getElementById('allow-management');
        // 检查是否允许在游戏中管理玩家
        if (!allowManagement.checked && this.gameState !== 'ended' && this.gameState !== 'betting') {
            alert('只能在游戏结束后更改玩家数量！');
            return;
        }

        count = parseInt(count);
        while (this.players.length < count) {
            this.addPlayer();
        }
        while (this.players.length > count) {
            this.removePlayer();
        }
    }

    // 要牌
    hit() {
        if (this.gameState !== 'playing') {
            console.log('不是玩家回合，当前状态:', this.gameState);
            return;
        }

        // 增加防重入锁
        if (this._hittingCard) {
            console.log('已有hit操作正在进行中，跳过重复调用');
            return;
        }
        this._hittingCard = true;

        try {
            const currentPlayer = this.getCurrentPlayer();
            if (!currentPlayer) {
                console.log('没有当前玩家');
                this._hittingCard = false;
                return;
            }

            const currentHand = this.getCurrentHand();
            if (!currentHand) {
                console.log('没有当前手牌');
                this._hittingCard = false;
                return;
            }

            // A分牌后的手牌不允许要牌，包括AA
            if (currentHand.isAceSplit && currentHand.length >= 2) {
                console.log('A分牌后的手牌不能要牌，只能分牌或停牌');
                this._hittingCard = false;
                return;
            }

            // 检查剩余牌数，如果少于30张则洗牌
            this.checkDeckAndShuffle();

            // 从牌堆中抽一张牌
            const card = this.deck.deal();
            if (!card) {
                console.error('没有牌可抽了');
                this._hittingCard = false;
                return;
            }

            // 添加牌到当前手牌
            currentHand.push(card);
            console.log('玩家要了一张牌:', card.toString(), '当前手牌长度:', currentHand.length);

            // 计算当前点数
            const points = this.calculateHandValue(currentHand);
            console.log('玩家当前点数:', points.value, '是否软手:', points.isSoft);

            // === 全新方案：立即更新UI ===
            // 1. 先强制更新整个玩家区域，确保手牌容器存在
            this.updatePlayersDisplay();

            // 2. 然后对当前手牌进行更精确的更新
            const playerIndex = this.currentPlayerIndex;
            const handIndex = currentPlayer.currentHandIndex;
            const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
            const handContainer = document.getElementById(handContainerId);

            if (handContainer) {
                // 获取卡牌容器
                const cardsContainer = handContainer.querySelector('.cards');
                if (cardsContainer) {
                    // 清空现有卡牌
                    cardsContainer.innerHTML = '';

                    // 重新创建所有卡牌
                    currentHand.forEach(cardObj => {
                        if (cardObj) {
                            const cardElement = cardObj.createCardElement();
                            cardsContainer.appendChild(cardElement);
                        }
                    });

                    // 更新点数显示
                    const pointsDisplay = handContainer.querySelector('.points-display');
                    if (pointsDisplay) {
                        const pointsText = this.getFormattedPoints(currentHand);
                        pointsDisplay.textContent = pointsText;
                        pointsDisplay.classList.toggle('bust', points.value > 21);
                    }
                }
            }

            // 根据操作模式调整延迟
            // 手动模式下使用更短的延迟
            const delay = window.isAutoMode ?
                this.getAdaptiveDelay(this.autoDelay, 'hit') :
                100; // 手动模式下使用较短的延迟

            // 更新策略建议显示
            this.updateStrategyHint();

            // 更新按钮状态，确保加倍按钮在玩家有第三张牌时被禁用
            this.updateButtonStates();

            // 使用requestAnimationFrame确保UI更新后再执行后续操作
            requestAnimationFrame(() => {
                // 检查是否爆牌或达到21点
                if (points.value > 21) {
                    setTimeout(() => {
                        this._hittingCard = false;
                        this.moveToNext();
                    }, delay);
                } else if (points.value === 21) {
                    setTimeout(() => {
                        this._hittingCard = false;
                        this.stand();
                    }, delay);
                } else {
                    // 如果是A分牌后的手牌且刚补完一张牌，自动停牌
                    if (currentHand.isAceSplit && currentHand.length === 2) {
                        setTimeout(() => {
                            this._hittingCard = false;
                            this.stand();
                        }, delay);
                        return;
                    }

                    // 如果自动模式开启，继续执行自动操作
                    if (window.isAutoMode && this.gameState === 'playing') {
                        setTimeout(() => {
                            this._hittingCard = false;
                            if (window.isAutoMode && this.gameState === 'playing') {
                                this.executeAutoAction();
                            }
                        }, delay);
                    } else {
                        // 如果是手动模式，立即释放锁
                        this._hittingCard = false;
                    }
                }
            });
        } catch (error) {
            console.error('执行要牌操作时出错:', error);
            this._hittingCard = false;
        }
    }

    // 直接更新当前手牌显示，不依赖UI更新循环
    _directUpdateCurrentHand(player, hand, forceUpdate = false) {
        if (!player || !hand) return;

        try {
            const playerIndex = this.currentPlayerIndex;
            const handIndex = player.currentHandIndex;
            const containerId = `player-${playerIndex}-hand-${handIndex}`;

            // 1. 直接更新卡牌显示，添加forceUpdate参数
            this.updateOptimizedHandDisplay(containerId, hand, forceUpdate);

            // 2. 更新点数显示
            const handContainer = document.getElementById(containerId);
            if (handContainer) {
                const pointsDisplay = handContainer.querySelector('.points-display');
                if (pointsDisplay) {
                    const points = this.calculateHandValue(hand);
                    const pointsText = this.getFormattedPoints(hand);
                    pointsDisplay.textContent = pointsText;

                    // 更新爆牌状态
                    if (points.value > 21) {
                        pointsDisplay.classList.add('bust');
                    } else {
                        pointsDisplay.classList.remove('bust');
                    }

                    console.log(`更新手牌点数显示: ${pointsText} (实际值:${points.value}, 软手:${points.isSoft})`);
                }
            }
        } catch (error) {
            console.error('直接更新手牌显示时出错:', error);
        }
    }

    // 停牌 - 优化版
    stand() {
        if (this.gameState !== 'playing') {
            if (window.Logger) {
                window.Logger.getLogger('Game').debug('不是玩家回合，当前状态:', this.gameState);
            } else {
                console.log('不是玩家回合，当前状态:', this.gameState);
            }
            return;
        }

        // 增加防重入锁
        if (this._standing) {
            if (window.Logger) {
                window.Logger.getLogger('Game').debug('已有stand操作正在进行中，跳过重复调用');
            } else {
                console.log('已有stand操作正在进行中，跳过重复调用');
            }
            return;
        }
        this._standing = true;

        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            console.log('没有当前玩家');
            this._standing = false;
            return;
        }

        const currentHand = this.getCurrentHand();
        if (!currentHand) {
            console.log('没有当前手牌');
            this._standing = false;
            return;
        }

        // 快速更新UI状态
        this._directUpdateCurrentHand(currentPlayer, currentHand, true);

        // 根据模式使用不同的延迟时间
        // 手动模式下使用极短的延迟，自动模式下使用自适应延迟
        const delay = window.isAutoMode ?
            this.getAdaptiveDelay(this.autoDelay, 'stand') :
            50; // 手动模式下使用极短的延迟

        // 使用requestAnimationFrame确保UI更新后再移动到下一个玩家
        requestAnimationFrame(() => {
            setTimeout(() => {
                this._standing = false;
                this.moveToNext();
            }, delay);
        });
    }

    // 加倍
    double() {
        // 增加防重入锁，避免快速点击或自动模式下重复执行
        if (this._doubling) {
            gameLogger.debug('加倍操作正在进行中，跳过重复请求');
            return;
        }
        this._doubling = true;

        try {
            if (this.gameState !== 'playing') {
                gameLogger.debug('不是玩家回合，当前状态:', this.gameState);
                this._doubling = false;
                return;
            }

            const currentPlayer = this.getCurrentPlayer();
            if (!currentPlayer) {
                gameLogger.debug('没有当前玩家');
                this._doubling = false;
                return;
            }

            const currentHand = this.getCurrentHand();
            if (!currentHand) {
                gameLogger.debug('没有当前手牌');
                this._doubling = false;
                return;
            }

            // 检查手牌长度，只有两张牌才能加倍
            if (currentHand.length !== 2) {
                gameLogger.debug('只有初始的两张牌才能加倍');
                this._doubling = false;
                return;
            }

            // 分牌后的手牌也可以加倍，移除这个限制

            // 检查是否有足够的筹码
            const betAmount = currentPlayer.bets[currentPlayer.currentHandIndex];
            if (betAmount <= 0 || betAmount > currentPlayer.chips) {
                gameLogger.debug('没有足够的筹码加倍');
                this._doubling = false;
                return;
            }

            // 执行加倍
            if (!currentPlayer.doubleBet()) {
                gameLogger.debug('加倍失败');
                this._doubling = false;
                return;
            }

            // 标记手牌为加倍状态
            currentHand.doubled = true;

            // 更新统计信息
            currentPlayer.stats.doubles++;

            // 检查剩余牌数，如果少于30张则洗牌
            this.checkDeckAndShuffle();

            // 从牌堆中抽一张牌
            const card = this.deck.deal();
            if (!card) {
                gameLogger.error('没有牌可抽了');
                this._doubling = false;
                return;
            }

            currentHand.push(card);
            gameLogger.debug('玩家加倍后获得一张牌:', card);

            // === 全新方案：立即更新UI ===
            // 1. 先强制更新整个玩家区域，确保手牌容器存在
            this.updatePlayersDisplay();

            // 2. 然后对当前手牌进行更精确的更新
            const playerIndex = this.currentPlayerIndex;
            const handIndex = currentPlayer.currentHandIndex;
            const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
            const handContainer = document.getElementById(handContainerId);

            if (handContainer) {
                // 获取卡牌容器
                const cardsContainer = handContainer.querySelector('.cards');
                if (cardsContainer) {
                    // 清空现有卡牌
                    cardsContainer.innerHTML = '';

                    // 重新创建所有卡牌
                    currentHand.forEach(cardObj => {
                        if (cardObj) {
                            const cardElement = cardObj.createCardElement();
                            cardsContainer.appendChild(cardElement);
                        }
                    });

                    // 更新点数显示
                    const pointsDisplay = handContainer.querySelector('.points-display');
                    if (pointsDisplay) {
                        const pointsText = this.getFormattedPoints(currentHand);
                        pointsDisplay.textContent = pointsText;
                        const points = this.calculateHandValue(currentHand);
                        pointsDisplay.classList.toggle('bust', points.value > 21);
                    }

                    // 更新下注显示，反映加倍后的金额和加倍标识
                    const betDisplay = handContainer.querySelector('.bet-display');
                    if (betDisplay) {
                        // 创建新的下注显示HTML，包含加倍标识
                        let betValueHTML = `<span class="bet-value">${currentPlayer.bets[handIndex]}`;

                        // 添加加倍标识
                        betValueHTML += `<span class="bet-badge double-badge">加倍</span>`;

                        // 如果是分牌手牌，也添加分牌标识
                        if (currentHand.split) {
                            betValueHTML += `<span class="bet-badge split-badge">分牌</span>`;
                        }

                        // 如果是投降手牌，也添加投降标识
                        if (currentHand.surrendered) {
                            betValueHTML += `<span class="bet-badge surrender-badge">投降</span>`;
                        }

                        betValueHTML += `</span>`;

                        // 更新下注显示
                        betDisplay.innerHTML = `<span class="bet-label">下注:</span> ${betValueHTML}`;
                    }
                }
            }

            // 使用自适应延迟
            const delay = this.getAdaptiveDelay(this.autoDelay, 'double');

            // 立即移动到下一手牌
            setTimeout(() => {
                if (this.calculateHandValue(currentHand).value > 21) {
                    gameLogger.debug('加倍后爆牌了');
                    currentHand.isBust = true;
                }
                this.moveToNext();
                // 释放防重入锁
                this._doubling = false;
            }, delay);
        } catch (error) {
            // 确保在发生错误时也释放锁
            gameLogger.error('执行加倍操作时出错:', error);
            this._doubling = false;
        }
    }

    // 分牌
    split() {
        // 防重入锁，避免快速点击导致多次分牌
        if (this._splitting) {
            console.log('分牌操作正在进行中，跳过重复请求');
            return;
        }
        this._splitting = true;

        try {
            if (this.gameState !== 'playing') {
                console.log('不是玩家回合，当前状态:', this.gameState);
                this._splitting = false;
                return;
            }

            const currentPlayer = this.getCurrentPlayer();
            if (!currentPlayer) {
                console.log('没有当前玩家');
                this._splitting = false;
                return;
            }

            const currentHand = this.getCurrentHand();
            if (!currentHand) {
                console.log('没有当前手牌');
                this._splitting = false;
                return;
            }

            // 严格检查分牌次数限制(最多4手牌)
            if (currentPlayer.hands.length >= 4) {
                console.log('已达到最大分牌次数限制（4手牌）');
                // 显示提示信息
                this.showResultBanner('<span class="special">最多只能分为4手牌!</span>', 'special', false);
                setTimeout(() => {
                    const banner = document.getElementById('result-banner');
                    if (banner) banner.style.display = 'none';
                }, 2000);
                this._splitting = false;
                return;
            }

            // 检查是否可以分牌
            if (!currentPlayer.canSplit()) {
                console.log('当前手牌不能分牌');
                this._splitting = false;
                return;
            }

            // 检查是否有足够的筹码
            const betAmount = currentPlayer.bets[currentPlayer.currentHandIndex];
            if (betAmount <= 0 || betAmount > currentPlayer.chips) {
                console.log('没有足够的筹码分牌');
                this._splitting = false;
                return;
            }

            // 记录第一张牌是否是A
            const isAce = currentHand[0].rank === 'A';

            // 执行分牌
            if (!currentPlayer.splitBet()) {
                console.log('分牌失败');
                this._splitting = false;
                return;
            }

            // 创建新手牌
            const newHand = [];
            newHand.push(currentHand.pop());

            // 标记分牌状态
            currentHand.split = true;
            newHand.split = true;
            // 标记刚分牌的状态，避免连续快速分牌
            currentHand.justSplit = true;

            // 记录分牌操作，确保在下注历史中正确显示
            console.log(`玩家${currentPlayer.name}执行分牌操作，第一手牌和第二手牌都标记为split=true`);

            // 如果是A分牌，标记特殊状态
            if (isAce) {
                currentHand.isAceSplit = true;
                newHand.isAceSplit = true;
            }

            // 添加新手牌到玩家手牌列表的正确位置
            // 将新手牌插入到当前手牌的后面，而不是添加到末尾
            currentPlayer.hands.splice(currentPlayer.currentHandIndex + 1, 0, newHand);

            // 确保bets数组和initialBets数组也同步更新顺序
            currentPlayer.bets.splice(currentPlayer.currentHandIndex + 1, 0, currentPlayer.bets.pop());

            // 同样处理initialBets数组，确保它与bets数组保持同步
            if (currentPlayer.initialBets && currentPlayer.initialBets.length > currentPlayer.currentHandIndex + 1) {
                currentPlayer.initialBets.splice(
                    currentPlayer.currentHandIndex + 1,
                    0,
                    currentPlayer.initialBets.pop()
                );
            }

            // 更新统计信息
            currentPlayer.stats.splits++;

            // 检查剩余牌数，如果少于30张则洗牌
            this.checkDeckAndShuffle();

            // 强制重建UI以确保新的手牌容器被正确创建
            this.updatePlayersDisplay();

            // 更新按钮状态，确保分牌后的手牌在满足条件时也能显示"可分牌"提示标识
            this.updateButtonStates();

            // 计算分牌操作的基础延迟时间
            // 使用自适应延迟，确保分牌后的操作速度受游戏设置中自动模式速度的影响
            const splitBaseDelay = this.getAdaptiveDelay(window.isAutoMode ? this.autoDelay : 500, 'split');
            console.log('分牌操作基础延迟时间:', splitBaseDelay);

            // 为两手牌各发一张牌
            setTimeout(() => {
                // 先为当前手牌发一张牌
                const card1 = this.deck.deal();
                if (card1) {
                    // 确保牌是明牌
                    card1.setHidden(false);
                    currentHand.push(card1);
                    console.log('分牌后第一手牌获得一张牌:', card1);

                    // 强制更新第一手牌显示
                    this._forceUpdateSplitHand(this.currentPlayerIndex, currentPlayer.currentHandIndex, currentHand);

                    // 更新按钮状态，确保分牌后的手牌在满足条件时也能显示"可分牌"提示标识
                    this.updateButtonStates();

                    // 检查是否可以继续分牌
                    if (currentHand.length === 2 && !currentHand.doubled) {
                        const card1 = currentHand[0];
                        const card2 = currentHand[1];
                        if (card1 && card2 && card1.getValue() === card2.getValue()) {
                            // 检查玩家是否有足够的筹码
                            const betAmount = currentPlayer.bets[currentPlayer.currentHandIndex] || 0;
                            if (betAmount > 0 && currentPlayer.chips >= betAmount && currentPlayer.hands.length < 4) {
                                // 添加可分牌样式类到手牌容器
                                const handContainerId = `player-${this.currentPlayerIndex}-hand-${currentPlayer.currentHandIndex}`;
                                const handContainer = document.getElementById(handContainerId);
                                if (handContainer) {
                                    handContainer.classList.add('can-split');
                                }

                                // 添加可分牌提示标识到分牌按钮上方
                                this._addSplitIndicator(this.currentPlayerIndex, currentPlayer.currentHandIndex);
                            }
                        }
                    }
                }

                // 如果是A分牌，检查是否需要自动停牌
                const autoStandFirst = isAce && currentHand.length === 2;
                // 计算手牌点数
                const handValue = this.calculateHandValue(currentHand).value;

                // 使用自适应延迟
                const firstHandDelay = this.getAdaptiveDelay(splitBaseDelay * 0.6, 'split');

                setTimeout(() => {
                    // 释放分牌锁
                    this._splitting = false;

                    // 如果是A分牌且已经发了第二张牌，自动停牌
                    if (autoStandFirst) {
                        console.log('A分牌后自动停牌');
                        this.stand();
                    } else if (handValue === 21) {
                        console.log('分牌后第一手牌达到21点，自动停牌');
                        this.stand();
                    } else if (window.isAutoMode) {
                        // 在自动模式下，使用策略表决定操作
                        console.log('分牌后第一手牌使用策略表决定操作');
                        this.executeAutoAction();
                    }
                }, firstHandDelay);
            }, this.getAdaptiveDelay(splitBaseDelay * 0.5, 'split'));

            // 为第二手牌发一张牌
            setTimeout(() => {
                // 获取正确的下一手牌（可能不是最后一个）
                const nextHand = currentPlayer.hands[currentPlayer.currentHandIndex + 1];
                if (!nextHand) {
                    console.error('找不到下一手牌');
                    return;
                }

                const card2 = this.deck.deal();
                if (card2) {
                    // 确保牌是明牌
                    card2.setHidden(false);
                    nextHand.push(card2);
                    console.log('分牌后第二手牌获得一张牌:', card2);

                    // 强制更新第二手牌显示
                    this._forceUpdateSplitHand(this.currentPlayerIndex, currentPlayer.currentHandIndex + 1, nextHand);

                    // 更新按钮状态，确保分牌后的手牌在满足条件时也能显示"可分牌"提示标识
                    this.updateButtonStates();

                    // 检查第二手牌是否可以继续分牌
                    if (nextHand.length === 2 && !nextHand.doubled) {
                        const card1 = nextHand[0];
                        const card2 = nextHand[1];
                        if (card1 && card2 && card1.getValue() === card2.getValue()) {
                            // 检查玩家是否有足够的筹码
                            const betAmount = currentPlayer.bets[currentPlayer.currentHandIndex + 1] || 0;
                            if (betAmount > 0 && currentPlayer.chips >= betAmount && currentPlayer.hands.length < 4) {
                                // 添加可分牌样式类到手牌容器
                                const handContainerId = `player-${this.currentPlayerIndex}-hand-${currentPlayer.currentHandIndex + 1}`;
                                const handContainer = document.getElementById(handContainerId);
                                if (handContainer) {
                                    handContainer.classList.add('can-split');
                                }

                                // 当前手牌是第一手，所以不需要为第二手牌添加提示标识
                                // 只有当玩家操作到第二手牌时才会添加提示标识
                            }
                        }
                    }
                }

                // 如果是A分牌，检查是否需要自动停牌
                const autoStandSecond = isAce && nextHand.length === 2;

                // 使用自适应延迟
                const secondHandDelay = this.getAdaptiveDelay(splitBaseDelay * 0.4, 'split');

                setTimeout(() => {
                    // 当前手牌是第一手，所以我们不需要为第二手牌调用stand
                    // 只在UI上显示相关信息
                    if (autoStandSecond) {
                        console.log('A分牌后第二手牌将自动停牌');
                    } else if (this.calculateHandValue(nextHand).value === 21) {
                        console.log('分牌后第二手牌达到21点，将自动停牌');
                    }
                }, secondHandDelay);
            }, this.getAdaptiveDelay(splitBaseDelay * 0.7, 'split'));
        } catch (error) {
            console.error('分牌操作出错:', error);
            this._splitting = false;
        }
    }

    // 投降
    surrender() {
        // 增加防重入锁，避免快速点击或自动模式下重复执行
        if (this._surrendering) {
            console.log('投降操作正在进行中，跳过重复请求');
            return;
        }
        this._surrendering = true;

        try {
            if (this.gameState !== 'playing') {
                console.log('不是玩家回合，当前状态:', this.gameState);
                this._surrendering = false;
                return;
            }

            const currentPlayer = this.getCurrentPlayer();
            if (!currentPlayer) {
                console.log('没有当前玩家');
                this._surrendering = false;
                return;
            }

            const currentHand = this.getCurrentHand();
            if (!currentHand) {
                console.log('没有当前手牌');
                this._surrendering = false;
                return;
            }

            // 检查手牌长度，只有两张牌才能投降
            if (currentHand.length !== 2) {
                console.log('只有初始的两张牌才能投降');
                this._surrendering = false;
                return;
            }

            // 检查是否分牌后的手牌，分牌后不能投降
            if (currentHand.split) {
                console.log('分牌后不能投降');
                this._surrendering = false;
                return;
            }

            // 在自动模式下，验证投降条件是否符合基本策略
            if (window.isAutoMode) {
                const dealerUpCard = this.dealerHand[0];
                if (!dealerUpCard) {
                    console.log('无法获取庄家明牌，不能投降');
                    this._surrendering = false;
                    return;
                }

                // 使用getAutoAction函数获取策略建议
                if (typeof window.getAutoAction === 'function') {
                    const action = window.getAutoAction(currentHand, dealerUpCard, this);

                    // 只有当策略建议是投降(R)时才允许投降
                    if (action !== 'R') {
                        console.warn(`自动模式下策略不建议投降: 策略建议=${action}, 庄家牌=${dealerUpCard.rank}${dealerUpCard.suit}`);
                        this._surrendering = false;
                        return;
                    }

                    console.log(`确认有效的投降策略: 策略建议=${action}, 庄家牌=${dealerUpCard.rank}${dealerUpCard.suit}`);
                } else {
                    // 如果getAutoAction函数不可用，使用策略表查询
                    console.warn('自动模式下无法获取策略建议，不允许投降');
                    this._surrendering = false;
                    return;
                }
            }

            // 标记投降状态
            currentHand.surrendered = true;

            // 立即更新投降标识显示
            const playerIndex = this.currentPlayerIndex;
            const handIndex = currentPlayer.currentHandIndex;
            const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
            const handContainer = document.getElementById(handContainerId);

            if (handContainer) {
                // 获取下注显示区域
                const betDisplay = handContainer.querySelector('.bet-display');
                if (betDisplay) {
                    // 创建新的下注显示HTML，包含投降标识
                    const betAmount = currentPlayer.bets[handIndex] || 0;
                    let betValueHTML = `<span class="bet-value">${betAmount}`;

                    // 添加投降标识
                    betValueHTML += `<span class="bet-badge surrender-badge">投降</span>`;

                    // 更新下注显示
                    betDisplay.innerHTML = `<span class="bet-label">下注:</span> ${betValueHTML}</span>`;
                }
            }

            // 使用较短的固定延迟，加快移动到下一手牌的速度
            const delay = 100; // 使用固定的短延迟，而不是自适应延迟

            // 移动到下一个玩家
            setTimeout(() => {
                this.moveToNext();
                // 释放防重入锁
                this._surrendering = false;
            }, delay);
        } catch (error) {
            // 确保在发生错误时也释放锁
            console.error('执行投降操作时出错:', error);
            this._surrendering = false;
        }
    }

    // 发牌给指定手牌
    dealCard(hand) {
        if (!hand) {
            console.log('发牌错误：手牌为空');
            return;
        }

        // 检查剩余牌数，如果少于30张则洗牌
        this.checkDeckAndShuffle();

        const card = this.deck.deal();
        if (!card) {
            console.error('发牌错误：没有牌可发了');
            return;
        }

        // 确保所有玩家的牌都是明牌
        card.setHidden(false);

        console.log('发牌:', card);
        hand.push(card);

        // 检查是否爆牌或达到21点
        const points = this.calculateHandValue(hand);
        const currentPlayer = this.getCurrentPlayer();

        // === 全新方案：立即更新UI ===
        // 1. 先强制更新整个玩家区域，确保所有手牌容器存在
        this.updatePlayersDisplay();

        // 2. 找到对应的手牌并更新
        if (currentPlayer && this.gameState === 'playing') {
            // 遍历所有玩家找到匹配的手牌
            for (let playerIndex = 0; playerIndex < this.players.length; playerIndex++) {
                const player = this.players[playerIndex];

                for (let handIndex = 0; handIndex < player.hands.length; handIndex++) {
                    if (player.hands[handIndex] === hand) {
                        // 找到匹配的手牌，更新显示
                        const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
                        const handContainer = document.getElementById(handContainerId);

                        if (handContainer) {
                            // 获取卡牌容器
                            const cardsContainer = handContainer.querySelector('.cards');
                            if (cardsContainer) {
                                // 清空现有卡牌
                                cardsContainer.innerHTML = '';

                                // 重新创建所有卡牌
                                hand.forEach(cardObj => {
                                    if (cardObj) {
                                        const cardElement = cardObj.createCardElement();
                                        cardsContainer.appendChild(cardElement);
                                    }
                                });

                                // 更新点数显示
                                const pointsDisplay = handContainer.querySelector('.points-display');
                                if (pointsDisplay) {
                                    const pointsText = this.getFormattedPoints(hand);
                                    pointsDisplay.textContent = pointsText;
                                    pointsDisplay.classList.toggle('bust', points.value > 21);
                                }
                            }
                        }

                        break; // 找到匹配手牌后跳出内层循环
                    }
                }
            }
        }

        // 更新策略建议显示
        this.updateStrategyHint();

        if (points.value > 21) {
            hand.isBust = true;
            console.log('爆牌了，点数:', points.value);
            // 如果是玩家的手牌，自动进入下一手
            if (currentPlayer && currentPlayer.type === 'player') {
                // 使用自适应延迟
                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay, 'bust') :
                    200; // 手动模式下使用更短的延迟
                setTimeout(() => this.moveToNext(), delay);
            }
        } else if (points.value === 21) {
            // 如果是玩家的手牌且是21点，自动停牌
            if (currentPlayer && this.gameState === 'playing') {
                if (this.isBlackjack(hand)) {
                    hand.isBlackjack = true;
                    console.log('玩家获得BlackJack！自动停牌');
                } else {
                    console.log('玩家到达21点！自动停牌');
                }
                // 使用自适应延迟
                const delay = window.isAutoMode ?
                    this.getAdaptiveDelay(this.autoDelay, 'blackjack') :
                    200; // 手动模式下使用更短的延迟
                setTimeout(() => this.stand(), delay);
            }
        }

        return card;
    }

    // 设置自动模式延迟
    setAutoDelay(delay) {
        // 确保延迟时间是100的倍数
        const normalizedDelay = Math.round(delay / 100) * 100;
        // 设置延迟范围为300-1000ms，与UI滑块一致
        this.autoDelay = Math.max(300, Math.min(1000, normalizedDelay));
        console.log('设置自动延迟为：', this.autoDelay, 'ms');

        // 更新所有使用自动延迟的UI元素
        const autoDelayDisplay = document.getElementById('auto-delay-display');
        if (autoDelayDisplay) {
            autoDelayDisplay.textContent = this.autoDelay;
        }
    }

    // 保存已完成的游戏到历史记录
    saveToHistory() {
        try {
            // 如果当前游戏状态不是ended，不保存
            if (this.gameState !== 'ended') {
                console.log('游戏尚未结束，不保存历史记录');
                return;
            }

            // 如果没有玩家，不保存
            if (!this.players || this.players.length === 0) {
                console.log('没有玩家，不保存历史记录');
                return;
            }

            // 如果玩家没有手牌，不保存
            if (!this.players[0].hands || this.players[0].hands.length === 0) {
                console.log('玩家没有手牌，不保存历史记录');
                return;
            }

            // 确保每个手牌都有结果状态信息
            const dealerValue = this.calculateHandValue(this.dealerHand);
            const dealerBust = this.isBust(this.dealerHand);
            const dealerBlackjack = this.isBlackjack(this.dealerHand);

            this.players.forEach((player, playerIndex) => {
                player.hands.forEach((hand, handIndex) => {
                    // 如果手牌没有resultState，计算并添加
                    if (!hand.resultState) {
                        console.log(`为玩家${playerIndex}手牌${handIndex}添加结果状态信息`);

                        // 获取当前手牌的下注金额和初始下注
                        const betAmount = player.initialBets[handIndex] || 0;
                        const isDoubleDown = hand.doubled;
                        const isSplitHand = hand.split;
                        const playerValue = this.calculateHandValue(hand);
                        const playerBust = this.isBust(hand);
                        const playerBlackjack = this.isBlackjack(hand);

                        // 计算结果文本和类型
                        let result = '';
                        let resultClass = '';
                        let winAmount = hand.profit !== undefined ? hand.profit : 0;

                        if (hand.surrendered) {
                            result = '已投降';
                            resultClass = 'special';
                            winAmount = -betAmount * 0.5;
                        } else if (playerBust) {
                            result = '爆牌！';
                            resultClass = 'special';
                            winAmount = -betAmount * (isDoubleDown ? 2 : 1);
                        } else if (dealerBust) {
                            result = '庄家爆牌！胜利！';
                            resultClass = 'win';
                            winAmount = betAmount * (isDoubleDown ? 2 : 1);
                        } else if (playerBlackjack && !dealerBlackjack && !isSplitHand) {
                            result = 'BlackJack！胜利！';
                            resultClass = 'win';
                            winAmount = betAmount * 1.5;
                        } else if (dealerBlackjack) {
                            result = '庄家BlackJack！';
                            resultClass = 'lose';
                            winAmount = -betAmount * (isDoubleDown ? 2 : 1);
                        } else if (playerValue.value > dealerValue.value) {
                            result = playerValue.value === 21 ? '21点！胜利！' : '胜利！';
                            resultClass = 'win';
                            winAmount = betAmount * (isDoubleDown ? 2 : 1);
                        } else if (playerValue.value < dealerValue.value) {
                            result = '失败！';
                            resultClass = 'lose';
                            winAmount = -betAmount * (isDoubleDown ? 2 : 1);
                        } else {
                            result = '平局！';
                            resultClass = 'push';
                            winAmount = 0;
                        }

                        // 设置手牌的结果状态
                        hand.resultState = {
                            result: result,
                            resultClass: resultClass,
                            winAmount: winAmount
                        };

                        // 确保手牌的profit信息与winAmount一致
                        hand.profit = winAmount;
                    }
                });
            });

            // 保存游戏状态到历史记录
            this.gameHistory.addRecord(this.createGameState());
            console.log('成功保存游戏历史记录');
        } catch (error) {
            console.error('保存游戏历史时出错:', error);
        }
    }

    // 创建游戏状态对象
    createGameState() {
        return {
            players: this.players.map(player => ({
                name: player.name,
                hands: player.hands.map((hand, handIndex) => {
                    // 创建一个新的手牌数组，包含所有卡牌
                    const handCards = hand.map(card => ({
                        suit: card.suit,
                        rank: card.rank,
                        hidden: card.hidden
                    }));

                    // 计算该手牌的盈亏金额
                    let handProfit = 0;
                    const betAmount = player.bets[handIndex] || 0;
                    const originalBetAmount = player.initialBets[handIndex] || 0;

                    if (hand.surrendered) {
                        handProfit = -originalBetAmount / 2;
                    } else if (this.gameState === 'ended') {
                        // 如果已经结算，则记录实际盈亏
                        if (betAmount === 0) {
                            // 下注为0表示已经结算且输掉了所有筹码
                            handProfit = -(hand.doubled ? originalBetAmount * 2 : originalBetAmount);
                        } else {
                            // 否则通过下注和总盈亏计算当前手牌的盈亏
                            handProfit = originalBetAmount !== betAmount ? betAmount - originalBetAmount : 0;
                        }
                    }

                    // 返回包含卡牌和手牌信息的对象
                    return {
                        cards: handCards,
                        surrendered: hand.surrendered || false,
                        doubled: hand.doubled || false,
                        split: hand.split || false,
                        profit: handProfit,
                        // 保存结果状态信息
                        resultState: hand.resultState || null
                    };
                }),
                currentHandIndex: player.currentHandIndex,
                gameState: player.gameState,
                stats: { ...player.stats },
                // 保存筹码和下注信息
                chips: player.chips,
                bets: [...player.bets],
                initialBets: [...player.initialBets],
                betStatus: player.betStatus
            })),
            dealerHand: this.dealerHand.map(card => ({
                suit: card.suit,
                rank: card.rank,
                hidden: card.hidden
            })),
            gameState: this.gameState,
            currentPlayerIndex: this.currentPlayerIndex,
            deck: {
                numberOfDecks: this.deck.numberOfDecks,
                cards: this.deck.cards.map(card => ({
                    suit: card.suit,
                    rank: card.rank,
                    hidden: card.hidden
                })),
                usedCards: this.deck.usedCards.map(card => ({
                    suit: card.suit,
                    rank: card.rank,
                    hidden: card.hidden
                }))
            },
            timestamp: new Date().toISOString()
        };
    }

    // 更新测试模式UI
    updateTestModeUI() {
        const testModeControls = document.getElementById('test-mode-controls');
        if (testModeControls) {
            testModeControls.style.display = this.isTestMode ? 'block' : 'none';
        }
    }

    // 添加可分牌提示标识
    _addSplitIndicator(playerIndex, handIndex) {
        try {
            // 查找对应的手牌容器
            const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
            const handContainer = document.getElementById(handContainerId);

            if (!handContainer) {
                console.warn(`找不到手牌容器: ${handContainerId}`);
                return;
            }

            // 添加可分牌样式类到手牌容器
            handContainer.classList.add('can-split');

            // 查找分牌按钮
            const splitButton = document.getElementById('split');
            if (!splitButton) {
                console.warn('找不到分牌按钮');
                return;
            }

            // 给分牌按钮添加可分牌样式类，激活霓虹特效
            splitButton.classList.add('can-split');

            // 检查是否已经有提示标识
            let indicator = document.querySelector('.can-split-indicator');
            if (indicator) {
                // 已存在提示标识，不需要重复添加
                return;
            }

            // 创建提示标识
            indicator = document.createElement('div');
            indicator.className = 'can-split-indicator';

            // 添加到分牌按钮上方
            splitButton.parentNode.insertBefore(indicator, splitButton);

            // 确保指示器水平居中于分牌按钮
            const buttonRect = splitButton.getBoundingClientRect();
            const controlsRect = splitButton.parentNode.getBoundingClientRect();

            // 计算相对于父容器的中心位置
            const leftPosition = buttonRect.left - controlsRect.left + buttonRect.width / 2;
            indicator.style.left = `${leftPosition}px`;
            indicator.style.transform = 'translateX(-50%)';

            // 确保指示器在按钮上方
            indicator.style.top = '-30px';
            indicator.style.position = 'absolute';

            console.log(`为分牌按钮添加可分牌提示标识，对应手牌 ${handContainerId}`);
        } catch (error) {
            console.error('添加可分牌提示标识时出错:', error);
        }
    }

    // 移除所有可分牌提示标识
    _removeAllSplitIndicators() {
        try {
            // 移除所有提示标识
            document.querySelectorAll('.can-split-indicator').forEach(indicator => {
                indicator.remove();
            });

            // 移除分牌按钮的霓虹特效和所有可分牌样式类
            const splitBtn = document.getElementById('split');
            if (splitBtn) {
                splitBtn.classList.remove('can-split');

                // 移除分牌按钮上的提示标识（兼容旧版本）
                const indicator = splitBtn.querySelector('.split-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            // 移除所有可分牌样式类
            document.querySelectorAll('.hand-container.can-split').forEach(container => {
                container.classList.remove('can-split');
            });

            console.log('移除所有可分牌提示标识');
        } catch (error) {
            console.error('移除可分牌提示标识时出错:', error);
        }
    }

    // 设置自定义手牌
    setCustomHand(playerIndex, handIndex, cards) {
        if (!this.isTestMode) {
            console.log('必须在测试模式下才能设置自定义手牌');
            return;
        }

        if (playerIndex < 0 || playerIndex >= this.players.length) {
            console.log('无效的玩家索引');
            return;
        }

        const player = this.players[playerIndex];

        // 确保玩家有足够的手牌数组容量
        while (player.hands.length <= handIndex) {
            player.hands.push([]);
            // 同时确保bets数组也同步扩展
            if (player.bets.length <= handIndex) {
                player.bets.push(0);
            }
        }

        // 清空当前手牌
        player.hands[handIndex] = [];

        // 添加新的手牌
        cards.forEach(cardInfo => {
            const card = new Card(cardInfo.suit, cardInfo.rank);
            player.hands[handIndex].push(card);
        });

        this.updateUI();
        console.log(`已为玩家${playerIndex + 1}的第${handIndex + 1}手牌设置自定义牌`);
    }

    // 设置庄家手牌
    setDealerHand(cards) {
        if (!this.isTestMode) {
            console.log('必须在测试模式下才能设置庄家手牌');
            return;
        }

        // 清空当前手牌
        this.dealerHand = [];

        // 添加新的手牌
        cards.forEach(cardInfo => {
            const card = new Card(cardInfo.suit, cardInfo.rank);
            this.dealerHand.push(card);
        });

        this.updateUI();
        console.log('已设置庄家自定义手牌');
    }

    // 更新策略建议
    updateStrategyHint() {
        try {
            const hintText = document.getElementById('strategy-hint-text');
            const strategyHint = document.getElementById('strategy-hint');
            const strategySwitch = document.getElementById('strategy-hint-switch');
            if (!hintText || !strategyHint || !strategySwitch) return;

            // 根据开关状态控制显示
            strategyHint.style.display = strategySwitch.checked ? 'block' : 'none';
            if (!strategySwitch.checked) return;

            // 如果不是玩家回合，显示等待信息
            if (this.gameState !== 'playing') {
                hintText.textContent = '等待游戏开始...';
                hintText.className = '';
                return;
            }

            const currentHand = this.getCurrentHand();
            if (!currentHand) {
                hintText.textContent = '等待发牌...';
                hintText.className = '';
                return;
            }

            // 如果已经爆牌
            if (this.calculateHandValue(currentHand).value > 21) {
                hintText.textContent = '已爆牌';
                hintText.className = 'bust';
                return;
            }

            // 如果已经21点
            if (this.calculateHandValue(currentHand).value === 21) {
                hintText.textContent = '已达21点';
                hintText.className = 'stand';
                return;
            }

            // 使用autoStrategy的逻辑获取建议
            const dealerUpCard = this.dealerHand[0];

            // 检查庄家明牌是否存在
            if (!dealerUpCard) {
                gameLogger.warn('庄家明牌不存在，无法提供策略建议');
                hintText.textContent = '无法提供建议';
                return;
            }

            // 确保window.getAutoAction函数存在
            if (typeof window.getAutoAction !== 'function') {
                gameLogger.error('策略函数不存在，无法提供建议');
                hintText.textContent = '策略系统错误';
                return;
            }

            // 调用带错误处理的函数获取策略
            const action = window.getAutoAction(currentHand, dealerUpCard, this);
            gameLogger.debug('策略建议:', action);

            // 将操作代码转换为中文建议
            let hint = '';
            switch (action) {
                case 'H':  // Hit
                    hint = '要牌';
                    break;
                case 'S':  // Stand
                    hint = '停牌';
                    hintText.classList.add('stand');
                    break;
                case 'D':  // Double
                    // 如果手牌超过2张，加倍选项不可用，改为要牌
                    if (currentHand.length > 2) {
                        hint = '要牌 (无法加倍)';
                    } else {
                        hint = '加倍';
                        hintText.classList.add('double');
                    }
                    break;
                case 'P':  // Split
                    // 如果手牌超过2张，分牌选项不可用
                    if (currentHand.length > 2) {
                        hint = '要牌 (无法分牌)';
                    } else {
                        hint = '分牌';
                        hintText.classList.add('split');
                    }
                    break;
                case 'R':  // Surrender
                    // 如果手牌超过2张，投降选项不可用
                    if (currentHand.length > 2) {
                        hint = '要牌 (无法投降)';
                    } else {
                        hint = '投降';
                        hintText.classList.add('surrender');
                    }
                    break;
                default:
                    hint = '要牌';
            }

            // 更新显示
            hintText.textContent = `建议: ${hint}`;

            // 根据建议类型设置样式
            hintText.className = '';
            if (hint.includes('停牌')) {
                hintText.classList.add('stand');
            } else if (hint.includes('加倍')) {
                hintText.classList.add('double');
            } else if (hint.includes('分牌')) {
                hintText.classList.add('split');
            } else if (hint.includes('投降')) {
                hintText.classList.add('surrender');
            }
        } catch (error) {
            gameLogger.error('更新策略建议时出错:', error);
            const hintText = document.getElementById('strategy-hint-text');
            if (hintText) {
                hintText.textContent = '策略计算错误';
            }
        }
    }

    // 检查牌库剩余牌数，如果少于30张则洗牌
    checkDeckAndShuffle() {
        // 检查是否需要根据渗透率洗牌
        if (this.deck.shouldShuffleByPenetration()) {
            console.log(`根据渗透率(${this.deck.getPenetrationRateText()})触发洗牌`);
            this.deck.shuffle();
            this.updateDeckInfo();
        } else if (this.deck.getRemainingCards() < 30) {
            console.log('剩余牌数不足30张，重新洗牌');
            this.deck.shuffle();
            this.updateDeckInfo();
        }
    }

    // 初始化游戏
    init() {
        // 重置游戏状态
        this.gameState = 'betting';

        // 确保dealerCardDelay属性初始化
        if (!this.dealerCardDelay) {
            this.dealerCardDelay = 500;
        }

        // 尝试从设置中读取"开始新游戏时重置统计"选项的状态
        const resetStatsElement = document.getElementById('reset-stats');
        if (resetStatsElement) {
            this.resetStatsOnNewGame = resetStatsElement.checked;
            console.log('初始化：开始新游戏时重置统计 =', this.resetStatsOnNewGame);
        }

        // 初始化暂停自动模式按钮状态
        const pauseAutoBtn = document.getElementById('pause-auto');
        if (pauseAutoBtn) {
            // 根据自动模式状态设置显示与否
            pauseAutoBtn.style.display = window.isAutoMode ? 'inline-block' : 'none';
            pauseAutoBtn.textContent = this.isAutoPaused ? '继续自动' : '暂停自动';

            // 不再添加点击事件处理，因为在构造函数中已经添加过
        }

        // 绑定游戏按钮
        document.getElementById('hit').addEventListener('click', () => this.hit());
        document.getElementById('stand').addEventListener('click', () => this.stand());
        document.getElementById('double').addEventListener('click', () => this.double());
        document.getElementById('split').addEventListener('click', () => this.split());
        document.getElementById('surrender').addEventListener('click', () => this.surrender());
        document.getElementById('new-game').addEventListener('click', () => {
            const deckCount = parseInt(document.getElementById('deck-number').value);

            // 检查是否启用了"开始新游戏时重置统计"选项
            const shouldResetStats = this.resetStatsOnNewGame !== undefined ? this.resetStatsOnNewGame : true; // 默认为true

            // 当用户手动点击"开始新游戏"按钮时，重置玩家和牌库，并根据设置选项决定是否重置统计数据
            this.startNewGame(deckCount, true, true, shouldResetStats);

            // 如果重置了统计数据，提供反馈
            if (shouldResetStats) {
                console.log('已重置所有统计数据和游戏历史');
            }
        });
        document.getElementById('prev-hand').addEventListener('click', () => {
            console.log('点击查看上一局按钮');
            console.log('历史记录长度:', this.gameHistory.length);
            console.log('当前指针:', this.historyPointer);
            console.log('当前游戏状态:', this.gameState);
            this.viewPreviousGame();
        });
        document.getElementById('next-hand').addEventListener('click', () => {
            console.log('点击查看下一局按钮');
            console.log('历史记录长度:', this.gameHistory.length);
            console.log('当前指针:', this.historyPointer);
            console.log('当前游戏状态:', this.gameState);
            this.viewNextGame();
        });
        document.getElementById('current-hand').addEventListener('click', () => {
            console.log('点击返回当前局按钮');
            console.log('历史记录长度:', this.gameHistory.length);
            console.log('当前指针:', this.historyPointer);
            console.log('当前游戏状态:', this.gameState);
            this.returnToActiveGame();
        });

        // 添加玩家管理按钮事件
        document.getElementById('add-player').addEventListener('click', () => this.addPlayer());
        document.getElementById('remove-player').addEventListener('click', () => this.removePlayer());
        document.getElementById('player-count').addEventListener('change', (e) => this.updatePlayerCount(parseInt(e.target.value)));

        // 添加牌库选择事件
        document.getElementById('deck-number').addEventListener('change', (e) => {
            this.numberOfDecks = parseInt(e.target.value);
            this.updateDeckInfo();
        });

        // 确保下注控制按钮在初始时隐藏
        const bettingControls = document.getElementById('betting-controls');
        if (bettingControls) {
            bettingControls.classList.add('hidden');
        }

        // 绑定下注按钮事件
        const clearBetsBtn = document.getElementById('clear-bets-btn');
        const confirmBetBtn = document.getElementById('confirm-bet-btn');
        const repeatBetsBtn = document.getElementById('repeat-bets-btn');

        if (clearBetsBtn) {
            clearBetsBtn.addEventListener('click', () => this.clearBet());
        }

        if (confirmBetBtn) {
            confirmBetBtn.addEventListener('click', () => this.confirmBet());
        }

        // 添加全部确认并开始按钮的事件处理
        const confirmAllBetsBtn = document.getElementById('confirm-all-bets-btn');
        if (confirmAllBetsBtn) {
            confirmAllBetsBtn.addEventListener('click', () => this.confirmAllBets());
        }

        if (repeatBetsBtn) {
            repeatBetsBtn.addEventListener('click', () => this.repeatLastBets());
        }

        // 绑定游戏设置按钮
        document.getElementById('game-settings').addEventListener('click', () => {
            // 显示设置菜单
            document.getElementById('game-settings-menu').style.display = 'block';

            // 更新渗透率滑块值
            const penetrationRateSlider = document.getElementById('penetration-rate-slider');
            const penetrationRateValue = document.getElementById('penetration-rate-value');
            if (this.deck) {
                const currentRate = Math.round(this.deck.getPenetrationRate() * 100);
                penetrationRateSlider.value = currentRate;
                penetrationRateValue.textContent = `${currentRate}%`;
            }

            // 更新筹码设置值
            document.getElementById('min-bet').value = this.minBet;
            document.getElementById('max-bet').value = this.maxBet;
            document.getElementById('default-bet').value = this.defaultBet;
        });

        // 绑定重置统计按钮
        document.getElementById('reset-stats-btn').addEventListener('click', () => {
            const confirmed = confirm('确定要重置所有统计数据和游戏历史吗？此操作无法撤销。');
            if (confirmed) {
                this.resetAllStats();
                alert('已成功重置所有统计数据和游戏历史');
                this.updateUI();
            }
        });

        // 绑定关闭设置按钮
        document.getElementById('close-settings').addEventListener('click', () => {
            document.getElementById('game-settings-menu').style.display = 'none';
        });

        // 绑定取消设置按钮
        document.getElementById('cancel-settings').addEventListener('click', () => {
            document.getElementById('game-settings-menu').style.display = 'none';
        });

        // 绑定保存设置按钮
        document.getElementById('save-settings').addEventListener('click', () => {
            // 获取渗透率设置
            const penetrationRate = parseInt(document.getElementById('penetration-rate-slider').value) / 100;

            // 获取筹码设置
            const minBet = parseInt(document.getElementById('min-bet').value);
            const maxBet = parseInt(document.getElementById('max-bet').value);
            const defaultBet = parseInt(document.getElementById('default-bet').value);

            // 验证下注设置
            if (minBet > maxBet) {
                alert('最小下注不能大于最大下注');
                return;
            }

            if (defaultBet < minBet || defaultBet > maxBet) {
                alert('默认下注必须在最小和最大下注之间');
                return;
            }

            // 获取"开始新游戏时重置统计"选项状态（不再立即重置）
            const resetStatsOnNewGame = document.getElementById('reset-stats').checked;
            // 保存此选项到本地变量，供新游戏按钮使用
            this.resetStatsOnNewGame = resetStatsOnNewGame;
            console.log('已保存设置：开始新游戏时重置统计 =', resetStatsOnNewGame);

            // 获取蜗牛洗牌模式设置
            const snailShuffleMode = document.getElementById('snail-shuffle').checked;
            // 检查蜗牛洗牌模式是否发生变化
            const snailModeChanged = this.deck && (snailShuffleMode !== this.deck.isSnailMode);

            // 应用设置
            if (this.deck) {
                // 更新蜗牛洗牌模式
                if (snailModeChanged) {
                    gameLogger.debug(`蜗牛洗牌模式已${snailShuffleMode ? '开启' : '关闭'}`);
                    this.deck.isSnailMode = snailShuffleMode;
                }

                this.deck.setPenetrationRate(penetrationRate);

                // 应用筹码设置
                this.minBet = minBet;
                this.maxBet = maxBet;
                this.defaultBet = defaultBet;
                this.currentBet = defaultBet;

                // 更新筹码配置对象
                this.betConfiguration = {
                    minBet: minBet,
                    maxBet: maxBet,
                    defaultBet: defaultBet,
                    betStep: Math.min(10, Math.floor(minBet / 2))
                };

                // 获取自动下注设置
                const autoBetting = document.getElementById('auto-betting').checked;
                this.setAutoBetting(autoBetting);

                // 如果启用了自动下注，更新自动下注金额
                if (autoBetting) {
                    const autoBetAmount = parseInt(document.getElementById('auto-bet-amount').value);
                    if (!isNaN(autoBetAmount) && autoBetAmount >= minBet && autoBetAmount <= maxBet) {
                        this.setAllPlayersAutoBetAmount(autoBetAmount);
                    } else {
                        this.setAllPlayersAutoBetAmount(defaultBet);
                    }
                }

                // 获取自动补充筹码设置
                const autoRefillEnabled = document.getElementById('auto-refill-chips').checked;
                const refillThreshold = parseInt(document.getElementById('auto-refill-threshold').value);
                const refillAmount = parseInt(document.getElementById('auto-refill-amount').value);

                // 验证自动补充筹码设置
                if (autoRefillEnabled) {
                    if (isNaN(refillThreshold) || isNaN(refillAmount) ||
                        refillThreshold <= 0 || refillAmount <= 0) {
                        alert('自动补充筹码设置无效，请检查阈值和金额');
                        return;
                    }
                }

                // 应用自动补充筹码设置
                this.setAutoRefillChips(autoRefillEnabled, refillThreshold, refillAmount);
                gameLogger.info(`设置自动补充筹码: 启用=${autoRefillEnabled}, 阈值=${refillThreshold}, 金额=${refillAmount}`);

                // 立即更新牌库统计显示，确保蜗牛洗牌模式变化能立即反映在UI上
                this.updateDeckInfo();

                // 如果蜗牛洗牌模式发生了变化，更新整个UI
                if (snailModeChanged) {
                    this.updateUI();
                }
            }

            // 关闭设置菜单
            document.getElementById('game-settings-menu').style.display = 'none';
        });

        // 渗透率滑块事件
        const penetrationRateSlider = document.getElementById('penetration-rate-slider');
        const penetrationRateValue = document.getElementById('penetration-rate-value');
        if (penetrationRateSlider && penetrationRateValue) {
            penetrationRateSlider.addEventListener('input', function() {
                penetrationRateValue.textContent = `${this.value}%`;
            });
        }

        // 自动模式切换
        const autoModeCheckbox = document.getElementById('auto-mode');
        const autoSpeedContainer = document.getElementById('auto-speed-settings');
        const autoSpeedSlider = document.getElementById('auto-speed');
        const speedValue = document.getElementById('speed-value');
        const decreaseSpeedBtn = document.getElementById('decrease-speed');
        const increaseSpeedBtn = document.getElementById('increase-speed');

        // 庄家发牌速度控制
        const dealerSpeedSlider = document.getElementById('dealer-speed');
        const dealerSpeedValue = document.getElementById('dealer-speed-value');
        const decreaseDealerSpeedBtn = document.getElementById('decrease-dealer-speed');
        const increaseDealerSpeedBtn = document.getElementById('increase-dealer-speed');

        if (autoModeCheckbox && autoSpeedContainer && autoSpeedSlider && speedValue) {
            // 自动模式切换事件
            autoModeCheckbox.addEventListener('change', (e) => {
                window.isAutoMode = e.target.checked;

                // 显示或隐藏速度控制容器
                if (e.target.checked) {
                    autoSpeedContainer.style.display = 'block';
                    // 确保滑块宽度正确
                    setTimeout(() => {
                        autoSpeedSlider.style.width = '120px';
                    }, 0);
                } else {
                    autoSpeedContainer.style.display = 'none';
                }

                // 启动或停止BackgroundRunner
                if (window.BackgroundRunner) {
                    if (e.target.checked) {
                        // 启动BackgroundRunner并设置回调函数
                        window.BackgroundRunner.start(() => {
                            if (window.isAutoMode && this.gameState === 'playing') {
                                this.executeAutoAction();
                            }
                        });
                        gameLogger.debug('已启动BackgroundRunner');
                    } else {
                        window.BackgroundRunner.stop();
                        gameLogger.debug('已停止BackgroundRunner');
                    }
                }

                // 开始第一次自动操作
                if (e.target.checked) {
                    this.executeAutoAction();
                }
            });

            // 自动模式速度控制已移至ui.js中处理
        }

        // 庄家发牌速度控制已移至ui.js中处理

        // 测试模式相关事件处理
        const testAddCardButton = document.getElementById('test-add-card');
        const testClearHandButton = document.getElementById('test-clear-hand');
        const testPlayerSelect = document.getElementById('test-player-select');
        const testHandSelect = document.getElementById('test-hand-select');
        const testCardSuit = document.getElementById('test-card-suit');
        const testCardRank = document.getElementById('test-card-rank');

        // 如果测试事件已经初始化，则跳过绑定
        if (!window.testEventsInitialized && testAddCardButton && testClearHandButton && testPlayerSelect && testHandSelect && testCardSuit && testCardRank) {
            // 添加牌
            testAddCardButton.addEventListener('click', () => {
                const playerValue = testPlayerSelect.value;
                const handIndex = parseInt(testHandSelect.value);
                const suit = testCardSuit.value;
                const rank = testCardRank.value;

                if (playerValue === 'dealer') {
                    // 设置庄家手牌
                    const currentDealerHand = this.dealerHand || [];
                    this.setDealerHand([...currentDealerHand, { suit, rank }]);
                } else {
                    // 设置玩家手牌
                    const playerIndex = parseInt(playerValue);
                    const currentHand = this.players[playerIndex]?.hands[handIndex] || [];
                    this.setCustomHand(playerIndex, handIndex, [...currentHand, { suit, rank }]);
                }
            });

            // 清空手牌
            testClearHandButton.addEventListener('click', () => {
                const playerValue = testPlayerSelect.value;
                const handIndex = parseInt(testHandSelect.value);

                if (playerValue === 'dealer') {
                    this.setDealerHand([]);
                } else {
                    const playerIndex = parseInt(playerValue);
                    this.setCustomHand(playerIndex, handIndex, []);
                }
            });

            // 玩家选择变化时更新手牌选择器
            testPlayerSelect.addEventListener('change', () => {
                const playerValue = testPlayerSelect.value;
                testHandSelect.style.display = playerValue === 'dealer' ? 'none' : 'inline-block';
            });

            // 标记测试事件已初始化
            window.testEventsInitialized = true;
        }

        // 添加庄家软17点停牌开关监听
        const dealerStandSoft17Switch = document.getElementById('dealer-stand-soft17');
        if (dealerStandSoft17Switch) {
            dealerStandSoft17Switch.addEventListener('change', (e) => {
                console.log('庄家软17点停牌设置:', e.target.checked ? '开启' : '关闭');
            });
        }

        // 添加蜗牛洗牌开关监听
        const snailShuffleSwitch = document.getElementById('snail-shuffle');
        if (snailShuffleSwitch) {
            snailShuffleSwitch.addEventListener('change', (e) => {
                gameLogger.debug('蜗牛洗牌设置:', e.target.checked ? '开启' : '关闭');
                // 如果在游戏中改变设置，不会立即生效，而是在下次洗牌时生效
                if (this.deck) {
                    gameLogger.debug('洗牌方式已更改，将在下次洗牌时生效');
                }
            });
        }

        // 添加自动下注相关的事件处理
        const autoBettingCheckbox = document.getElementById('auto-betting');
        const autoBetSettings = document.getElementById('auto-bet-settings');
        const autoBetAmount = document.getElementById('auto-bet-amount');
        const setAutoBetButton = document.getElementById('set-auto-bet');

        if (autoBettingCheckbox && autoBetSettings) {
            // 自动下注开关事件
            autoBettingCheckbox.addEventListener('change', (e) => {
                // 显示或隐藏自动下注设置区域
                autoBetSettings.style.display = e.target.checked ? 'flex' : 'none';

                // 将当前默认下注额填入自动下注输入框
                if (e.target.checked) {
                    autoBetAmount.value = this.defaultBet;
                }
            });

            // 设置自动下注金额按钮事件
            if (setAutoBetButton && autoBetAmount) {
                setAutoBetButton.addEventListener('click', () => {
                    const amount = parseInt(autoBetAmount.value);
                    if (isNaN(amount) || amount < this.minBet || amount > this.maxBet) {
                        alert(`自动下注金额必须在${this.minBet}到${this.maxBet}之间`);
                        autoBetAmount.value = this.defaultBet;
                        return;
                    }

                    this.setAllPlayersAutoBetAmount(amount);
                    alert(`已设置所有玩家的自动下注金额为${amount}`);
                });
            }
        }

        // 检查是否有上一次的下注记录
        if (localStorage.getItem('lastBets')) {
            try {
                this.lastBets = JSON.parse(localStorage.getItem('lastBets'));
                gameLogger.debug('已加载之前的下注记录:', this.lastBets);
            } catch (e) {
                console.error('加载下注记录失败:', e);
                this.lastBets = [];
            }
        }

        // 应用UI样式
        this.updateUI();
    }

    // 获取当前游戏状态的快照
    getGameSnapshot() {
        return {
            players: this.players.map(player => ({
                name: player.name,
                hands: player.hands.map(hand => hand.map(card => ({ suit: card.suit, rank: card.rank }))),
                currentHandIndex: player.currentHandIndex,
                stats: { ...player.stats },
                gameState: player.gameState
            })),
            dealerHand: this.dealerHand.map(card => ({ suit: card.suit, rank: card.rank, hidden: card.hidden })),
            currentPlayerIndex: this.currentPlayerIndex,
            gameState: this.gameState
        };
    }

    // 添加下注相关方法

    // 开始下注阶段
    startBettingPhase() {
        gameLogger.debug('开始下注阶段');
        if (this.gameState !== 'betting') {
            gameLogger.debug('当前状态不是下注阶段');
            return;
        }

        // 记录每个玩家的当前筹码数
        const playerChips = this.players.map(player => ({
            name: player.name,
            chips: player.chips
        }));
        gameLogger.debug('下注阶段开始前玩家筹码状态:', playerChips);

        // 检查是否启用了自动补充筹码功能
        if (this.autoRefillChips && this.autoRefillChips.enabled) {
            gameLogger.debug('自动补充筹码功能已启用，检查玩家筹码状态');

            // 检查每个玩家的筹码是否低于阈值
            this.players.forEach((player, index) => {
                if (player.chips < this.autoRefillChips.threshold) {
                    // 自动补充筹码
                    const refillAmount = this.autoRefillChips.amount;
                    gameLogger.debug(`[自动补充筹码] 玩家${player.name}筹码(${player.chips})低于阈值(${this.autoRefillChips.threshold})，自动补充${refillAmount}筹码`);

                    if (player.addChips(refillAmount)) {
                        gameLogger.debug(`[自动补充筹码] 玩家${player.name}成功补充筹码，当前筹码: ${player.chips}`);
                        // 显示提示消息
                        if (typeof showToast === 'function') {
                            showToast(`已为 ${player.name} 自动补充 ${refillAmount} 筹码`);
                        }
                    } else {
                        gameLogger.error(`[自动补充筹码] 玩家${player.name}补充筹码失败`);
                    }
                }
            });
        }

        // 重置所有玩家的下注状态，但不清除筹码数量
        this.players.forEach(player => {
            // 确保下注状态为waiting
            player.betStatus = 'waiting';

            // 确保下注金额为0
            player.bets = [0];

            // 记录重置后的状态
            gameLogger.debug(`[startBettingPhase] 玩家${player.name}重置下注状态，当前筹码:${player.chips}`);
        });

        // 检查是否开启了自动下注（游戏设置中的自动下注或计牌系统的自动下注）
        const cardCountingAutoBet = window.cardCountingSystem &&
                                   window.cardCountingSystem.autoBetStrategy &&
                                   window.cardCountingSystem.autoBetStrategy.enabled;

        if (this.isAutoBetting || cardCountingAutoBet) {
            gameLogger.debug(`启用了自动下注，执行自动下注 (来源: ${this.isAutoBetting ? '游戏设置' : ''}${(this.isAutoBetting && cardCountingAutoBet) ? '和' : ''}${cardCountingAutoBet ? '计牌系统' : ''})`);
            setTimeout(() => {
                this.autoPlaceBets();
            }, 500); // 添加短暂延迟以便UI更新
            return;
        }

        // 显示下注界面
        this.showBettingUI();

        // 显示固定的下注控制按钮
        const fixedBettingControls = document.getElementById('betting-controls');
        if (fixedBettingControls) {
            fixedBettingControls.classList.remove('hidden');
        }

        this.updateUI();
    }

    // 显示下注界面
    showBettingUI() {
        // 移除旧的下注界面（如果存在）
        const oldBettingUI = document.getElementById('betting-ui');
        if (oldBettingUI) {
            oldBettingUI.remove();
        }

        // 移除旧的筹码容器（如果存在）
        const oldChipsContainer = document.getElementById('chips-container');
        if (oldChipsContainer) {
            oldChipsContainer.remove();
        }

        // 创建新的筹码下注界面
        const gameArea = document.querySelector('.game-area');
        if (!gameArea) return;

        // 创建筹码容器
        const chipsContainer = document.createElement('div');
        chipsContainer.id = 'chips-container';
        chipsContainer.className = 'chips-container';

        // 创建选择筹码区域 - 把这部分移到前面，表示先选筹码
        // 创建筹码选择区域 - 使用 chips-section 类来应用水平排列样式
        const chipValues = [100, 500, 1000, 2000, 5000];
        const chipsSection = document.createElement('div');
        chipsSection.className = 'chips-section';

        // 创建选中筹码提示
        const selectedChipInfo = document.createElement('div');
        selectedChipInfo.id = 'selected-chip-info';
        selectedChipInfo.className = 'selected-chip-info';
        selectedChipInfo.innerHTML = '已选择筹码：<span id="selected-chip-value">0</span>';
        chipsContainer.appendChild(selectedChipInfo);

        // 添加所有筹码
        chipValues.forEach(value => {
            const chip = document.createElement('div');
            chip.className = 'chip';
            chip.dataset.value = value;
            chip.textContent = value;

            // 修改点击事件：只选择筹码，不立即下注
            chip.addEventListener('click', () => {
                // 更新选中的筹码值
                this.selectedChipValue = value;

                // 视觉上更新选中状态
                document.querySelectorAll('.chip').forEach(c => {
                    c.classList.remove('selected');
                });
                chip.classList.add('selected');

                // 更新选中筹码显示
                document.getElementById('selected-chip-value').textContent = value;

                gameLogger.debug(`已选择筹码：${value}`);
            });

            chipsSection.appendChild(chip);
        });

        // 添加自定义筹码输入
        const customChip = document.createElement('div');
        customChip.className = 'chip custom-chip';
        customChip.textContent = '自定义';
        customChip.addEventListener('click', () => {
            const customValue = prompt('请输入自定义筹码金额（1-999999）：');
            if (customValue !== null) {
                const value = parseInt(customValue);
                if (!isNaN(value) && value > 0 && value <= 999999) {
                    this.selectedChipValue = value;

                    // 视觉上更新选中状态
                    document.querySelectorAll('.chip').forEach(c => {
                        c.classList.remove('selected');
                    });
                    customChip.classList.add('selected');

                    // 更新选中筹码显示
                    document.getElementById('selected-chip-value').textContent = value;

                    gameLogger.debug(`已选择自定义筹码：${value}`);
                } else {
                    alert('请输入有效的筹码金额（1-999999）');
                }
            }
        });
        chipsSection.appendChild(customChip);

        chipsContainer.appendChild(chipsSection);

        // 创建下注区域容器
        const betAreasContainer = document.createElement('div');
        betAreasContainer.className = 'bet-areas-container';

        // 为每个玩家创建下注区域
        this.players.forEach((player, index) => {
            const betArea = document.createElement('div');
            betArea.className = 'bet-area';
            betArea.dataset.playerIndex = index;

            // 如果已有下注，添加has-bet类
            if (player.getCurrentBet() > 0) {
                betArea.classList.add('has-bet');
            }

            // 玩家名称
            const playerName = document.createElement('div');
            playerName.className = 'player-name';
            playerName.textContent = player.name;
            betArea.appendChild(playerName);

            // 下注金额显示
            const betValue = document.createElement('div');
            betValue.className = 'bet-value';
            betValue.textContent = player.getCurrentBet() || '0';
            betArea.appendChild(betValue);

            // 修改点击事件，直接应用当前选中的筹码
            betArea.addEventListener('click', () => {
                // 如果没有选择筹码，提示用户
                if (this.selectedChipValue <= 0) {
                    alert('请先选择筹码金额');
                    return;
                }

                // 检查是否有足够的筹码
                if (this.selectedChipValue > player.chips) {
                    alert(`玩家 ${player.name} 筹码不足！`);
                    return;
                }

                // 设置当前玩家
                this.currentPlayerIndex = index;

                // 获取当前下注金额并增加选中筹码值
                const currentBet = player.getCurrentBet() || 0;
                const newBet = currentBet + this.selectedChipValue;

                // 清除之前的下注并重新下注
                player.clearBet();
                if (!player.placeBet(newBet)) {
                    alert('下注失败，请检查筹码余额');
                    return;
                }

                // 更新当前下注额
                this.currentBet = newBet;

                // 更新下注区域显示
                this.updateBetAreaDisplay(index);

                // 如果已有下注，添加has-bet类
                betArea.classList.add('has-bet');

                gameLogger.debug(`玩家 ${player.name} 增加下注：${this.selectedChipValue}，总下注：${newBet}`);
            });

            betAreasContainer.appendChild(betArea);
        });

        chipsContainer.appendChild(betAreasContainer);

        // 创建控制区域
        const bettingControls = document.createElement('div');
        bettingControls.className = 'betting-controls dynamic-controls';
        bettingControls.style.display = 'flex';
        bettingControls.style.justifyContent = 'space-between';
        bettingControls.style.alignItems = 'center';
        bettingControls.style.width = '100%';

        // 修改玩家信息显示
        const bettingInfo = document.createElement('div');
        bettingInfo.className = 'betting-info';
        bettingInfo.innerHTML = '';  // 删除提示文字
        bettingControls.appendChild(bettingInfo);

        // 创建按钮容器
        const bettingButtons = document.createElement('div');
        bettingButtons.className = 'betting-buttons';
        bettingButtons.style.display = 'flex';
        bettingButtons.style.gap = '10px';

        // 创建重复下注按钮
        const repeatBetsBtn = document.createElement('button');
        repeatBetsBtn.id = 'repeat-bets-btn-inline';
        repeatBetsBtn.className = 'btn btn-info';
        repeatBetsBtn.textContent = '重复下注';
        repeatBetsBtn.onclick = () => this.repeatLastBets();
        bettingButtons.appendChild(repeatBetsBtn);

        // 创建清空下注按钮
        const clearBetsBtn = document.createElement('button');
        clearBetsBtn.id = 'clear-bets-btn-inline';
        clearBetsBtn.className = 'btn btn-danger';
        clearBetsBtn.textContent = '清空下注';
        clearBetsBtn.onclick = () => {
            // 清除所有玩家的下注
            this.players.forEach((player, idx) => {
                player.clearBet();
                this.updateBetAreaDisplay(idx);
            });

            // 移除所有has-bet类
            document.querySelectorAll('.bet-area').forEach(area => {
                area.classList.remove('has-bet');
            });

            gameLogger.debug('已清除所有玩家的下注');
        };
        bettingButtons.appendChild(clearBetsBtn);

        // 创建确认下注按钮
        const confirmBetBtn = document.createElement('button');
        confirmBetBtn.id = 'confirm-bet-btn-inline';
        confirmBetBtn.className = 'btn btn-success';
        confirmBetBtn.textContent = '确认下注';
        confirmBetBtn.onclick = () => this.confirmBet();
        bettingButtons.appendChild(confirmBetBtn);

        // 创建全部确认按钮
        const confirmAllBetsBtn = document.createElement('button');
        confirmAllBetsBtn.id = 'confirm-all-bets-btn-inline';
        confirmAllBetsBtn.className = 'btn btn-primary';
        confirmAllBetsBtn.textContent = '全部确认';
        confirmAllBetsBtn.onclick = () => this.confirmAllBets();
        bettingButtons.appendChild(confirmAllBetsBtn);

        // 将按钮容器添加到控制区域
        bettingControls.appendChild(bettingButtons);

        chipsContainer.appendChild(bettingControls);

        chipsContainer.appendChild(bettingControls);

        // 将筹码容器添加到游戏区域
        gameArea.appendChild(chipsContainer);

        // 隐藏底部固定的下注控制按钮
        const fixedBettingControls = document.getElementById('betting-controls');
        if (fixedBettingControls) {
            fixedBettingControls.classList.add('hidden');
        }
    }

    // 隐藏下注界面 - 完全重写确保界面能被移除
    hideBettingUI() {
        console.log('执行强制隐藏下注界面');

        // 隐藏固定的下注控制按钮
        const fixedBettingControls = document.getElementById('betting-controls');
        if (fixedBettingControls) {
            fixedBettingControls.classList.add('hidden');
        }

        // 移除筹码容器
        const chipsContainer = document.getElementById('chips-container');
        if (chipsContainer) {
            console.log('找到并移除chips-container');
            chipsContainer.remove();
        }

        // 移除betting-ui
        const bettingUI = document.getElementById('betting-ui');
        if (bettingUI) {
            console.log('找到并移除betting-ui');
            bettingUI.remove();
        }

        // 防止遗漏，用选择器查找所有可能的元素
        const elementsToRemove = [
            '.chips-container',
            '.betting-ui',
            '#chips-container',
            '#betting-ui',
            '[id^="chips"]',
            '[class^="dynamic-controls"]',
            '.dynamic-controls',
            '#clear-bets-btn-inline',
            '#confirm-bet-btn-inline',
            '#confirm-all-bets-btn-inline',
            '.betting-buttons'
        ];

        elementsToRemove.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                gameLogger.debug(`移除元素: ${selector}`);
                el.remove();
            });
        });

        gameLogger.debug('下注界面移除完成');
    }

    // 全部确认下注并开始游戏
    confirmAllBets() {
        gameLogger.debug('执行全部确认下注');

        // 检查是否所有玩家都有下注
        let allPlayersHaveBets = true;
        let anyPlayerHasBet = false;

        this.players.forEach((player, index) => {
            const currentBet = player.getCurrentBet();
            if (currentBet <= 0) {
                allPlayersHaveBets = false;
            } else {
                anyPlayerHasBet = true;
                // 自动确认每个有下注的玩家
                player.confirmBet();
            }
        });

        // 如果没有任何玩家下注，提示用户
        if (!anyPlayerHasBet) {
            alert('请至少为一位玩家下注！');
            return;
        }

        // 如果有些玩家没有下注，询问用户是否继续
        if (!allPlayersHaveBets) {
            const confirmed = confirm('有些玩家没有下注，是否继续游戏？');
            if (!confirmed) {
                return;
            }
        }

        // 重置选中筹码值
        this.selectedChipValue = 0;

        // 强制隐藏下注界面
        this.hideBettingUI();

        // 设置游戏状态为playing
        this.gameState = 'playing';

        // 直接开始游戏，不使用setTimeout
        this.startPlayingPhase();
    }

    // 更新下注区域显示
    updateBetAreaDisplay(playerIndex) {
        const betArea = document.querySelector(`.bet-area[data-player-index="${playerIndex}"]`);
        if (!betArea) return;

        const player = this.players[playerIndex];
        if (!player) return;

        // 更新下注金额显示
        const betValue = betArea.querySelector('.bet-value');
        if (betValue) {
            betValue.textContent = player.getCurrentBet() || '0';
        }

        // 更新下注区域状态
        if (player.getCurrentBet() > 0) {
            betArea.classList.add('has-bet');
        } else {
            betArea.classList.remove('has-bet');
        }
    }

    // 更新下注信息显示 - 适应新的下注逻辑
    updateBettingInfo() {
        // 由于新的下注逻辑不再显示当前玩家的下注信息，此方法简化

        // 更新选中筹码显示
        const selectedChipValue = document.getElementById('selected-chip-value');
        if (selectedChipValue) {
            selectedChipValue.textContent = this.selectedChipValue || '0';
        }

        // 更新所有玩家的下注区域显示
        this.players.forEach((player, index) => {
            this.updateBetAreaDisplay(index);
        });
    }

    // 增加下注金额
    increaseBet() {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) return;

        // 确保不超过最大下注额和玩家筹码数
        const newBet = Math.min(
            this.currentBet + this.betStep,
            this.maxBet,
            currentPlayer.chips
        );

        this.currentBet = newBet;
        this.updateBetAmountDisplay();
    }

    // 减少下注金额
    decreaseBet() {
        // 确保不低于最小下注额
        this.currentBet = Math.max(this.currentBet - this.betStep, this.minBet);
        this.updateBetAmountDisplay();
    }

    // 设置预设下注金额
    setPresetBet(amount) {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) return;

        // 确保不超过玩家筹码数
        this.currentBet = Math.min(amount, currentPlayer.chips);
        this.updateBetAmountDisplay();
    }

    // 更新下注金额显示
    updateBetAmountDisplay() {
        const betAmount = document.getElementById('current-bet-amount');
        if (betAmount) {
            betAmount.textContent = this.currentBet;
        }
    }

    // 确认下注
    confirmBet() {
        gameLogger.debug('执行确认下注');
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            gameLogger.debug('没有当前玩家，无法确认下注');
            return;
        }

        // 检查下注金额是否有效
        if (this.currentBet <= 0 || this.currentBet > currentPlayer.chips) {
            alert('无效下注金额');
            return;
        }

        // 确保玩家筹码已经扣除且下注确认
        if (currentPlayer.getCurrentBet() !== this.currentBet) {
            // 清除之前的下注并重新下注
            currentPlayer.clearBet();
            if (!currentPlayer.placeBet(this.currentBet)) {
                alert('下注失败，请检查筹码余额');
                return;
            }
        }

        // 更新下注状态
        gameLogger.debug(`玩家 ${currentPlayer.name} 确认下注: ${this.currentBet}`);
        currentPlayer.confirmBet();

        // 检查是否所有玩家都已确认下注
        const allConfirmed = this.players.every(player => player.betStatus === 'confirmed');
        if (allConfirmed) {
            gameLogger.debug('所有玩家已确认下注，直接开始游戏');
            // 立即隐藏下注界面并开始游戏
            this.hideBettingUI();
            this.gameState = 'playing';
            setTimeout(() => this.startPlayingPhase(), this.getAdaptiveDelay(this.autoDelay, 'default'));
            return;
        }

        // 如果还有玩家未确认，移动到下一个玩家
        gameLogger.debug('还有玩家未确认下注，移动到下一个玩家');
        this.moveToNextBettingPlayer();
    }

    // 清除下注
    clearBet() {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) return;

        // 清除已下注的筹码
        currentPlayer.clearBet();

        // 重置当前下注金额为默认值
        this.currentBet = this.defaultBet;
        this.updateBetAmountDisplay();
        this.updateUI();
    }

    // 移动到下一个等待下注的玩家
    moveToNextBettingPlayer() {
        gameLogger.debug('寻找下一个未确认下注的玩家');

        // 如果没有玩家，直接返回
        if (this.players.length === 0) {
            gameLogger.debug('没有玩家，无法继续');
            return;
        }

        // 检查是否所有玩家都已确认下注
        const allConfirmed = this.players.every(player => player.betStatus === 'confirmed');
        if (allConfirmed) {
            gameLogger.debug('检测到所有玩家已确认下注，准备开始游戏');
            // 强制隐藏下注界面
            this.hideBettingUI();
            // 设置游戏状态为playing
            this.gameState = 'playing';
            // 直接开始游戏，不使用setTimeout
            this.startPlayingPhase();
            return;
        }

        // 寻找下一个未确认下注的玩家
        let foundNext = false;
        const startIndex = this.currentPlayerIndex;

        // 从当前玩家的下一位开始查找
        for (let i = 1; i <= this.players.length; i++) {
            const nextIndex = (startIndex + i) % this.players.length;
            const nextPlayer = this.players[nextIndex];

            if (nextPlayer && nextPlayer.betStatus !== 'confirmed') {
                gameLogger.debug(`找到下一个未确认的玩家: ${nextPlayer.name}`);
                this.currentPlayerIndex = nextIndex;
                foundNext = true;
                break;
            }
        }

        // 如果没有找到下一个未确认的玩家（理论上不会发生，因为之前已检查）
        if (!foundNext) {
            gameLogger.debug('未找到下一个未确认的玩家，可能所有玩家已确认');
            // 再次检查是否所有玩家已确认
            const doubleCheck = this.players.every(player => player.betStatus === 'confirmed');
            if (doubleCheck) {
                gameLogger.debug('二次确认所有玩家已下注，开始游戏');
                // 强制隐藏下注界面
                this.hideBettingUI();
                // 设置游戏状态
                this.gameState = 'playing';
                // 直接开始游戏，不使用setTimeout
                this.startPlayingPhase();
                return;
            }
        }

        // 更新当前下注玩家的显示
        const currentPlayer = this.getCurrentPlayer();
        if (currentPlayer) {
            gameLogger.debug(`更新当前玩家显示: ${currentPlayer.name}`);
            this.currentBet = currentPlayer.getCurrentBet() || this.defaultBet;

            // 更新筹码下注界面
            const chipsContainer = document.getElementById('chips-container');
            if (chipsContainer) {
                // 更新活跃下注区
                document.querySelectorAll('.bet-area').forEach((area) => {
                    if (parseInt(area.dataset.playerIndex) === this.currentPlayerIndex) {
                        area.classList.add('active');
                    } else {
                        area.classList.remove('active');
                    }
                });

                // 更新下注信息显示
                this.updateBettingInfo();
            }
        }
    }

    // 添加筹码（用于买入）
    addChips(playerIndex, amount) {
        if (playerIndex < 0 || playerIndex >= this.players.length) {
            console.error('无效的玩家索引');
            return false;
        }

        amount = parseInt(amount);
        if (isNaN(amount) || amount <= 0) {
            console.error('无效的筹码金额');
            return false;
        }

        return this.players[playerIndex].addChips(amount);
    }

    // 为所有玩家添加相同数量的筹码
    addChipsForAll(amount) {
        amount = parseInt(amount);
        if (isNaN(amount) || amount <= 0) {
            console.error('无效的筹码金额');
            return false;
        }

        let success = true;
        this.players.forEach(player => {
            if (!player.addChips(amount)) {
                success = false;
            }
        });

        this.updateUI();
        return success;
    }

    // 开始游戏阶段（从下注到发牌）
    startPlayingPhase() {
        gameLogger.debug('强制开始游戏阶段，从下注到发牌');

        // 强制隐藏下注界面
        this.hideBettingUI();

        // 确保游戏状态为playing
        this.gameState = 'playing';

        // 检查所有玩家是否都已确认下注
        const allConfirmed = this.players.every(player => player.betStatus === 'confirmed');
        if (!allConfirmed) {
            gameLogger.debug('警告：并非所有玩家都已确认下注，但仍然继续游戏');
            // 强制确认所有玩家的下注
            this.players.forEach(player => {
                if (player.betStatus !== 'confirmed' && player.getCurrentBet() > 0) {
                    player.confirmBet();
                }
            });
        }

        // 设置当前玩家为第一位玩家
        this.currentPlayerIndex = 0;

        // 如果不是测试模式，进行发牌操作
        if (!this.isTestMode) {
            // 清空所有手牌
            this.players.forEach(player => {
                player.hands = [[]];
                player.currentHandIndex = 0;
            });
            this.dealerHand = [];

            // 正常发牌
            for (let i = 0; i < 2; i++) {
                for (const player of this.players) {
                    const card = this.deck.deal();
                    // 确保玩家的牌始终是明牌
                    if (card) {
                        card.setHidden(false);
                        player.hands[0].push(card);
                    }
                }
                this.dealerHand.push(this.deck.deal());
            }

            // 隐藏庄家的第二张牌
            if (this.dealerHand[1]) {
                this.dealerHand[1].setHidden(true);
                gameLogger.debug('庄家第二张牌设置为隐藏');
            }
        }

        // 立即更新UI显示发牌结果
        this.updateUI();
        gameLogger.debug('游戏开始，UI已更新');

        // 检查第一位玩家是否有BlackJack
        if (!this.isTestMode) {
            const firstHand = this.getCurrentHand();
            if (firstHand && this.isBlackjack(firstHand)) {
                gameLogger.debug('第一位玩家初始手牌获得BlackJack！');
                firstHand.isBlackjack = true;
                this.updateUI();
                setTimeout(() => {
                    gameLogger.debug('BlackJack展示完毕，移动到下一位玩家');
                    this.moveToNext();  // 直接移动到下一位玩家
                }, 600);
                return;
            }
        }

        // 如果开启了自动模式且不是测试模式，等待一会儿再开始操作
        if (window.isAutoMode && !this.isTestMode) {
            gameLogger.debug('自动模式已开启，等待开始...');

            // 使用自适应延迟时间 - 第一次操作使用更长的延迟（比普通操作多1秒）
            const firstActionDelay = this.getAdaptiveDelay(this.autoDelay, 'default') + 1000;
            gameLogger.debug('初始自动操作延迟时间:', firstActionDelay, 'ms (包含额外1000ms等待)');

            setTimeout(() => {
                if (window.autoStrategy && typeof window.autoStrategy.getAction === 'function') {
                    this.executeAutoAction();
                } else {
                    gameLogger.debug('自动策略未加载，稍后重试...');
                    setTimeout(() => this.executeAutoAction(), 300);
                }
            }, firstActionDelay);
        }
    }

    // 添加自动下注方法
    autoPlaceBets() {
        gameLogger.debug('执行自动下注');

        // 检查是否有玩家
        if (this.players.length === 0) {
            gameLogger.debug('没有玩家，无法执行自动下注');
            return;
        }

        let allBetConfirmed = true;

        // 始终检查是否启用了基于真数的自动下注（计牌系统的自动下注）
        const useCountBasedBetting = window.cardCountingSystem &&
                                    window.cardCountingSystem.autoBetStrategy &&
                                    window.cardCountingSystem.autoBetStrategy.enabled;

        // 获取当前真数
        const trueCount = this.deck.getTrueCount();

        // 如果启用了基于真数的自动下注，记录日志
        if (useCountBasedBetting) {
            gameLogger.debug(`[自动下注] 使用基于真数的自动下注策略，当前真数: ${trueCount}`);
        } else {
            gameLogger.debug(`[自动下注] 使用固定金额自动下注策略`);
        }

        // 为每位玩家执行下注
        for (let i = 0; i < this.players.length; i++) {
            const player = this.players[i];

            // 记录详细的下注前状态
            gameLogger.debug(`[自动下注] 玩家${player.name}下注前状态 - 筹码:${player.chips}, 当前下注:${player.getCurrentBet()}, 下注状态:${player.betStatus}`);

            // 检查是否启用了自动补充筹码功能
            if (this.autoRefillChips && this.autoRefillChips.enabled) {
                // 检查玩家筹码是否低于阈值
                if (player.chips < this.autoRefillChips.threshold) {
                    // 自动补充筹码
                    const refillAmount = this.autoRefillChips.amount;
                    gameLogger.debug(`[自动补充筹码] 玩家${player.name}筹码(${player.chips})低于阈值(${this.autoRefillChips.threshold})，自动补充${refillAmount}筹码`);

                    if (player.addChips(refillAmount)) {
                        gameLogger.debug(`[自动补充筹码] 玩家${player.name}成功补充筹码，当前筹码: ${player.chips}`);
                        // 显示提示消息
                        if (typeof showToast === 'function') {
                            showToast(`已为 ${player.name} 自动补充 ${refillAmount} 筹码`);
                        }
                    } else {
                        gameLogger.error(`[自动补充筹码] 玩家${player.name}补充筹码失败`);
                    }
                }
            }

            // 避免重复下注，如果玩家已经有确认的下注，则跳过
            if (player.betStatus === 'confirmed' && player.getCurrentBet() > 0) {
                gameLogger.debug(`[自动下注] 玩家${player.name}已有确认的下注${player.getCurrentBet()}，跳过此次自动下注`);
                continue;
            }

            // 获取下注金额
            let betAmount;

            // 如果启用了基于真数的自动下注，使用计牌系统推荐的下注金额
            if (useCountBasedBetting) {
                betAmount = window.cardCountingSystem.getRecommendedBet(trueCount);
                gameLogger.debug(`[自动下注] 基于真数${trueCount}的推荐下注金额: ${betAmount}`);

                // 如果计牌系统返回的下注金额为0，使用默认下注金额
                if (betAmount <= 0) {
                    betAmount = this.defaultBet;
                    gameLogger.debug(`[自动下注] 计牌系统返回的下注金额为0，使用默认下注金额: ${betAmount}`);
                }
            } else {
                // 否则使用为此玩家设置的下注金额，如果没有则使用默认值
                betAmount = this.autoBetAmounts[i] || this.defaultBet;
            }

            // 确保下注金额不超过玩家筹码数和最大下注限制
            betAmount = Math.min(betAmount, player.chips, this.maxBet);

            // 确保下注金额不低于最小下注限制
            betAmount = Math.max(betAmount, this.minBet);

            // 清除当前下注，避免累积
            if (player.getCurrentBet() > 0) {
                gameLogger.debug(`[自动下注] 清除玩家${player.name}当前下注:${player.getCurrentBet()}`);
                player.clearBet();
                gameLogger.debug(`[自动下注] 清除后玩家${player.name}筹码:${player.chips}`);
            }

            // 执行下注
            const chipsBeforeBet = player.chips;
            if (player.placeBet(betAmount)) {
                player.confirmBet();
                gameLogger.debug(`[自动下注] 玩家${player.name}自动下注${betAmount}筹码，下注前:${chipsBeforeBet}，下注后:${player.chips}，差额:${chipsBeforeBet - player.chips}`);
            } else {
                gameLogger.debug(`[自动下注] 玩家${player.name}自动下注失败`);
                allBetConfirmed = false;
            }
        }

        // 保存当前下注记录，用于重复下注功能
        this.lastBets = this.players.map(player => player.getCurrentBet());
        gameLogger.debug('保存当前下注记录:', this.lastBets);

        // 所有玩家下注完成后，进入发牌阶段
        if (allBetConfirmed) {
            // 更新UI显示
            this.updateUI();

            setTimeout(() => {
                this.startPlayingPhase();
            }, this.getAdaptiveDelay(this.autoDelay, 'default')); // 使用自适应延迟
        } else {
            // 如果有下注失败的情况，回到手动下注模式
            this.isAutoBetting = false;
            this.currentPlayerIndex = 0;
            this.showBettingUI();
            // 确保UI更新
            this.updateUI();
        }
    }

    // 设置自动下注开关
    setAutoBetting(enabled) {
        this.isAutoBetting = enabled;
        gameLogger.debug(`游戏设置中的自动下注已${enabled ? '开启' : '关闭'}`);

        // 如果关闭了游戏设置中的自动下注，但计牌系统的自动下注仍然开启，提示用户
        if (!enabled && window.cardCountingSystem &&
            window.cardCountingSystem.autoBetStrategy &&
            window.cardCountingSystem.autoBetStrategy.enabled) {
            gameLogger.debug('计牌系统的自动下注仍然处于启用状态，将优先使用计牌系统的自动下注策略');
        }

        return enabled;
    }

    // 设置自动补充筹码
    setAutoRefillChips(enabled, threshold, amount) {
        this.autoRefillChips = {
            enabled: enabled,
            threshold: threshold || 1000,
            amount: amount || 10000
        };

        gameLogger.debug(`自动补充筹码已${enabled ? '开启' : '关闭'}, 阈值: ${this.autoRefillChips.threshold}, 金额: ${this.autoRefillChips.amount}`);

        return true;
    }

    // 设置玩家的自动下注金额
    setPlayerAutoBetAmount(playerIndex, amount) {
        if (playerIndex < 0 || playerIndex >= this.players.length) {
            console.error('无效的玩家索引');
            return false;
        }

        amount = parseInt(amount);
        if (isNaN(amount) || amount < this.minBet) {
            amount = this.defaultBet;
        }

        // 确保不超过最大下注限额
        amount = Math.min(amount, this.maxBet);

        this.autoBetAmounts[playerIndex] = amount;
        gameLogger.debug(`已为玩家${this.players[playerIndex].name}设置自动下注金额: ${amount}`);
        return true;
    }

    // 设置所有玩家的自动下注金额
    setAllPlayersAutoBetAmount(amount) {
        amount = parseInt(amount);
        if (isNaN(amount) || amount < this.minBet) {
            amount = this.defaultBet;
        }

        // 确保不超过最大下注限额
        amount = Math.min(amount, this.maxBet);

        this.players.forEach((player, index) => {
            this.autoBetAmounts[index] = amount;
        });

        gameLogger.debug(`已为所有玩家设置自动下注金额: ${amount}`);
        return true;
    }

    // 紧急强制启动游戏方法 - 可在界面卡住时调用
    forceStartGame() {
        gameLogger.debug('紧急方法：强制启动游戏');

        // 强制完成所有玩家的下注
        this.players.forEach(player => {
            // 如果玩家有下注但未确认，则确认下注
            if (player.getCurrentBet() > 0 && player.betStatus !== 'confirmed') {
                gameLogger.debug(`强制确认玩家 ${player.name} 的下注`);
                player.confirmBet();
            }
            // 如果玩家没有下注，给予最小下注并确认
            else if (player.getCurrentBet() <= 0) {
                gameLogger.debug(`为玩家 ${player.name} 添加最小下注`);
                const minBet = Math.min(this.minBet, player.chips);
                if (minBet > 0) {
                    player.placeBet(minBet);
                    player.confirmBet();
                }
            }
        });

        // 强制隐藏下注界面
        this.hideBettingUI();

        // 强制设置游戏状态为playing
        this.gameState = 'playing';

        // 立即更新UI
        this.updateUI();

        // 直接调用startPlayingPhase方法，不使用setTimeout
        gameLogger.debug('紧急强制进入游戏阶段');
        this.startPlayingPhase();
    }

    // 确认下注 - 增加紧急启动逻辑
    confirmBet() {
        gameLogger.debug('执行确认下注');
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            gameLogger.debug('没有当前玩家，无法确认下注');
            return;
        }

        // 检查下注金额是否有效
        if (this.currentBet <= 0 || this.currentBet > currentPlayer.chips) {
            alert('无效下注金额');
            return;
        }

        // 确保玩家筹码已经扣除且下注确认
        if (currentPlayer.getCurrentBet() !== this.currentBet) {
            // 清除之前的下注并重新下注
            currentPlayer.clearBet();
            if (!currentPlayer.placeBet(this.currentBet)) {
                alert('下注失败，请检查筹码余额');
                return;
            }
        }

        // 更新下注状态
        gameLogger.debug(`玩家 ${currentPlayer.name} 确认下注: ${this.currentBet}`);
        currentPlayer.confirmBet();

        // 检查是否所有玩家都已确认下注
        const allConfirmed = this.players.every(player => player.betStatus === 'confirmed');
        if (allConfirmed) {
            gameLogger.debug('所有玩家已确认下注，直接开始游戏');
            // 隐藏下注界面
            this.hideBettingUI();
            // 设置游戏状态为playing
            this.gameState = 'playing';
            // 直接调用startPlayingPhase方法
            this.startPlayingPhase();
            return;
        }

        // 如果还有玩家未确认，移动到下一个玩家
        gameLogger.debug('还有玩家未确认下注，移动到下一个玩家');
        this.moveToNextBettingPlayer();
    }

    // 添加重复下注功能
    repeatLastBets() {
        gameLogger.debug('执行重复下注');

        // 检查是否有上一局的下注记录
        if (!this.lastBets || this.lastBets.length === 0) {
            alert('没有上一局的下注记录');
            return;
        }

        let success = true;

        // 为每个玩家应用上一局的下注
        this.players.forEach((player, index) => {
            if (index < this.lastBets.length && this.lastBets[index] > 0) {
                // 检查玩家是否有足够的筹码
                if (this.lastBets[index] > player.chips) {
                    alert(`玩家 ${player.name} 筹码不足，无法重复上一局的下注`);
                    success = false;
                    return;
                }

                // 清除当前下注并应用上一局的下注
                player.clearBet();
                if (!player.placeBet(this.lastBets[index])) {
                    alert(`为玩家 ${player.name} 重复下注失败`);
                    success = false;
                    return;
                }

                // 更新下注区域显示
                this.updateBetAreaDisplay(index);
            }
        });

        if (success) {
            gameLogger.debug('重复下注完成，下注金额:', this.lastBets);
        } else {
            gameLogger.debug('重复下注部分失败');
        }
    }

    /**
     * 重置所有玩家的统计数据和游戏历史
     */
    resetAllStats() {
        gameLogger.debug('开始全面重置所有统计数据和游戏历史...');

        // 清除所有玩家的统计数据
        this.players.forEach(player => {
            // 使用player的resetStats方法重置基本统计数据
            player.resetStats();

            // 清除可能存在的总下注金额字段
            if (player.totalBetAmount !== undefined) {
                player.totalBetAmount = 0;
                gameLogger.debug(`重置玩家${player.name}的总下注金额`);
            }

            // 强制确保连胜连败相关字段被重置
            if (player.stats) {
                // 重置连胜连败记录
                player.stats.longestWinStreak = 0;
                player.stats.longestLoseStreak = 0;
                player.stats.currentWinStreak = 0;
                player.stats.currentLoseStreak = 0;

                // 重置其他可能影响统计的字段
                player.stats.largestWin = 0;
                player.stats.largestLoss = 0;

                gameLogger.debug(`强制重置玩家${player.name}的连胜连败记录和最大赢取/损失记录`);
            }
        });

        // 清除游戏历史记录
        this.gameHistory.clearHistory();

        // 重置所有记录的下注
        this.lastBets = [];

        // 清除localStorage中的下注历史记录
        localStorage.removeItem('bettingHistory');
        localStorage.removeItem('gameCount');
        localStorage.removeItem('playerStats');
        localStorage.removeItem('longestWinStreak');
        localStorage.removeItem('longestLoseStreak');

        // 如果有下注历史记录实例，清除其内存数据
        if (window.bettingHistory) {
            window.bettingHistory.clearHistory();
            gameLogger.debug('已清除下注历史记录实例数据');
        }

        // 重置全局玩家统计对象中的相关字段
        if (window.playerStats) {
            // 重置基本统计数据
            window.playerStats.totalBetAmount = 0;
            window.playerStats.totalWinAmount = 0;
            window.playerStats.totalLossAmount = 0;
            window.playerStats.totalProfit = 0;
            window.playerStats.splitCount = 0;

            // 强制重置连胜连败记录
            window.playerStats.longestWinStreak = 0;
            window.playerStats.longestLoseStreak = 0;
            window.playerStats.currentWinStreak = 0;
            window.playerStats.currentLoseStreak = 0;

            // 重置其他可能影响统计的字段
            window.playerStats.largestWin = 0;
            window.playerStats.largestLoss = 0;
            window.playerStats.highestChips = this.players[0]?.chips || 10000;
            window.playerStats.lowestChips = this.players[0]?.chips || 10000;

            gameLogger.debug('已重置全局玩家统计对象中的所有字段');

            // 强制更新玩家统计面板显示
            if (typeof window.playerStats.updateStatsDisplay === 'function') {
                window.playerStats.updateStatsDisplay();
                gameLogger.debug('强制更新玩家统计面板显示');
            }
        }

        // 重置游戏计数器
        this.gameCount = 0;

        // 重置运行计数
        if (window.cardCountingSystem) {
            window.cardCountingSystem.runningCount = 0;
            gameLogger.debug('已重置算牌系统的运行计数');
        }

        gameLogger.debug('已完成全面重置所有统计数据和游戏历史');
    }

    /**
     * 暂停自动模式
     */
    pauseAutoMode() {
        if (!window.isAutoMode || this.isAutoPaused) {
            return; // 如果自动模式未开启或已经暂停，无需操作
        }

        this.isAutoPaused = true;
        gameLogger.debug('自动模式已暂停');

        // 更新暂停按钮文本
        const pauseBtn = document.getElementById('pause-auto');
        if (pauseBtn) {
            pauseBtn.textContent = '继续自动';
        }
    }

    /**
     * 继续自动模式
     */
    resumeAutoMode() {
        if (!window.isAutoMode || !this.isAutoPaused) {
            return; // 如果自动模式未开启或没有暂停，无需操作
        }

        this.isAutoPaused = false;
        gameLogger.debug('自动模式已恢复');

        // 更新暂停按钮文本
        const pauseBtn = document.getElementById('pause-auto');
        if (pauseBtn) {
            pauseBtn.textContent = '暂停自动';
        }

        // 如果游戏状态为playing，立即执行自动操作
        if (this.gameState === 'playing') {
            // 使用额外延迟，让玩家有时间查看当前状态
            const resumeDelay = 1000;
            gameLogger.debug(`恢复自动模式后操作延迟: ${resumeDelay}ms`);
            setTimeout(() => this.executeAutoAction(), resumeDelay);
        }
    }

    /**
     * 切换自动模式暂停/继续状态
     */
    toggleAutoPause() {
        if (this.isAutoPaused) {
            this.resumeAutoMode();
        } else {
            this.pauseAutoMode();
        }
    }

    // 高性能版本的手牌显示更新，使用requestAnimationFrame
    updateOptimizedHandDisplay(containerId, hand, forceUpdate = false) {
        if (!hand || !Array.isArray(hand)) {
            console.warn('更新手牌显示失败：无效的手牌数据', hand);
            return;
        }

        try {
            const container = typeof containerId === 'string' ?
                (window.PerformanceUtils.elementCache.get(containerId) || document.getElementById(containerId)) :
                containerId;

            if (!container) {
                console.warn('找不到卡牌容器:', containerId);
                return;
            }

            // 缓存DOM元素
            if (typeof containerId === 'string') {
                window.PerformanceUtils.elementCache.cache.set(containerId, container);
            }

            // 查找或创建卡牌容器
            let cardsContainer = container.querySelector('.cards');
            if (!cardsContainer) {
                gameLogger.debug('找不到卡牌容器，创建新容器');
                cardsContainer = document.createElement('div');
                cardsContainer.className = 'cards';

                // 不要清空整个容器，只添加卡牌区域
                // 查找或创建第一个子元素之后的位置插入
                if (container.firstChild) {
                    container.insertBefore(cardsContainer, container.firstChild.nextSibling);
                } else {
                    container.appendChild(cardsContainer);
                }
            }

            // 检查是否是历史记录查看模式或需要强制更新
            const forceRebuild = this.isViewingHistory || forceUpdate;

            // 保存当前卡牌数量以便比较
            const currentCards = cardsContainer.children.length;

            // 如果在历史记录模式下或手牌数量已变化，需要重建
            if (forceRebuild || currentCards !== hand.length) {
                gameLogger.debug(`${forceRebuild ? (this.isViewingHistory ? '历史记录模式' : '强制更新模式') : '手牌数量变化'} (${currentCards}->${hand.length})，重建卡牌区域`);

                // 使用DocumentFragment批量更新
                const fragment = document.createDocumentFragment();

                hand.forEach(card => {
                    if (card && typeof card.createCardElement === 'function') {
                        fragment.appendChild(card.createCardElement());
                    }
                });

                // 清空并一次性添加所有卡牌
                cardsContainer.innerHTML = '';
                cardsContainer.appendChild(fragment);
            } else {
                // 数量相同，只更新必要的卡牌
                let hasChange = false;

                for (let i = 0; i < hand.length; i++) {
                    const existingCardElement = cardsContainer.children[i];
                    const card = hand[i];

                    if (card && existingCardElement) {
                        // 检查卡牌是否需要更新
                        const existingSuit = existingCardElement.dataset?.suit;
                        const existingRank = existingCardElement.dataset?.rank;
                        const isHidden = existingCardElement.classList.contains('hidden');

                        if (existingSuit !== card.suit ||
                            existingRank !== card.rank ||
                            isHidden !== card.hidden) {
                            // 只替换需要更新的卡牌
                            hasChange = true;
                            const newCardElement = card.createCardElement();
                            cardsContainer.replaceChild(newCardElement, existingCardElement);
                        }
                    }
                }

                if (hasChange) {
                    gameLogger.debug(`手牌内容已更新: ${containerId}`);
                }
            }
        } catch (error) {
            console.error('更新卡牌显示时出错:', error);
        }
    }

    // 根据不同情况获取自适应延迟时间
    getAdaptiveDelay(baseDelay = 500, situation = 'default') {
        // 如果窗口在后台，大幅降低延迟
        if (document.hidden) {
            return Math.max(100, baseDelay * 0.1);
        }

        // 根据不同情况返回合适的延迟时间，提高系数使延迟更接近设置值
        switch (situation) {
            case 'split': // 分牌操作
                return Math.max(120, baseDelay * 0.9);
            case 'hit': // 要牌操作
                return Math.max(100, baseDelay * 0.8);
            case 'stand': // 停牌操作
                return Math.max(100, baseDelay * 0.8);
            case 'blackjack': // 黑杰克自动处理
                return Math.max(120, baseDelay * 0.9);
            case 'bust': // 爆牌自动处理
                return Math.max(120, baseDelay * 0.9);
            case 'playerChange': // 切换玩家 - 增加更多延迟，确保状态同步
                return Math.max(100, baseDelay * 1.1);
            case 'handChange': // 切换手牌 - 新增此情况
                return Math.max(100, baseDelay * 1.0);
            case 'dealerTurn': // 庄家回合
                return Math.max(150, baseDelay * 0.9);
            default: // 默认情况
                return Math.max(100, baseDelay * 0.8);
        }
    }

    // 为分牌后的第二张牌重新绘制UI
    _forceUpdateSplitHand(playerIndex, handIndex, hand) {
        // 查找对应的手牌容器
        const handContainerId = `player-${playerIndex}-hand-${handIndex}`;
        const handContainer = document.getElementById(handContainerId);

        if (!handContainer) {
            console.warn(`找不到分牌后的手牌容器: ${handContainerId}`);
            return;
        }

        // 获取卡牌容器
        let cardsContainer = handContainer.querySelector('.cards');
        if (!cardsContainer) {
            gameLogger.debug('找不到卡牌容器，创建新容器');
            cardsContainer = document.createElement('div');
            cardsContainer.className = 'cards';
            if (handContainer.firstChild) {
                handContainer.insertBefore(cardsContainer, handContainer.firstChild.nextSibling);
            } else {
                handContainer.appendChild(cardsContainer);
            }
        }

        // 清空并重建所有卡牌
        cardsContainer.innerHTML = '';
        hand.forEach(cardObj => {
            if (cardObj) {
                const cardElement = cardObj.createCardElement();
                cardsContainer.appendChild(cardElement);
            }
        });

        // 更新点数显示
        const pointsDisplay = handContainer.querySelector('.points-display');
        if (pointsDisplay) {
            const points = this.calculateHandValue(hand);
            const pointsText = this.getFormattedPoints(hand);
            pointsDisplay.textContent = pointsText;
            pointsDisplay.classList.toggle('bust', points.value > 21);
        }

        // 更新手牌标题栏，确保下注金额显示在左侧
        const handHeader = handContainer.querySelector('.hand-header');
        if (handHeader) {
            // 获取当前玩家
            const player = this.players[playerIndex];
            if (player) {
                // 清空标题栏
                handHeader.innerHTML = '';

                // 显示下注金额 - 放在左侧
                const betAmount = player.bets[handIndex] || 0;
                if (betAmount > 0) {
                    const betDisplay = document.createElement('div');
                    betDisplay.className = 'bet-display';

                    // 检查是否为加倍或分牌手牌，添加相应标识
                    let betValueHTML = `<span class="bet-value">${betAmount}`;

                    // 添加加倍标识
                    if (hand.doubled) {
                        betValueHTML += `<span class="bet-badge double-badge">加倍</span>`;
                    }

                    // 添加分牌标识
                    if (hand.split) {
                        betValueHTML += `<span class="bet-badge split-badge">分牌</span>`;
                    }

                    // 添加投降标识
                    if (hand.surrendered) {
                        betValueHTML += `<span class="bet-badge surrender-badge">投降</span>`;
                    }

                    betValueHTML += `</span>`;

                    betDisplay.innerHTML = `<span class="bet-label">下注:</span> ${betValueHTML}`;
                    handHeader.appendChild(betDisplay);
                }

                // 手牌编号
                if (player.hands.length > 1) {
                    const handTitle = document.createElement('div');
                    handTitle.className = 'hand-title';
                    handTitle.textContent = `手牌 ${handIndex + 1}`;
                    handHeader.appendChild(handTitle);
                }
            }
        }

        gameLogger.debug(`已强制更新分牌后的手牌容器: ${handContainerId}`);
    }

    // 设置庄家发牌延迟时间
    setDealerCardDelay(delay) {
        // 确保延迟时间是100的倍数
        const normalizedDelay = Math.round(delay / 100) * 100;
        // 确保延迟时间在合理范围内（500-2000毫秒）
        this.dealerCardDelay = Math.max(500, Math.min(2000, normalizedDelay));
        gameLogger.debug('设置庄家发牌延迟为：', this.dealerCardDelay, 'ms');

        // 更新UI显示
        const dealerDelayDisplay = document.getElementById('dealer-delay-display');
        if (dealerDelayDisplay) {
            dealerDelayDisplay.textContent = this.dealerCardDelay;
        }

        return this.dealerCardDelay;
    }

    /**
     * 结算游戏并返回结果数组 - 供模拟引擎调用
     * @returns {Array} 结算结果数组
     */
    settleGame() {
        // 设置游戏状态为结束
        this.gameState = 'ended';

        console.log('Game.settleGame() 被调用 - 返回结算结果数组');

        const dealerPoints = this.calculateHandValue(this.dealerHand);
        const dealerBust = this.isBust(this.dealerHand);
        const dealerBlackjack = this.isBlackjack(this.dealerHand);

        // 结果数组
        let results = [];

        // 遍历所有玩家的所有手牌
        for (const player of this.players) {
            // 如果玩家没有手牌或手牌为空，跳过结算
            if (!player.hands || player.hands.length === 0 || player.hands[0].length === 0) {
                continue;
            }

            player.hands.forEach((hand, handIndex) => {
                // 获取此手牌的下注金额
                const betAmount = player.bets[handIndex] || 0;

                // 如果没有下注，跳过结算
                if (betAmount <= 0) {
                    return;
                }

                // 计算玩家手牌点数
                const playerPoints = this.calculateHandValue(hand);
                const playerBust = this.isBust(hand);
                const playerBlackjack = this.isBlackjack(hand) && !hand.split; // 分牌后的21点不算黑杰克

                // 判断是否为加倍或分牌
                const isDoubleDown = hand.doubled || false;
                const isSplitHand = hand.split || false;

                // 计算原始下注金额（加倍前的金额）
                const originalBet = isDoubleDown ? Math.round(betAmount / 2) : betAmount;

                // 结算结果
                let outcome = '';
                let profit = 0;

                // 如果手牌已经投降
                if (hand.surrendered) {
                    outcome = 'surrender';
                    profit = -originalBet / 2; // 投降返还一半筹码

                    // 更新玩家筹码
                    player.chips += originalBet / 2;

                    // 清除下注
                    player.bets[handIndex] = 0;

                    // 添加到结果数组
                    results.push({
                        playerIndex: this.players.indexOf(player),
                        playerName: player.name,
                        handIndex: handIndex,
                        bet: betAmount,
                        originalBet: originalBet,
                        outcome: outcome,
                        profit: profit,
                        isDoubleDown: isDoubleDown,
                        isSplit: isSplitHand
                    });

                    return;
                }

                // 如果玩家爆牌
                if (playerBust) {
                    outcome = 'bust';
                    profit = -betAmount;

                    // 清除下注
                    player.bets[handIndex] = 0;
                }
                // 如果庄家爆牌且玩家没爆牌
                else if (dealerBust) {
                    outcome = 'win';
                    profit = betAmount;

                    // 返还筹码
                    player.chips += betAmount * 2;

                    // 清除下注
                    player.bets[handIndex] = 0;
                }
                // 如果玩家有黑杰克
                else if (playerBlackjack) {
                    // 如果庄家也有黑杰克，平局
                    if (dealerBlackjack) {
                        outcome = 'push';
                        profit = 0;

                        // 返还原始下注
                        player.chips += betAmount;
                    } else {
                        outcome = 'blackjack';
                        profit = betAmount * 1.5;

                        // 返还原始下注加上1.5倍赔付
                        player.chips += betAmount * 2.5;
                    }

                    // 清除下注
                    player.bets[handIndex] = 0;
                }
                // 如果庄家有黑杰克且玩家没有黑杰克
                else if (dealerBlackjack) {
                    outcome = 'lose';
                    profit = -betAmount;

                    // 清除下注
                    player.bets[handIndex] = 0;
                }
                // 比较点数
                else {
                    if (playerPoints.value > dealerPoints.value) {
                        outcome = 'win';
                        profit = betAmount;

                        // 返还筹码
                        player.chips += betAmount * 2;
                    } else if (playerPoints.value < dealerPoints.value) {
                        outcome = 'lose';
                        profit = -betAmount;
                    } else {
                        outcome = 'push';
                        profit = 0;

                        // 返还原始下注
                        player.chips += betAmount;
                    }

                    // 清除下注
                    player.bets[handIndex] = 0;
                }

                // 更新玩家统计
                if (outcome === 'win' || outcome === 'blackjack') {
                    player.stats.wins++;
                    if (outcome === 'blackjack') {
                        player.stats.blackjacks++;
                    }
                } else if (outcome === 'lose' || outcome === 'bust') {
                    player.stats.losses++;
                } else if (outcome === 'push') {
                    player.stats.pushes++;
                }

                // 更新筹码盈亏
                player.stats.chipProfit += profit;

                // 更新最高/最低筹码记录
                if (player.chips > player.stats.maxChips) {
                    player.stats.maxChips = player.chips;
                }
                if (player.chips < player.stats.minChips) {
                    player.stats.minChips = player.chips;
                }

                // 添加到结果数组
                results.push({
                    playerIndex: this.players.indexOf(player),
                    playerName: player.name,
                    handIndex: handIndex,
                    bet: betAmount,
                    originalBet: originalBet,
                    outcome: outcome,
                    profit: profit,
                    isDoubleDown: isDoubleDown,
                    isSplit: isSplitHand
                });
            });
        }

        console.log(`Game.settleGame() 返回 ${results.length} 条结果记录`);
        return results;
    }
}

// 注释全局游戏实例，防止与ui.js中的实例冲突
// window.game = new Game();

// 显式地将Game类添加到window对象中
window.Game = Game;