/* 测试模式控制面板 */
.test-mode-controls {
    position: fixed; /* 固定定位，不受页面其他元素影响 */
    top: 80px; /* 初始位置，避开顶部导航栏 */
    right: 10px; /* 修改为右侧位置，更符合常规UI习惯 */
    transform: none;
    background: rgba(15, 23, 42, 0.95);
    border-radius: 15px;
    padding: 15px; /* 增加内边距 */
    margin: 0;
    box-shadow: 
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(99, 102, 241, 0.3); /* 增强边框效果 */
    display: none; /* 默认隐藏 */
    flex-direction: column;
    z-index: 999; /* 确保在其他元素之上 */
    max-width: 320px; /* 略微增加宽度 */
    width: auto;
    font-size: 0.85rem;
    user-select: none; /* 防止拖动时选中文本 */
    transition: all 0.3s ease; /* 平滑过渡效果 */
    backdrop-filter: blur(8px); /* 添加模糊效果 */
}

/* 拖动时的样式 */
.test-mode-controls.dragging {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    opacity: 0.92;
    transform: scale(1.02); /* 轻微缩放效果 */
}

/* 测试模式控制面板内部样式 */
.test-control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    cursor: move; /* 拖动手柄光标 */
    background: linear-gradient(90deg, rgba(79, 70, 229, 0.1), rgba(56, 189, 248, 0.1));
    padding: 8px 10px;
    border-radius: 8px;
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.test-mode-controls h3 {
    margin: 0;
    font-size: 1rem;
    flex-grow: 1;
    text-align: center;
    color: #e0e7ff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.test-mode-close {
    font-size: 1.2rem;
    cursor: pointer;
    color: #cbd5e1;
    padding: 0 5px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.test-mode-close:hover {
    color: #f87171;
    background: rgba(248, 113, 113, 0.1);
}

.test-control-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.card-selector {
    display: grid;
    grid-template-columns: 1fr 1fr; /* 两列布局 */
    gap: 8px;
    margin-bottom: 8px;
}

.card-selector select {
    padding: 6px 8px;
    font-size: 0.9rem;
    background-color: rgba(30, 41, 59, 0.8);
    color: #e2e8f0;
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.card-selector select:hover {
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.card-selector select:focus {
    outline: none;
    border-color: rgba(99, 102, 241, 0.7);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 让玩家和手牌选择器占据整行 */
.card-selector select:nth-child(1),
.card-selector select:nth-child(2) {
    grid-column: span 2;
}

.card-selector button {
    padding: 8px 12px;
    font-size: 0.9rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#test-add-card {
    background: linear-gradient(135deg, #4f46e5, #6366f1);
    color: white;
}

#test-add-card:hover {
    background: linear-gradient(135deg, #4338ca, #4f46e5);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(99, 102, 241, 0.3);
}

#test-clear-hand {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
}

#test-clear-hand:hover {
    background: linear-gradient(135deg, #b91c1c, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(239, 68, 68, 0.3);
}

/* 添加工具提示 */
.test-control-tip {
    font-size: 0.8rem;
    color: #94a3b8;
    padding: 6px 10px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 6px;
    border-left: 3px solid #6366f1;
    margin-top: 5px;
}
