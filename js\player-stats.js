/**
 * 玩家统计信息管理类
 * 用于显示玩家的游戏统计信息
 */
class PlayerStats {
    /**
     * 初始化玩家统计信息
     */
    constructor() {
        // 创建统计信息UI
        this.createStatsUI();

        // 绑定HTML中已有的按钮事件
        this.bindButtonEvents();
    }

    /**
     * 绑定HTML中已有的按钮事件
     */
    bindButtonEvents() {
        const statsButton = document.getElementById('player-stats-btn');
        if (statsButton) {
            statsButton.addEventListener('click', () => {
                this.showStats();
            });
        }
    }

    /**
     * 创建统计信息UI
     */
    createStatsUI() {
        // 创建统计信息面板
        const statsPanel = document.createElement('div');
        statsPanel.id = 'player-stats-panel';
        statsPanel.className = 'player-stats-panel';
        statsPanel.style.display = 'none';

        // 创建面板标题
        const panelHeader = document.createElement('div');
        panelHeader.className = 'panel-header';

        const title = document.createElement('h3');
        title.textContent = '玩家统计信息';
        panelHeader.appendChild(title);

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-button';
        closeButton.textContent = '×';
        closeButton.addEventListener('click', () => {
            statsPanel.style.display = 'none';
        });
        panelHeader.appendChild(closeButton);

        statsPanel.appendChild(panelHeader);

        // 创建统计信息内容区域
        const statsContent = document.createElement('div');
        statsContent.className = 'stats-content';

        // 创建筛选器容器
        const filterContainer = document.createElement('div');
        filterContainer.className = 'filter-container';
        filterContainer.style.display = 'flex';
        filterContainer.style.alignItems = 'center';

        // 创建玩家选择器
        const playerFilter = document.createElement('div');
        playerFilter.className = 'filter-item player-filter';
        playerFilter.style.display = 'flex';
        playerFilter.style.alignItems = 'center';

        const playerLabel = document.createElement('label');
        playerLabel.textContent = '选择玩家: ';
        playerLabel.htmlFor = 'stats-player-select';
        playerFilter.appendChild(playerLabel);

        const playerSelect = document.createElement('select');
        playerSelect.id = 'stats-player-select';
        playerSelect.style.height = '36px';
        playerSelect.style.boxSizing = 'border-box';
        playerSelect.style.verticalAlign = 'middle';

        playerSelect.addEventListener('change', () => {
            this.filterStats();
        });

        playerFilter.appendChild(playerSelect);
        filterContainer.appendChild(playerFilter);

        // 创建下注金额筛选器
        const betFilter = document.createElement('div');
        betFilter.className = 'filter-item bet-filter';
        betFilter.style.display = 'flex';
        betFilter.style.alignItems = 'center';

        const betLabel = document.createElement('label');
        betLabel.textContent = '下注金额: ';
        betLabel.htmlFor = 'stats-bet-select';
        betFilter.appendChild(betLabel);

        const betAmountSelect = document.createElement('select');
        betAmountSelect.id = 'stats-bet-filter-select';
        betAmountSelect.style.height = '36px';
        betAmountSelect.style.boxSizing = 'border-box';
        betAmountSelect.style.verticalAlign = 'middle';

        // 添加"全部金额"选项
        const allBetOption = document.createElement('option');
        allBetOption.value = 'all';
        allBetOption.textContent = '全部金额';
        betAmountSelect.appendChild(allBetOption);

        betAmountSelect.addEventListener('change', () => {
            this.filterStats();
        });

        betFilter.appendChild(betAmountSelect);
        filterContainer.appendChild(betFilter);

        // 创建重置筛选按钮
        const resetFilter = document.createElement('button');
        resetFilter.className = 'reset-filter-btn';
        resetFilter.textContent = '重置筛选';
        resetFilter.style.alignSelf = 'center'; // 确保按钮垂直居中
        resetFilter.style.height = '36px';
        resetFilter.style.boxSizing = 'border-box';
        resetFilter.style.verticalAlign = 'middle';
        resetFilter.style.lineHeight = '36px';
        resetFilter.addEventListener('click', () => {
            betAmountSelect.value = 'all';
            this.filterStats();
        });
        filterContainer.appendChild(resetFilter);

        statsContent.appendChild(filterContainer);

        // 创建统计信息容器 (使用新的布局)
        const statsContainer = document.createElement('div');
        statsContainer.className = 'player-stats-container';
        statsContainer.id = 'player-stats-container';
        statsContent.appendChild(statsContainer);

        // 保留旧的统计信息网格以兼容
        const statsGrid = document.createElement('div');
        statsGrid.className = 'stats-grid';
        statsGrid.id = 'stats-grid';
        statsGrid.style.display = 'none'; // 隐藏旧的网格
        statsContent.appendChild(statsGrid);

        statsPanel.appendChild(statsContent);

        // 将统计信息面板添加到文档
        document.body.appendChild(statsPanel);

        // 获取下注金额选择器
        const statsBetSelect = document.getElementById('stats-bet-filter-select');

        this.statsPanel = statsPanel;
        this.playerSelect = playerSelect;
        this.statsBetSelect = statsBetSelect;
        this.statsGrid = statsGrid;
        this.statsContainer = statsContainer;
    }

    /**
     * 显示统计信息面板
     */
    showStats() {
        // 更新玩家选择下拉框
        this.updatePlayerSelector();

        // 更新下注金额选择下拉框
        this.updateBetSelector();

        // 显示面板
        this.statsPanel.style.display = 'block';

        // 筛选并显示统计信息
        this.filterStats();
    }

    /**
     * 更新下注金额选择下拉框
     */
    updateBetSelector() {
        // 获取下注金额选择器
        if (!this.statsBetSelect) return;

        // 保存当前选中的值
        const currentValue = this.statsBetSelect.value;

        // 清空下拉框，保留"全部金额"选项
        while (this.statsBetSelect.options.length > 1) {
            this.statsBetSelect.remove(1);
        }

        // 创建下注金额集合
        const betSet = new Set();

        // 从历史记录中获取所有原始下注金额
        if (window.bettingHistory && window.bettingHistory.history) {
            window.bettingHistory.history.forEach(record => {
                // 使用originalBet字段（如果存在），否则使用bet字段
                const originalBet = record.originalBet !== undefined ? record.originalBet : record.bet;
                betSet.add(originalBet);
            });
        }

        // 将下注金额转换为数组并排序
        const betArray = Array.from(betSet).sort((a, b) => a - b);

        // 添加下注金额选项
        betArray.forEach(bet => {
            const option = document.createElement('option');
            option.value = bet;
            option.textContent = bet;
            this.statsBetSelect.appendChild(option);
        });

        // 尝试恢复之前选中的值
        if (currentValue && this.statsBetSelect.querySelector(`option[value="${currentValue}"]`)) {
            this.statsBetSelect.value = currentValue;
        } else {
            this.statsBetSelect.value = 'all';
        }
    }

    /**
     * 筛选统计信息
     */
    filterStats() {
        const playerIndex = this.playerSelect.value;
        const betFilter = this.statsBetSelect ? this.statsBetSelect.value : 'all';

        this.showPlayerStats(playerIndex, betFilter);
    }

    /**
     * 更新玩家选择下拉框
     */
    updatePlayerSelector() {
        // 清空下拉框
        this.playerSelect.innerHTML = '';

        // 添加"全部玩家"选项
        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = '全部玩家';
        this.playerSelect.appendChild(allOption);

        // 获取所有玩家
        const players = window.game ? window.game.players : [];

        // 添加玩家选项
        players.forEach((player, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = player.name;
            this.playerSelect.appendChild(option);
        });
    }

    /**
     * 显示玩家统计信息
     * @param {string|number} playerIndex - 玩家索引，'all'表示所有玩家
     * @param {string|number} betFilter - 下注金额筛选条件，'all'表示所有金额，数字表示特定下注金额
     */
    showPlayerStats(playerIndex, betFilter = 'all') {
        // 清空统计信息容器
        this.statsContainer.innerHTML = '';
        this.statsGrid.innerHTML = '';

        // 获取玩家
        const players = window.game ? window.game.players : [];

        // 处理"全部玩家"选项
        if (playerIndex === 'all') {
            console.log('显示所有玩家的统计信息');
            // 创建一个合并的统计对象
            const combinedStats = this.getCombinedPlayerStats(betFilter, 'all');
            this.showCombinedPlayerStats(combinedStats, betFilter);
            return;
        }

        const player = players[playerIndex];

        if (!player) {
            console.error('找不到玩家:', playerIndex);
            return;
        }

        // 获取玩家统计信息
        const stats = player.stats;

        // 获取下注历史统计信息
        let historyStats = { totalGames: 0, winRate: 0, totalProfit: 0 };
        if (window.bettingHistory) {
            // 如果有下注金额筛选，使用筛选后的统计信息
            if (betFilter !== 'all') {
                const betAmount = parseInt(betFilter);
                historyStats = this.getFilteredPlayerStats(playerIndex, betAmount);
            } else {
                historyStats = window.bettingHistory.getPlayerStats(playerIndex);
            }
        }

        // 添加筛选信息提示
        this.addFilterInfo(betFilter);

        // 计算额外的统计数据
        // 计算当前筛选玩家的总下注金额
        let totalBetAmount = 0;

        // 获取玩家历史记录
        const playerHistory = window.bettingHistory ?
            (betFilter !== 'all' ?
                this.getFilteredPlayerHistory(playerIndex, parseInt(betFilter)) :
                window.bettingHistory.getPlayerHistory(playerIndex)) :
            [];

        // 计算总下注金额
        // 优先使用从历史记录中计算的总下注金额
        if (playerHistory.length > 0) {
            // 直接从历史记录中累加下注金额
            playerHistory.forEach(record => {
                totalBetAmount += record.bet || 0;
            });
            console.log(`从历史记录计算的总下注金额: ${totalBetAmount}`);
        }
        // 如果没有历史记录，但玩家对象中有总下注金额
        else if (player.totalBetAmount !== undefined) {
            totalBetAmount = player.totalBetAmount;
            console.log(`使用玩家对象中的总下注金额: ${totalBetAmount}`);
        }
        // 然后检查全局玩家统计对象中是否有总下注金额
        else if (window.playerStats && window.playerStats.totalBetAmount !== undefined) {
            // 只有在筛选所有玩家时才使用全局总下注金额
            if (playerIndex === 'all') {
                totalBetAmount = window.playerStats.totalBetAmount;
                console.log(`使用全局玩家统计中的总下注金额: ${totalBetAmount}`);
            } else {
                // 对于单个玩家，使用历史统计数据
                totalBetAmount = historyStats.totalGames * historyStats.avgBet || 0;
                console.log(`使用历史统计数据计算的单个玩家总下注金额: ${totalBetAmount}`);
            }
        }
        // 最后使用历史统计数据
        else {
            totalBetAmount = historyStats.totalGames * historyStats.avgBet || 0;
            console.log(`使用历史统计数据计算的总下注金额: ${totalBetAmount}`);
        }

        // 计算总盈亏
        let playerProfit = historyStats.totalProfit || 0;

        // 1. 游戏结果区域 - 左上
        const resultStatsSection = document.createElement('div');
        resultStatsSection.className = 'player-stats-section';

        const resultStatsTitle = document.createElement('h4');
        resultStatsTitle.className = 'player-stats-title';
        resultStatsTitle.textContent = '游戏结果';
        resultStatsSection.appendChild(resultStatsTitle);

        const resultStatsGrid = document.createElement('div');
        resultStatsGrid.className = 'player-stats-grid';

        // 添加游戏结果卡片
        this.addStatsCard(resultStatsGrid, '获胜次数', historyStats.winCount || 0, 'positive');
        this.addStatsCard(resultStatsGrid, '失败次数', historyStats.loseCount || 0, 'negative');
        this.addStatsCard(resultStatsGrid, '平局次数', historyStats.pushCount || 0);
        this.addStatsCard(resultStatsGrid, '黑杰克次数', historyStats.blackjackCount || 0, historyStats.blackjackCount > 0 ? 'positive' : '');

        resultStatsSection.appendChild(resultStatsGrid);
        this.statsContainer.appendChild(resultStatsSection);

        // 2. 游戏操作区域 - 右上
        const operationStatsSection = document.createElement('div');
        operationStatsSection.className = 'player-stats-section';

        const operationStatsTitle = document.createElement('h4');
        operationStatsTitle.className = 'player-stats-title';
        operationStatsTitle.textContent = '游戏操作';
        operationStatsSection.appendChild(operationStatsTitle);

        const operationStatsGrid = document.createElement('div');
        operationStatsGrid.className = 'player-stats-grid';

        // 从历史记录中计算操作数据
        let doubleCount = 0;
        let doubleWinCount = 0;
        let splitCount = 0;
        let surrenderCount = 0;

        // 使用前面已经获取的playerHistory变量

        // 计算操作次数
        if (playerHistory.length > 0) {

            playerHistory.forEach(record => {
                // 加倍操作
                if (record.bet > record.originalBet) {
                    doubleCount++;
                    if (record.result === '赢' || record.result === '黑杰克') {
                        doubleWinCount++;
                    }
                }

                // 分牌操作 - 直接计算分牌动作次数
                if (record.actionType === '分牌') {
                    splitCount++;
                    console.log(`计算分牌次数: 玩家${record.playerIndex || record.playerName}执行分牌操作，游戏局ID: ${record.gameId || record.game}`);
                }

                // 投降操作
                if (record.result === '投降') {
                    surrenderCount++;
                }
            });

            console.log(`从历史记录计算的操作数据: 加倍=${doubleCount}, 加倍获胜=${doubleWinCount}, 分牌=${splitCount}, 投降=${surrenderCount}`);
        } else {
            // 如果没有历史记录，尝试从玩家对象中获取操作数据
            if (player.stats) {
                doubleCount = player.stats.doubles || 0;
                doubleWinCount = player.stats.doubleWins || 0;
                splitCount = player.stats.splits || 0;
                surrenderCount = player.stats.surrenders || 0;
                console.log(`使用玩家对象中的统计数据: 加倍=${doubleCount}, 加倍获胜=${doubleWinCount}, 分牌=${splitCount}, 投降=${surrenderCount}`);
            } else {
                // 如果玩家对象中没有统计数据，使用全局统计数据
                doubleCount = stats.doubles || 0;
                doubleWinCount = stats.doubleWins || 0;
                splitCount = stats.splits || 0;
                surrenderCount = stats.surrenders || 0;
                console.log(`使用全局统计数据: 加倍=${doubleCount}, 加倍获胜=${doubleWinCount}, 分牌=${splitCount}, 投降=${surrenderCount}`);
            }
        }

        // 添加游戏操作卡片
        this.addStatsCard(operationStatsGrid, '加倍次数', doubleCount);
        this.addStatsCard(operationStatsGrid, '加倍获胜次数', doubleWinCount, doubleWinCount > 0 ? 'positive' : '');
        this.addStatsCard(operationStatsGrid, '分牌次数', splitCount);
        this.addStatsCard(operationStatsGrid, '投降次数', surrenderCount);

        operationStatsSection.appendChild(operationStatsGrid);
        this.statsContainer.appendChild(operationStatsSection);

        // 3. 总体统计区域 - 左下
        const totalStatsSection = document.createElement('div');
        totalStatsSection.className = 'player-stats-section';

        const totalStatsTitle = document.createElement('h4');
        totalStatsTitle.className = 'player-stats-title';
        totalStatsTitle.textContent = '总体统计';
        totalStatsSection.appendChild(totalStatsTitle);

        const totalStatsGrid = document.createElement('div');
        totalStatsGrid.className = 'player-stats-grid';
        totalStatsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';

        // 添加总体统计卡片
        this.addStatsCard(totalStatsGrid, '总局数', historyStats.totalGames || 0);
        this.addStatsCard(totalStatsGrid, '总下注金额', totalBetAmount);

        // 使用前面计算的playerProfit
        const totalProfitClass = playerProfit >= 0 ? 'positive' : 'negative';

        // 计算玩家优势
        let playerEdge = 0;
        if (totalBetAmount > 0) {
            playerEdge = (playerProfit / totalBetAmount) * 100;
            console.log(`计算玩家优势: ${playerProfit} / ${totalBetAmount} * 100 = ${playerEdge.toFixed(2)}%`);
        }
        const playerEdgeClass = playerEdge >= 0 ? 'positive' : 'negative';

        this.addStatsCard(totalStatsGrid, '总盈亏', playerProfit, totalProfitClass);
        this.addStatsCard(totalStatsGrid, '玩家优势', playerEdge.toFixed(2) + '%', playerEdgeClass);

        totalStatsSection.appendChild(totalStatsGrid);
        this.statsContainer.appendChild(totalStatsSection);

        // 4. 记录数据区域 - 右下
        const recordStatsSection = document.createElement('div');
        recordStatsSection.className = 'player-stats-section';

        const recordStatsTitle = document.createElement('h4');
        recordStatsTitle.className = 'player-stats-title';
        recordStatsTitle.textContent = '记录数据';
        recordStatsSection.appendChild(recordStatsTitle);

        const recordStatsGrid = document.createElement('div');
        recordStatsGrid.className = 'player-stats-grid';
        recordStatsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';

        // 从历史记录中计算记录数据
        let largestWin = 0;
        let largestLoss = 0;
        let longestWinStreak = 0;
        let longestLoseStreak = 0;
        let currentWinStreak = 0;
        let currentLoseStreak = 0;
        let highestChips = player.chips;
        let lowestChips = player.chips;

        // 如果有历史记录，计算记录数据
        if (playerHistory.length > 0) {
            // 排序历史记录，按游戏局数排序
            const sortedHistory = [...playerHistory].sort((a, b) => a.gameId - b.gameId);
            let currentChips = player.initialChips || 10000;

            sortedHistory.forEach(record => {
                // 更新最大赢取和最大损失
                if (record.profit > 0 && record.profit > largestWin) {
                    largestWin = record.profit;
                }
                if (record.profit < 0 && Math.abs(record.profit) > largestLoss) {
                    largestLoss = Math.abs(record.profit);
                }

                // 更新连胜连败
                if (record.result === '赢' || record.result === '黑杰克') {
                    currentWinStreak++;
                    currentLoseStreak = 0;
                    if (currentWinStreak > longestWinStreak) {
                        longestWinStreak = currentWinStreak;
                    }
                } else if (record.result === '输' || record.result === '爆牌' || record.result === '投降') {
                    currentLoseStreak++;
                    currentWinStreak = 0;
                    if (currentLoseStreak > longestLoseStreak) {
                        longestLoseStreak = currentLoseStreak;
                    }
                } else {
                    // 平局，重置连胜连败
                    currentWinStreak = 0;
                    currentLoseStreak = 0;
                }

                // 更新筹码记录
                currentChips += record.profit;
                if (currentChips > highestChips) {
                    highestChips = currentChips;
                }
                if (currentChips < lowestChips) {
                    lowestChips = currentChips;
                }
            });

            console.log(`从历史记录计算的记录数据: 最大赢取=${largestWin}, 最大损失=${largestLoss}, 最长连胜=${longestWinStreak}, 最长连败=${longestLoseStreak}, 最高筹码=${highestChips}, 最低筹码=${lowestChips}`);
        } else {
            // 如果没有历史记录，使用玩家统计数据
            largestWin = stats.largestWin || 0;
            largestLoss = stats.largestLoss || 0;
            longestWinStreak = stats.longestWinStreak || 0;
            longestLoseStreak = stats.longestLoseStreak || 0;
            highestChips = stats.maxChips || player.chips;
            lowestChips = stats.minChips || player.chips;
            console.log(`使用玩家统计数据: 最大赢取=${largestWin}, 最大损失=${largestLoss}, 最长连胜=${longestWinStreak}, 最长连败=${longestLoseStreak}, 最高筹码=${highestChips}, 最低筹码=${lowestChips}`);
        }

        // 添加记录数据卡片
        this.addStatsCard(recordStatsGrid, '单局最大赢取', largestWin, 'positive');
        this.addStatsCard(recordStatsGrid, '单局最大损失', largestLoss, 'negative');
        this.addStatsCard(recordStatsGrid, '最长连胜', longestWinStreak, 'positive');
        this.addStatsCard(recordStatsGrid, '最长连败', longestLoseStreak, 'negative');
        this.addStatsCard(recordStatsGrid, '最高筹码', highestChips, 'positive');
        this.addStatsCard(recordStatsGrid, '最低筹码', lowestChips, 'negative');
        this.addStatsCard(recordStatsGrid, '平均下注', historyStats.avgBet || 0);
        this.addStatsCard(recordStatsGrid, '胜率', `${historyStats.winRate || 0}%`, parseFloat(historyStats.winRate) > 50 ? 'positive' : '');

        recordStatsSection.appendChild(recordStatsGrid);
        this.statsContainer.appendChild(recordStatsSection);
    }

    /**
     * 添加统计卡片
     * @param {HTMLElement} container - 容器元素
     * @param {string} title - 标题
     * @param {string|number} value - 值
     * @param {string} valueClass - 值的CSS类
     */
    addStatsCard(container, title, value, valueClass = '') {
        const card = document.createElement('div');
        card.className = 'player-stats-card';

        const titleElement = document.createElement('div');
        titleElement.className = 'player-stats-card-title';
        titleElement.textContent = title;
        card.appendChild(titleElement);

        const valueElement = document.createElement('div');
        valueElement.className = `player-stats-card-value ${valueClass}`;

        // 如果是数字，格式化显示
        if (typeof value === 'number') {
            if (title === '总盈亏' && value > 0) {
                valueElement.textContent = `+${value}`;
            } else {
                valueElement.textContent = value.toLocaleString();
            }
        } else {
            valueElement.textContent = value;
        }

        card.appendChild(valueElement);
        container.appendChild(card);
    }

    /**
     * 添加筛选信息提示
     * @param {string|number} betFilter - 下注金额筛选条件
     */
    addFilterInfo(betFilter) {
        // 只在有特定筛选条件时才显示筛选信息
        if (betFilter !== 'all') {
            // 创建筛选信息
            const filterInfo = document.createElement('div');
            filterInfo.className = 'filter-info';
            filterInfo.textContent = `筛选: ${betFilter} 金额`;
            filterInfo.style.color = '#94a3b8';
            filterInfo.style.fontSize = '0.85rem';
            filterInfo.style.textAlign = 'right';
            filterInfo.style.padding = '0 10px';
            filterInfo.style.marginBottom = '10px';

            this.statsContainer.appendChild(filterInfo);
        }
    }

    /**
     * 获取合并的玩家统计数据
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string|number} playerIndex - 玩家索引，'all'表示所有玩家
     * @returns {Object} 合并的玩家统计数据
     */
    getCombinedPlayerStats(betFilter, playerIndex = 'all') {
        // 获取所有玩家
        const players = window.game ? window.game.players : [];
        if (!players.length || !window.bettingHistory) {
            return {
                totalGames: 0,
                winRate: 0,
                winCount: 0,
                loseCount: 0,
                pushCount: 0,
                blackjackCount: 0,
                totalProfit: 0,
                avgBet: 0,
                doubles: 0,
                doubleWins: 0,
                splits: 0,
                surrenders: 0,
                largestWin: 0,
                largestLoss: 0,
                longestWinStreak: 0,
                longestLoseStreak: 0,
                maxChips: 0,
                minChips: 0
            };
        }

        // 初始化合并统计数据
        const combinedStats = {
            totalGames: 0,
            winCount: 0,
            loseCount: 0,
            pushCount: 0,
            blackjackCount: 0,
            totalProfit: 0,
            totalBetAmount: 0,
            avgBet: 0,
            winRate: 0,
            doubles: 0,
            doubleWins: 0,
            splits: 0,
            surrenders: 0,
            largestWin: 0,
            largestLoss: 0,
            longestWinStreak: 0,
            longestLoseStreak: 0,
            maxChips: 0,
            minChips: 0
        };

        // 获取所有玩家的历史记录
        let allHistory = [];

        // 根据筛选条件获取历史记录
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            players.forEach((_, index) => {
                const playerHistory = this.getFilteredPlayerHistory(index, betAmount);
                allHistory = allHistory.concat(playerHistory);
            });
        } else {
            allHistory = window.bettingHistory.history || [];
        }

        // 计算基本统计数据
        combinedStats.totalGames = allHistory.length;

        // 计算游戏结果统计
        allHistory.forEach(record => {
            // 下注金额
            combinedStats.totalBetAmount += record.bet;

            // 处理结果
            if (record.result === '赢' || record.result === '黑杰克') {
                combinedStats.winCount++;
                combinedStats.totalProfit += record.profit;
                if (record.result === '黑杰克') {
                    combinedStats.blackjackCount++;
                }
            } else if (record.result === '输' || record.result === '爆牌' || record.result === '投降') {
                combinedStats.loseCount++;
                combinedStats.totalProfit += record.profit; // profit已经是负数
                if (record.result === '投降') {
                    combinedStats.surrenders++;
                }
            } else if (record.result === '平') {
                combinedStats.pushCount++;
            }

            // 识别加倍操作
            if (record.bet > record.originalBet) {
                combinedStats.doubles++;
                if (record.result === '赢' || record.result === '黑杰克') {
                    combinedStats.doubleWins++;
                }
            }

            // 更新最大赢取和最大损失
            if (record.profit > 0 && record.profit > combinedStats.largestWin) {
                combinedStats.largestWin = record.profit;
            }
            if (record.profit < 0 && Math.abs(record.profit) > combinedStats.largestLoss) {
                combinedStats.largestLoss = Math.abs(record.profit);
            }
        });

        // 计算分牌次数 - 直接计算分牌动作次数
        // 如果是筛选单个玩家，只计算该玩家的分牌次数
        if (betFilter !== 'all' && playerIndex !== 'all') {
            const playerIdx = parseInt(playerIndex);

            // 只处理当前选择的玩家的记录
            allHistory.forEach(record => {
                // 只计算当前选择的玩家的分牌动作次数
                if (record.playerIndex === playerIdx && record.actionType === '分牌') {
                    combinedStats.splits++;
                    console.log(`计算分牌次数: 玩家${playerIdx}执行分牌操作，游戏局ID: ${record.gameId || record.game}`);
                }
            });
        } else {
            // 如果是筛选所有玩家，计算所有玩家的分牌动作次数
            allHistory.forEach(record => {
                if (record.actionType === '分牌') {
                    combinedStats.splits++;
                    console.log(`计算分牌次数: 玩家${record.playerIndex}执行分牌操作，游戏局ID: ${record.gameId || record.game}`);
                }
            });
        }

        // 计算平均下注和胜率
        combinedStats.avgBet = combinedStats.totalGames > 0 ? Math.round(combinedStats.totalBetAmount / combinedStats.totalGames) : 0;
        combinedStats.winRate = combinedStats.totalGames > 0 ? ((combinedStats.winCount / combinedStats.totalGames) * 100).toFixed(1) : '0.0';

        // 计算连胜连败和筹码记录需要按玩家分别计算，然后取最大值
        players.forEach((currentPlayer, index) => {
            // 获取玩家历史记录
            const playerHistory = betFilter !== 'all' ?
                this.getFilteredPlayerHistory(index, parseInt(betFilter)) :
                window.bettingHistory.getPlayerHistory(index);

            if (playerHistory.length > 0) {
                // 排序历史记录，按游戏局数排序
                const sortedHistory = [...playerHistory].sort((a, b) => a.gameId - b.gameId);
                let currentChips = currentPlayer.initialChips || 10000;
                let currentWinStreak = 0;
                let currentLoseStreak = 0;
                let longestWinStreak = 0;
                let longestLoseStreak = 0;
                let highestChips = currentChips;
                let lowestChips = currentChips;

                sortedHistory.forEach(record => {
                    // 更新连胜连败
                    if (record.result === '赢' || record.result === '黑杰克') {
                        currentWinStreak++;
                        currentLoseStreak = 0;
                        if (currentWinStreak > longestWinStreak) {
                            longestWinStreak = currentWinStreak;
                        }
                    } else if (record.result === '输' || record.result === '爆牌' || record.result === '投降') {
                        currentLoseStreak++;
                        currentWinStreak = 0;
                        if (currentLoseStreak > longestLoseStreak) {
                            longestLoseStreak = currentLoseStreak;
                        }
                    } else {
                        // 平局，重置连胜连败
                        currentWinStreak = 0;
                        currentLoseStreak = 0;
                    }

                    // 更新筹码记录
                    currentChips += record.profit;
                    if (currentChips > highestChips) {
                        highestChips = currentChips;
                    }
                    if (currentChips < lowestChips) {
                        lowestChips = currentChips;
                    }
                });

                // 更新合并统计数据，取最大值
                combinedStats.longestWinStreak = Math.max(combinedStats.longestWinStreak, longestWinStreak);
                combinedStats.longestLoseStreak = Math.max(combinedStats.longestLoseStreak, longestLoseStreak);
                combinedStats.maxChips = Math.max(combinedStats.maxChips, highestChips);
                combinedStats.minChips = combinedStats.minChips === 0 ? lowestChips : Math.min(combinedStats.minChips, lowestChips);
            }
        });

        return combinedStats;
    }

    /**
     * 显示合并的玩家统计信息
     * @param {Object} stats - 合并的玩家统计数据
     * @param {string|number} betFilter - 下注金额筛选条件
     */
    showCombinedPlayerStats(stats, betFilter) {
        // 添加筛选信息提示
        this.addFilterInfo(betFilter);

        // 不再添加全部玩家提示，保持界面整洁

        // 1. 游戏结果区域 - 左上
        const resultStatsSection = document.createElement('div');
        resultStatsSection.className = 'player-stats-section';

        const resultStatsTitle = document.createElement('h4');
        resultStatsTitle.className = 'player-stats-title';
        resultStatsTitle.textContent = '游戏结果';
        resultStatsSection.appendChild(resultStatsTitle);

        const resultStatsGrid = document.createElement('div');
        resultStatsGrid.className = 'player-stats-grid';

        // 添加游戏结果卡片
        this.addStatsCard(resultStatsGrid, '获胜次数', stats.winCount, 'positive');
        this.addStatsCard(resultStatsGrid, '失败次数', stats.loseCount, 'negative');
        this.addStatsCard(resultStatsGrid, '平局次数', stats.pushCount);
        this.addStatsCard(resultStatsGrid, '黑杰克次数', stats.blackjackCount, stats.blackjackCount > 0 ? 'positive' : '');

        resultStatsSection.appendChild(resultStatsGrid);
        this.statsContainer.appendChild(resultStatsSection);

        // 2. 游戏操作区域 - 右上
        const operationStatsSection = document.createElement('div');
        operationStatsSection.className = 'player-stats-section';

        const operationStatsTitle = document.createElement('h4');
        operationStatsTitle.className = 'player-stats-title';
        operationStatsTitle.textContent = '游戏操作';
        operationStatsSection.appendChild(operationStatsTitle);

        const operationStatsGrid = document.createElement('div');
        operationStatsGrid.className = 'player-stats-grid';

        // 添加游戏操作卡片
        this.addStatsCard(operationStatsGrid, '加倍次数', stats.doubles);
        this.addStatsCard(operationStatsGrid, '加倍获胜次数', stats.doubleWins, stats.doubleWins > 0 ? 'positive' : '');
        this.addStatsCard(operationStatsGrid, '分牌次数', stats.splits);
        this.addStatsCard(operationStatsGrid, '投降次数', stats.surrenders);

        operationStatsSection.appendChild(operationStatsGrid);
        this.statsContainer.appendChild(operationStatsSection);

        // 3. 总体统计区域 - 左下
        const totalStatsSection = document.createElement('div');
        totalStatsSection.className = 'player-stats-section';

        const totalStatsTitle = document.createElement('h4');
        totalStatsTitle.className = 'player-stats-title';
        totalStatsTitle.textContent = '总体统计';
        totalStatsSection.appendChild(totalStatsTitle);

        const totalStatsGrid = document.createElement('div');
        totalStatsGrid.className = 'player-stats-grid';
        totalStatsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';

        // 添加总体统计卡片
        this.addStatsCard(totalStatsGrid, '总局数', stats.totalGames);
        this.addStatsCard(totalStatsGrid, '总下注金额', stats.totalBetAmount);

        // 计算总盈亏
        const totalProfitClass = stats.totalProfit >= 0 ? 'positive' : 'negative';

        // 计算玩家优势
        let playerEdge = 0;
        if (stats.totalBetAmount > 0) {
            playerEdge = (stats.totalProfit / stats.totalBetAmount) * 100;
        }
        const playerEdgeClass = playerEdge >= 0 ? 'positive' : 'negative';

        this.addStatsCard(totalStatsGrid, '总盈亏', stats.totalProfit, totalProfitClass);
        this.addStatsCard(totalStatsGrid, '玩家优势', playerEdge.toFixed(2) + '%', playerEdgeClass);

        totalStatsSection.appendChild(totalStatsGrid);
        this.statsContainer.appendChild(totalStatsSection);

        // 4. 记录数据区域 - 右下
        const recordStatsSection = document.createElement('div');
        recordStatsSection.className = 'player-stats-section';

        const recordStatsTitle = document.createElement('h4');
        recordStatsTitle.className = 'player-stats-title';
        recordStatsTitle.textContent = '记录数据';
        recordStatsSection.appendChild(recordStatsTitle);

        const recordStatsGrid = document.createElement('div');
        recordStatsGrid.className = 'player-stats-grid';
        recordStatsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';

        // 添加记录数据卡片
        this.addStatsCard(recordStatsGrid, '单局最大赢取', stats.largestWin, 'positive');
        this.addStatsCard(recordStatsGrid, '单局最大损失', stats.largestLoss, 'negative');
        this.addStatsCard(recordStatsGrid, '最长连胜', stats.longestWinStreak, 'positive');
        this.addStatsCard(recordStatsGrid, '最长连败', stats.longestLoseStreak, 'negative');
        this.addStatsCard(recordStatsGrid, '最高筹码', stats.maxChips, 'positive');
        this.addStatsCard(recordStatsGrid, '最低筹码', stats.minChips, 'negative');
        this.addStatsCard(recordStatsGrid, '平均下注', stats.avgBet);
        this.addStatsCard(recordStatsGrid, '胜率', `${stats.winRate}%`, parseFloat(stats.winRate) > 50 ? 'positive' : '');

        recordStatsSection.appendChild(recordStatsGrid);
        this.statsContainer.appendChild(recordStatsSection);
    }

    /**
     * 计算玩家优势
     * @param {Object} stats - 统计数据
     * @returns {number} 玩家优势百分比
     */
    calculatePlayerEdge(stats) {
        if (!stats || !stats.totalGames || stats.totalGames === 0) return 0;

        // 简单计算：总盈亏 / 总下注金额
        const totalBetAmount = stats.totalGames * stats.avgBet;
        if (totalBetAmount === 0) return 0;

        return ((stats.totalProfit / totalBetAmount) * 100).toFixed(2);
    }

    /**
     * 计算玩家平均点数
     * @returns {string} 平均点数
     */
    calculatePlayerAveragePoints() {
        // 这里需要更多数据才能准确计算，暂时返回一个估计值
        return '17.5';
    }

    /**
     * 更新统计显示
     * 用于在数据变化后刷新统计面板
     */
    updateStatsDisplay() {
        console.log('更新玩家统计显示');
        // 如果统计面板正在显示，重新筛选并显示统计信息
        if (this.statsPanel && this.statsPanel.style.display !== 'none') {
            this.filterStats();
        }
    }

    /**
     * 创建统计信息卡片 (旧版兼容方法)
     * @param {string} label - 标签
     * @param {string|number} value - 值
     * @param {string} valueClass - 值的CSS类
     */
    createStatCard(label, value, valueClass = '') {
        const card = document.createElement('div');
        card.className = 'stat-card';

        const labelElement = document.createElement('div');
        labelElement.className = 'stat-label';
        labelElement.textContent = label;
        card.appendChild(labelElement);

        const valueElement = document.createElement('div');
        valueElement.className = `stat-value ${valueClass}`;

        // 如果是数字，格式化显示
        if (typeof value === 'number') {
            if (label === '总盈亏' && value > 0) {
                valueElement.textContent = `+${value}`;
            } else {
                valueElement.textContent = value;
            }
        } else {
            valueElement.textContent = value;
        }

        card.appendChild(valueElement);

        this.statsGrid.appendChild(card);
    }

    /**
     * 获取筛选后的玩家历史记录
     * @param {number} playerIndex - 玩家索引
     * @param {number} betAmount - 下注金额
     * @returns {Array} 筛选后的玩家历史记录
     */
    getFilteredPlayerHistory(playerIndex, betAmount) {
        if (!window.bettingHistory || !window.bettingHistory.history) {
            return [];
        }

        // 筛选玩家和原始下注金额匹配的历史记录
        return window.bettingHistory.history.filter(record => {
            // 使用originalBet字段（如果存在），否则使用bet字段
            const originalBet = record.originalBet !== undefined ? record.originalBet : record.bet;
            return record.playerIndex === parseInt(playerIndex) && originalBet === betAmount;
        });
    }

    /**
     * 获取筛选后的玩家统计信息
     * @param {number} playerIndex - 玩家索引
     * @param {number} betAmount - 下注金额
     * @returns {Object} 筛选后的玩家统计数据
     */
    getFilteredPlayerStats(playerIndex, betAmount) {
        if (!window.bettingHistory || !window.bettingHistory.history) {
            return {
                totalGames: 0,
                winRate: 0,
                winCount: 0,
                loseCount: 0,
                pushCount: 0,
                blackjackCount: 0,
                totalProfit: 0,
                avgBet: 0
            };
        }

        // 获取筛选后的历史记录
        const filteredHistory = this.getFilteredPlayerHistory(playerIndex, betAmount);

        if (filteredHistory.length === 0) {
            return {
                totalGames: 0,
                winRate: 0,
                winCount: 0,
                loseCount: 0,
                pushCount: 0,
                blackjackCount: 0,
                totalProfit: 0,
                avgBet: 0
            };
        }

        // 计算统计数据
        let totalProfit = 0;
        let winCount = 0;
        let loseCount = 0;
        let pushCount = 0;
        let blackjackCount = 0;
        let totalBet = 0;

        filteredHistory.forEach(record => {
            totalProfit += record.profit;
            totalBet += record.bet;

            if (record.result === '赢' || record.result === '黑杰克') {
                winCount++;
                if (record.result === '黑杰克') {
                    blackjackCount++;
                }
            } else if (record.result === '输' || record.result === '爆牌' || record.result === '投降') {
                loseCount++;
            } else if (record.result === '平') {
                pushCount++;
            }
        });

        const totalGames = filteredHistory.length;
        const winRate = totalGames > 0 ? ((winCount / totalGames) * 100).toFixed(1) : '0.0';
        const avgBet = totalGames > 0 ? Math.round(totalBet / totalGames) : 0;

        return {
            totalGames,
            winRate,
            winCount,
            loseCount,
            pushCount,
            blackjackCount,
            totalProfit,
            avgBet
        };
    }
}

// 创建全局实例
window.playerStats = new PlayerStats();