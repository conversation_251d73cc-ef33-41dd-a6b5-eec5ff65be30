/**
 * 21点自动策略模块
 * 根据21点基本策略表提供最佳操作建议
 * 策略判断优先级：投降 -> 分牌 -> 加倍 -> 要牌 -> 停牌
 */

// 创建日志记录器
const autoStrategyLogger = window.Logger ? window.Logger.getLogger('AutoStrategy') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

// 基本策略表
const STRATEGY_TABLE = {
    // 策略代码说明
    legend: {
        "H": "要牌 (Hit)",
        "S": "停牌 (Stand)",
        "D": "加倍 (Double)",
        "P": "分牌 (Split)",
        "R": "投降 (Surrender)"
    },

    // 庄家明牌
    dealer_cards: ["2", "3", "4", "5", "6", "7", "8", "9", "10", "A"],

    // 硬牌策略 (玩家手牌没有A，或者A算作1点)
    hard_totals: {
        player_hands: {
            "5-7":   ["H", "H", "H", "H", "H", "H", "H", "H", "H", "H"],
            "8":     ["H", "H", "H", "H", "H", "H", "H", "H", "H", "H"],
            "9":     ["H", "D", "D", "D", "D", "H", "H", "H", "H", "H"],
            "10":    ["D", "D", "D", "D", "D", "D", "D", "D", "H", "H"],
            "11":    ["D", "D", "D", "D", "D", "D", "D", "D", "D", "H"],
            "12":    ["H", "H", "S", "S", "S", "H", "H", "H", "H", "H"],
            "13":    ["S", "S", "S", "S", "S", "H", "H", "H", "H", "H"],
            "14":    ["S", "S", "S", "S", "S", "H", "H", "H", "R", "H"],
            "15":    ["S", "S", "S", "S", "S", "H", "H", "H", "R", "H"],
            "16":    ["S", "S", "S", "S", "S", "H", "H", "R", "R", "H"],
            "17-21": ["S", "S", "S", "S", "S", "S", "S", "S", "S", "S"]
        }
    },

    // 软牌策略 (玩家手牌有A，且A可以算作11点)
    // 根据21点基本策略表.md文件
    soft_totals: {
        player_hands: {
            "A-2":   ["H", "H", "H", "D", "D", "H", "H", "H", "H", "H"],
            "A-3":   ["H", "H", "H", "D", "D", "H", "H", "H", "H", "H"],
            "A-4":   ["H", "H", "D", "D", "D", "H", "H", "H", "H", "H"],
            "A-5":   ["H", "H", "D", "D", "D", "H", "H", "H", "H", "H"],
            "A-6":   ["H", "D", "D", "D", "D", "H", "H", "H", "H", "H"],
            "A-7":   ["D", "D", "D", "D", "D", "S", "S", "H", "H", "H"],
            "A-8":   ["S", "S", "S", "S", "S", "S", "S", "S", "S", "S"],
            "A-9":   ["S", "S", "S", "S", "S", "S", "S", "S", "S", "S"]
        }
    },

    // 对牌策略 (玩家手牌为两张点数相同的牌)
    pairs: {
        player_hands: {
            "A-A":   ["P", "P", "P", "P", "P", "P", "P", "P", "P", "P"],
            "10-10": ["S", "S", "S", "S", "S", "S", "S", "S", "S", "S"],
            "9-9":   ["P", "P", "P", "P", "P", "S", "P", "P", "S", "S"],
            "8-8":   ["P", "P", "P", "P", "P", "P", "P", "P", "R", "P"],
            "7-7":   ["P", "P", "P", "P", "P", "P", "H", "H", "R", "H"],
            "6-6":   ["P", "P", "P", "P", "P", "H", "H", "H", "H", "H"],
            "5-5":   ["D", "D", "D", "D", "D", "D", "D", "D", "H", "H"],
            "4-4":   ["H", "H", "H", "P", "P", "H", "H", "H", "H", "H"],
            "3-3":   ["P", "P", "P", "P", "P", "P", "H", "H", "H", "H"],
            "2-2":   ["P", "P", "P", "P", "P", "P", "H", "H", "H", "H"]
        }
    }
};

/**
 * 自动策略类
 * 根据玩家手牌和庄家明牌提供最佳操作建议
 */
class AutoStrategy {
    constructor() {
        this.strategyTable = STRATEGY_TABLE;
        autoStrategyLogger.info('自动策略模块已初始化');
    }

    /**
     * 获取庄家明牌的索引
     * @param {Card} dealerCard - 庄家明牌
     * @returns {number} 庄家明牌在策略表中的索引
     */
    getDealerCardIndex(dealerCard) {
        if (!dealerCard) return -1;

        const dealerValue = dealerCard.rank;
        // 将JQK统一视为10
        const normalizedValue = ['J', 'Q', 'K'].includes(dealerValue) ? '10' : dealerValue;

        const index = this.strategyTable.dealer_cards.indexOf(normalizedValue);

        // 添加详细日志，帮助排查问题
        autoStrategyLogger.debug('庄家明牌索引', {
            dealerRank: dealerValue,
            normalizedValue: normalizedValue,
            index: index
        });

        return index;
    }

    /**
     * 检查是否为对子
     * @param {Array} hand - 玩家手牌
     * @returns {boolean} 是否为对子
     */
    isPair(hand) {
        if (hand.length !== 2) return false;

        // 获取两张牌的点数
        const card1Value = this.getCardValue(hand[0]);
        const card2Value = this.getCardValue(hand[1]);

        return card1Value === card2Value;
    }

    /**
     * 获取卡牌的标准点数值（用于对子判断）
     * @param {Card} card - 卡牌对象
     * @returns {number} 卡牌点数
     */
    getCardValue(card) {
        if (!card) return 0;

        // 将JQK统一视为10
        if (['J', 'Q', 'K', '10'].includes(card.rank)) {
            return 10;
        }

        return card.rank === 'A' ? 11 : parseInt(card.rank);
    }

    /**
     * 获取对子的策略键
     * @param {Array} hand - 玩家手牌
     * @returns {string} 策略键
     */
    getPairKey(hand) {
        if (hand.length !== 2) return null;

        const cardValue = this.getCardValue(hand[0]);

        if (cardValue === 11) { // A
            return 'A-A';
        } else if (cardValue === 10) { // 10, J, Q, K
            return '10-10';
        } else {
            return `${cardValue}-${cardValue}`;
        }
    }

    /**
     * 获取软牌的策略键
     * @param {Object} pointsInfo - 点数信息对象，包含value和isSoft
     * @returns {string} 策略键
     */
    getSoftHandKey(hand, pointsInfo) {
        // 确保是软手
        if (!pointsInfo.isSoft) return null;

        // 计算除A之外的点数总和
        const nonAceSum = pointsInfo.value - 11;

        // 添加详细日志，帮助排查问题
        autoStrategyLogger.debug('软牌策略键计算', {
            pointsValue: pointsInfo.value,
            nonAceSum: nonAceSum,
            hand: hand ? hand.map(c => c.toString()).join(',') : 'N/A'
        });

        // 软手策略键格式为 "A-X"，其中X是除A之外的点数总和
        // 如果nonAceSum大于9，则使用A-9作为键（策略表中最大是A-9）
        // 如果nonAceSum小于2，则使用A-2作为键（策略表中最小是A-2）
        if (nonAceSum > 9) {
            return 'A-9';
        } else if (nonAceSum < 2) {
            return 'A-2';
        } else {
            return `A-${nonAceSum}`;
        }
    }

    /**
     * 获取硬牌的策略键
     * @param {number} total - 手牌总点数
     * @returns {string} 策略键
     */
    getHardHandKey(total) {
        if (total <= 7) return '5-7';
        if (total >= 17) return '17-21';
        return total.toString();
    }

    /**
     * 内部计算手牌点数方法
     * 当没有game对象时使用此方法计算点数
     * @param {Array} cards - 手牌数组
     * @returns {Object} 点数信息对象，包含value和isSoft
     */
    calculateHandValue(cards) {
        if (!cards || !Array.isArray(cards) || cards.length === 0) {
            return { value: 0, isSoft: false };
        }

        let sum = 0;
        let aces = 0;
        let isSoft = false;

        // 计算非A牌的点数和A的数量
        for (const card of cards) {
            if (card.rank === 'A') {
                aces++;
            } else {
                // 使用getCardValue方法获取牌的点数
                const cardValue = this.getCardValue(card);
                sum += cardValue;
            }
        }

        // 初始时所有A都算作1点
        sum += aces;

        // 尝试将一张A升级为11点（最多只有一张A可以算作11点）
        if (aces > 0 && sum + 10 <= 21) {
            sum += 10;
            isSoft = true;
        }

        // 记录详细日志
        autoStrategyLogger.debug('计算手牌点数', {
            cards: cards.map(c => c.toString()).join(','),
            sum: sum,
            aces: aces,
            isSoft: isSoft
        });

        return { value: sum, isSoft: isSoft };
    }

    /**
     * 获取策略建议
     * @param {Array} hand - 玩家手牌
     * @param {Card} dealerCard - 庄家明牌
     * @param {boolean} canSplit - 是否可以分牌
     * @param {string} gameState - 游戏状态
     * @param {Game} game - 游戏实例，用于计算手牌点数
     * @returns {string} 策略建议（H, S, D, P, R）
     */
    getAction(hand, dealerCard, canSplit = true, gameState = 'playing', game = null) {
        try {
            // 检查参数有效性
            if (!hand || !Array.isArray(hand) || hand.length === 0 || !dealerCard) {
                autoStrategyLogger.error('无效的参数', { hand, dealerCard });
                return 'S'; // 默认停牌
            }

            // 获取庄家明牌索引
            const dealerIndex = this.getDealerCardIndex(dealerCard);
            if (dealerIndex === -1) {
                autoStrategyLogger.error('无法识别庄家明牌', dealerCard);
                return 'S'; // 默认停牌
            }

            // 记录详细日志
            autoStrategyLogger.debug('获取策略', {
                hand: hand.map(c => c.toString()).join(','),
                dealerCard: dealerCard.toString(),
                dealerIndex: dealerIndex
            });

            // 获取手牌点数信息
            // 如果没有传入game参数，尝试使用全局game对象
            if (!game && window.game) {
                game = window.game;
            }

            // 如果仍然没有game对象，使用内部方法计算点数
            let pointsInfo;
            if (game && typeof game.calculateHandValue === 'function') {
                pointsInfo = game.calculateHandValue(hand);
            } else {
                // 内部计算点数方法
                pointsInfo = this.calculateHandValue(hand);
            }

            // 记录点数信息
            autoStrategyLogger.debug('手牌点数', {
                value: pointsInfo.value,
                isSoft: pointsInfo.isSoft
            });

            // 按照优先级顺序判断策略：投降 -> 分牌 -> 加倍 -> 要牌/停牌

            // 1. 检查是否可以投降（只有初始两张牌可以投降）
            if (hand.length === 2 && gameState === 'playing') {
                // 检查对子是否应该投降
                if (this.isPair(hand)) {
                    const pairKey = this.getPairKey(hand);
                    if (pairKey && this.strategyTable.pairs.player_hands[pairKey]) {
                        const action = this.strategyTable.pairs.player_hands[pairKey][dealerIndex];
                        if (action === 'R') {
                            autoStrategyLogger.info('对子投降策略', { pairKey, dealerCard: dealerCard.toString() });
                            return 'R';
                        }
                    }
                }

                // 检查硬牌是否应该投降
                if (!pointsInfo.isSoft) {
                    const hardKey = this.getHardHandKey(pointsInfo.value);
                    if (this.strategyTable.hard_totals.player_hands[hardKey]) {
                        const action = this.strategyTable.hard_totals.player_hands[hardKey][dealerIndex];
                        if (action === 'R') {
                            autoStrategyLogger.info('硬牌投降策略', { hardKey, dealerCard: dealerCard.toString() });
                            return 'R';
                        }
                    }
                }

                // 软牌通常不会投降，但为了完整性也检查一下
                if (pointsInfo.isSoft) {
                    const softKey = this.getSoftHandKey(hand, pointsInfo);
                    if (softKey && this.strategyTable.soft_totals.player_hands[softKey]) {
                        const action = this.strategyTable.soft_totals.player_hands[softKey][dealerIndex];
                        if (action === 'R') {
                            autoStrategyLogger.info('软牌投降策略', { softKey, dealerCard: dealerCard.toString() });
                            return 'R';
                        }
                    }
                }
            }

            // 2. 检查是否可以分牌（只有两张相同点数的牌可以分牌）
            if (this.isPair(hand) && canSplit) {
                const pairKey = this.getPairKey(hand);
                if (pairKey && this.strategyTable.pairs.player_hands[pairKey]) {
                    const action = this.strategyTable.pairs.player_hands[pairKey][dealerIndex];
                    if (action === 'P') {
                        autoStrategyLogger.info('分牌策略', { pairKey, dealerCard: dealerCard.toString() });
                        return 'P';
                    }
                }
            }

            // 3. 检查是否可以加倍（只有初始两张牌可以加倍）
            if (hand.length === 2) {
                // 检查软牌是否应该加倍
                if (pointsInfo.isSoft) {
                    const softKey = this.getSoftHandKey(hand, pointsInfo);
                    if (softKey && this.strategyTable.soft_totals.player_hands[softKey]) {
                        const action = this.strategyTable.soft_totals.player_hands[softKey][dealerIndex];
                        if (action === 'D') {
                            autoStrategyLogger.info('软牌加倍策略', { softKey, dealerCard: dealerCard.toString() });
                            return 'D';
                        }
                    }
                }
                // 检查硬牌是否应该加倍
                else {
                    const hardKey = this.getHardHandKey(pointsInfo.value);
                    if (this.strategyTable.hard_totals.player_hands[hardKey]) {
                        const action = this.strategyTable.hard_totals.player_hands[hardKey][dealerIndex];
                        if (action === 'D') {
                            autoStrategyLogger.info('硬牌加倍策略', { hardKey, dealerCard: dealerCard.toString() });
                            return 'D';
                        }
                    }
                }
            }

            // 4. 决定要牌或停牌
            // 软牌策略
            if (pointsInfo.isSoft) {
                const softKey = this.getSoftHandKey(hand, pointsInfo);
                if (softKey && this.strategyTable.soft_totals.player_hands[softKey]) {
                    const action = this.strategyTable.soft_totals.player_hands[softKey][dealerIndex];

                    // 记录详细日志，帮助排查问题
                    autoStrategyLogger.debug('软牌策略查询', {
                        softKey: softKey,
                        dealerIndex: dealerIndex,
                        action: action,
                        pointsValue: pointsInfo.value,
                        dealerCard: dealerCard.toString()
                    });

                    // 如果策略是加倍但已经不能加倍了，则改为要牌
                    if (action === 'D' && hand.length > 2) {
                        autoStrategyLogger.info('软牌策略（不能加倍，改为要牌）', { softKey, dealerCard: dealerCard.toString() });
                        return 'H';
                    } else if (action === 'H' || action === 'S') {
                        autoStrategyLogger.info('软牌策略', { softKey, dealerCard: dealerCard.toString(), action });
                        return action;
                    }
                }
            }

            // 硬牌策略
            const hardKey = this.getHardHandKey(pointsInfo.value);
            if (this.strategyTable.hard_totals.player_hands[hardKey]) {
                const action = this.strategyTable.hard_totals.player_hands[hardKey][dealerIndex];
                // 如果策略是加倍但已经不能加倍了，则改为要牌
                if (action === 'D' && hand.length > 2) {
                    autoStrategyLogger.info('硬牌策略（不能加倍，改为要牌）', { hardKey, dealerCard: dealerCard.toString() });
                    return 'H';
                } else if (action === 'H' || action === 'S') {
                    autoStrategyLogger.info('硬牌策略', { hardKey, dealerCard: dealerCard.toString(), action });
                    return action;
                }
            }

            // 默认策略：严格按照策略表执行
            // 如果已经查询过策略表但没有找到匹配的策略，使用更保守的默认策略
            if (pointsInfo.value >= 17) {
                return 'S'; // 17点或以上一律停牌
            } else if (pointsInfo.value <= 8) {
                return 'H'; // 8点或以下一律要牌
            } else {
                // 9-16点，再次查询策略表
                const hardKey = this.getHardHandKey(pointsInfo.value);
                if (this.strategyTable.hard_totals.player_hands[hardKey]) {
                    const action = this.strategyTable.hard_totals.player_hands[hardKey][dealerIndex];
                    // 如果策略表中有明确的策略，按策略表执行
                    if (action === 'H' || action === 'S') {
                        return action;
                    }
                }

                // 如果仍然没有找到匹配的策略，使用传统的默认策略
                // 12-16点，庄家2-6停牌，7-A要牌
                if (pointsInfo.value >= 12 && pointsInfo.value <= 16) {
                    return dealerIndex <= 4 ? 'S' : 'H';
                } else {
                    return 'H'; // 其他情况要牌
                }
            }
        } catch (error) {
            autoStrategyLogger.error('获取策略时出错', error);
            // 出错时默认停牌
            return 'S';
        }
    }
}

// 创建全局自动策略实例
window.autoStrategy = new AutoStrategy();

// 显式地将AutoStrategy类添加到window对象中
window.AutoStrategy = AutoStrategy;

/**
 * 全局自动策略函数
 * 用于获取当前手牌的最佳操作建议
 * @param {Array} hand - 玩家手牌
 * @param {Card} dealerCard - 庄家明牌
 * @param {Game} game - 游戏实例
 * @param {boolean} [excludeSplit=false] - 是否排除分牌选项
 * @returns {string} 策略建议（H, S, D, P, R）
 */
window.getAutoAction = function(hand, dealerCard, game, excludeSplit = false) {
    try {
        if (!hand || !dealerCard || !game) {
            autoStrategyLogger.error('getAutoAction: 参数无效', { hand, dealerCard });
            return 'S';
        }

        // 获取当前玩家和手牌信息
        const currentPlayer = game.getCurrentPlayer();
        const playerHandsCount = currentPlayer ? currentPlayer.hands.length : 0;
        const handValue = game.calculateHandValue(hand);

        // 记录详细的手牌信息
        console.log(`[getAutoAction] 手牌详情: ${hand.map(c => c.rank + c.suit).join(', ')}, 点数: ${handValue.value}, 软牌: ${handValue.isSoft}, 长度: ${hand.length}`);
        console.log(`[getAutoAction] 庄家明牌: ${dealerCard.rank}${dealerCard.suit}, 玩家手牌数: ${playerHandsCount}, 排除分牌: ${excludeSplit}`);

        // 检查是否可以分牌
        const isPair = hand.length === 2 && hand[0].getValue() === hand[1].getValue();
        const canSplit = !excludeSplit && isPair && playerHandsCount < 4;

        if (isPair) {
            console.log(`[getAutoAction] 检测到对子: ${hand[0].rank}${hand[0].suit} 和 ${hand[1].rank}${hand[1].suit}, 可分牌: ${canSplit}`);
            if (!canSplit && !excludeSplit) {
                console.log(`[getAutoAction] 无法分牌原因: ${playerHandsCount >= 4 ? '已达最大手牌数(4)' : '未知原因'}`);
            }
        }

        // 获取策略建议
        let action = window.autoStrategy.getAction(hand, dealerCard, canSplit, game.gameState, game);
        console.log(`[getAutoAction] 初始策略建议: ${action}`);

        // 额外检查：如果手牌长度大于2，但策略建议是加倍(D)，则改为要牌(H)
        if (action === 'D' && hand.length > 2) {
            console.log(`[getAutoAction] 策略建议加倍，但手牌长度不是2(${hand.length})，改为要牌`);
            action = 'H';
        }

        // 额外检查：如果手牌长度大于2，但策略建议是分牌(P)，则根据点数决定要牌或停牌
        if (action === 'P' && hand.length > 2) {
            console.log(`[getAutoAction] 策略建议分牌，但手牌长度不是2(${hand.length})，改为根据点数决定`);
            const points = game.calculateHandValue(hand);
            action = points.value >= 17 ? 'S' : 'H';
            console.log(`[getAutoAction] 根据点数(${points.value})决定新策略: ${action}`);
        }

        // 检查是否是分牌后的手牌
        if (hand.split) {
            console.log(`[getAutoAction] 检测到分牌后的手牌，最终策略: ${action}`);

            // 特殊处理：如果是软18(A+7)面对庄家10，应该要牌而不是停牌
            if (handValue.value === 18 && handValue.isSoft &&
                (dealerCard.rank === '10' || dealerCard.rank === 'J' || dealerCard.rank === 'Q' || dealerCard.rank === 'K')) {
                if (action === 'S') {
                    console.log(`[getAutoAction] 特殊情况：分牌后的软18面对庄家10，策略从停牌改为要牌`);
                    action = 'H';
                }
            }
        }

        console.log(`[getAutoAction] 最终策略建议: ${action}`);
        return action;
    } catch (error) {
        console.error('[getAutoAction] 出错:', error);
        autoStrategyLogger.error('getAutoAction出错:', error);
        return 'S'; // 出错时默认停牌
    }
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AutoStrategy,
        getAutoAction: window.getAutoAction
    };
}
