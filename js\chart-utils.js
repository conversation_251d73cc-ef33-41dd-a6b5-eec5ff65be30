/**
 * 图表工具类
 * 用于处理和绘制模拟系统中的各种图表
 */
class ChartUtils {
    /**
     * 创建筹码曲线图表
     * @param {HTMLElement} container - 图表容器元素
     * @param {Array} chipsCurve - 筹码曲线数据
     * @param {Object} options - 图表配置选项
     */
    static createChipsChart(container, chipsCurve, options = {}) {
        if (!chipsCurve || chipsCurve.length === 0) {
            console.warn('没有可用的筹码曲线数据');
            return null;
        }

        // 清空容器
        container.innerHTML = '';

        // 创建canvas元素
        const canvas = document.createElement('canvas');
        canvas.id = 'chipsChart';
        container.appendChild(canvas);

        // 准备数据
        const labels = chipsCurve.map(point => `局 ${point.game}`);
        
        // 如果是多玩家，为每个玩家创建一个数据集
        const datasets = [];
        
        // 检查是否有多个玩家
        const isMultiPlayer = chipsCurve[0].chips && Array.isArray(chipsCurve[0].chips) && chipsCurve[0].chips.length > 1;
        
        if (isMultiPlayer) {
            // 多玩家模式
            const playerCount = chipsCurve[0].chips.length;
            const colors = this._generateColors(playerCount);
            
            for (let i = 0; i < playerCount; i++) {
                datasets.push({
                    label: `玩家 ${i + 1}`,
                    data: chipsCurve.map(point => point.chips[i]),
                    borderColor: colors[i],
                    backgroundColor: this._adjustAlpha(colors[i], 0.1),
                    borderWidth: 2,
                    tension: 0.4,
                    fill: options.fill || false
                });
            }
            
            // 添加总筹码曲线
            if (options.showTotal !== false) {
                datasets.push({
                    label: '总筹码',
                    data: chipsCurve.map(point => {
                        if (point.totalChips) return point.totalChips;
                        return point.chips.reduce((sum, chips) => sum + chips, 0);
                    }),
                    borderColor: '#38bdf8',
                    backgroundColor: 'rgba(56, 189, 248, 0.1)',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: false,
                    borderDash: [5, 5]
                });
            }
        } else {
            // 单玩家模式
            datasets.push({
                label: '筹码',
                data: chipsCurve.map(point => {
                    if (Array.isArray(point.chips)) return point.chips[0];
                    return point.totalChips || point.chips;
                }),
                borderColor: '#38bdf8',
                backgroundColor: 'rgba(56, 189, 248, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: options.fill !== false
            });
        }

        // 创建图表
        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '筹码变化曲线',
                        color: '#e6e6e6',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#e6e6e6'
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleColor: '#e6e6e6',
                        bodyColor: '#e6e6e6',
                        borderColor: '#38bdf8',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa',
                            maxRotation: 0,
                            autoSkip: true,
                            maxTicksLimit: 10
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa'
                        }
                    }
                }
            }
        });

        return chart;
    }

    /**
     * 生成指定数量的颜色
     * @param {number} count - 颜色数量
     * @returns {Array} 颜色数组
     * @private
     */
    static _generateColors(count) {
        const colors = [
            '#38bdf8', // 蓝色
            '#34d399', // 绿色
            '#f87171', // 红色
            '#fbbf24', // 黄色
            '#a78bfa', // 紫色
            '#fb923c', // 橙色
            '#60a5fa', // 浅蓝
            '#4ade80', // 浅绿
            '#f472b6', // 粉色
            '#94a3b8'  // 灰色
        ];

        // 如果需要的颜色数量超过预设，则生成随机颜色
        if (count > colors.length) {
            for (let i = colors.length; i < count; i++) {
                colors.push(this._getRandomColor());
            }
        }

        return colors.slice(0, count);
    }

    /**
     * 生成随机颜色
     * @returns {string} 随机颜色
     * @private
     */
    static _getRandomColor() {
        const letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
    }

    /**
     * 调整颜色的透明度
     * @param {string} color - 颜色
     * @param {number} alpha - 透明度
     * @returns {string} 调整后的颜色
     * @private
     */
    static _adjustAlpha(color, alpha) {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        } else if (color.startsWith('rgb')) {
            return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`);
        }
        return color;
    }
}

// 导出工具类
window.ChartUtils = ChartUtils;
