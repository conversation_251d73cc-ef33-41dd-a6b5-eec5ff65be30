/**
 * 21点自动策略验证器
 * 用于验证自动策略的正确性，确保策略建议符合基本策略表
 */

// 创建日志记录器
const strategyValidatorLogger = window.Logger ? window.Logger.getLogger('StrategyValidator') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

/**
 * 自动策略验证器类
 */
class AutoStrategyValidator {
    constructor() {
        // 引用全局策略表
        this.strategyTable = window.autoStrategy ? window.autoStrategy.strategyTable : null;

        // 初始化验证结果
        this.validationResults = {
            total: 0,
            passed: 0,
            failed: 0,
            errors: []
        };

        strategyValidatorLogger.info('自动策略验证器已初始化');
    }

    /**
     * 验证策略表是否已加载
     * @returns {boolean} 策略表是否已加载
     */
    isStrategyTableLoaded() {
        if (!this.strategyTable && window.autoStrategy && window.autoStrategy.strategyTable) {
            this.strategyTable = window.autoStrategy.strategyTable;
        }

        return !!this.strategyTable;
    }

    /**
     * 创建测试手牌
     * @param {string} handType - 手牌类型 ('hard', 'soft', 'pair')
     * @param {string} handKey - 手牌键值
     * @returns {Array} 测试手牌数组
     */
    createTestHand(handType, handKey) {
        // 创建Card类的模拟实例
        const createMockCard = (rank, suit = 'spades') => {
            return {
                rank: rank,
                suit: suit,
                getValue: function() {
                    if (this.rank === 'A') return 11;
                    if (['J', 'Q', 'K'].includes(this.rank)) return 10;
                    return parseInt(this.rank);
                },
                toString: function() {
                    return `${this.rank}${this.suit[0]}`;
                }
            };
        };

        // 根据不同类型创建手牌
        if (handType === 'pair') {
            // 对子手牌
            const parts = handKey.split('-');
            const rank = parts[0];

            // 特殊处理10点牌
            if (rank === '10') {
                // 随机选择一种10点牌
                const tenPointRanks = ['10', 'J', 'Q', 'K'];
                const selectedRank = tenPointRanks[Math.floor(Math.random() * tenPointRanks.length)];
                return [createMockCard(selectedRank), createMockCard(selectedRank)];
            }

            return [createMockCard(rank), createMockCard(rank)];
        } else if (handType === 'soft') {
            // 软牌手牌
            const parts = handKey.split('-');
            const secondValue = parseInt(parts[1]);

            // A + 其他牌
            const hand = [createMockCard('A')];

            // 添加其他牌以达到所需点数
            if (secondValue <= 9) {
                hand.push(createMockCard(secondValue.toString()));
            } else {
                // 如果需要多张牌来达到点数
                let remainingValue = secondValue;
                while (remainingValue > 0) {
                    const cardValue = Math.min(remainingValue, 9);
                    hand.push(createMockCard(cardValue.toString()));
                    remainingValue -= cardValue;
                }
            }

            return hand;
        } else {
            // 硬牌手牌
            let targetValue;

            if (handKey === '5-7') {
                // 随机选择5-7之间的一个值
                targetValue = Math.floor(Math.random() * 3) + 5;
            } else if (handKey === '17-21') {
                // 随机选择17-21之间的一个值
                targetValue = Math.floor(Math.random() * 5) + 17;
            } else {
                targetValue = parseInt(handKey);
            }

            // 创建达到目标点数的手牌
            const hand = [];
            let currentSum = 0;

            // 首先添加一张2-9之间的牌
            const firstCardValue = Math.min(9, Math.max(2, Math.floor(Math.random() * 8) + 2));
            hand.push(createMockCard(firstCardValue.toString()));
            currentSum += firstCardValue;

            // 添加更多的牌直到达到目标点数
            while (currentSum < targetValue) {
                const remainingValue = targetValue - currentSum;
                const cardValue = Math.min(remainingValue, 9);
                hand.push(createMockCard(cardValue.toString()));
                currentSum += cardValue;
            }

            return hand;
        }
    }

    /**
     * 验证单个策略
     * @param {string} handType - 手牌类型 ('hard', 'soft', 'pair')
     * @param {string} handKey - 手牌键值
     * @param {string} dealerRank - 庄家明牌点数
     * @param {string} expectedAction - 期望的操作
     * @returns {boolean} 验证是否通过
     */
    validateSingleStrategy(handType, handKey, dealerRank, expectedAction) {
        try {
            // 创建测试手牌
            const hand = this.createTestHand(handType, handKey);

            // 创建庄家明牌
            const dealerCard = {
                rank: dealerRank,
                suit: 'spades',
                getValue: function() {
                    if (this.rank === 'A') return 11;
                    if (['J', 'Q', 'K'].includes(this.rank)) return 10;
                    return parseInt(this.rank);
                },
                toString: function() {
                    return `${this.rank}${this.suit[0]}`;
                }
            };

            // 创建游戏对象模拟
            const mockGame = {
                calculateHandValue: function(cards) {
                    let sum = 0;
                    let aces = 0;
                    let isSoft = false;

                    // 计算非A牌的点数和A的数量
                    for (const card of cards) {
                        if (card.rank === 'A') {
                            aces++;
                        } else {
                            sum += card.getValue();
                        }
                    }

                    // 初始时所有A都算作1点
                    sum += aces;

                    // 尝试将尽可能多的A升级为11点
                    for (let i = 0; i < aces; i++) {
                        if (sum + 10 <= 21) {
                            sum += 10;
                            isSoft = true;
                        } else {
                            break;
                        }
                    }

                    return { value: sum, isSoft: isSoft };
                },
                getCurrentPlayer: function() {
                    return {
                        hands: [hand]
                    };
                },
                gameState: 'playing'
            };

            // 获取实际策略建议
            const actualAction = window.getAutoAction(hand, dealerCard, mockGame);

            // 验证结果
            const isPassed = actualAction === expectedAction;

            // 更新验证结果
            this.validationResults.total++;
            if (isPassed) {
                this.validationResults.passed++;
            } else {
                this.validationResults.failed++;
                this.validationResults.errors.push({
                    handType,
                    handKey,
                    dealerRank,
                    expectedAction,
                    actualAction,
                    hand: hand.map(c => c.toString()).join(',')
                });
            }

            return isPassed;
        } catch (error) {
            strategyValidatorLogger.error('验证策略时出错', error);
            this.validationResults.total++;
            this.validationResults.failed++;
            this.validationResults.errors.push({
                handType,
                handKey,
                dealerRank,
                expectedAction,
                error: error.message
            });
            return false;
        }
    }

    /**
     * 验证所有策略
     * @returns {Object} 验证结果
     */
    validateAllStrategies() {
        // 重置验证结果
        this.validationResults = {
            total: 0,
            passed: 0,
            failed: 0,
            errors: []
        };

        // 检查策略表是否已加载
        if (!this.isStrategyTableLoaded()) {
            strategyValidatorLogger.error('策略表未加载，无法验证');
            return this.validationResults;
        }

        // 获取庄家牌数组
        const dealerCards = this.strategyTable.dealer_cards;

        // 验证硬牌策略
        for (const handKey in this.strategyTable.hard_totals.player_hands) {
            const actions = this.strategyTable.hard_totals.player_hands[handKey];
            for (let i = 0; i < dealerCards.length; i++) {
                this.validateSingleStrategy('hard', handKey, dealerCards[i], actions[i]);
            }
        }

        // 验证软牌策略
        for (const handKey in this.strategyTable.soft_totals.player_hands) {
            const actions = this.strategyTable.soft_totals.player_hands[handKey];
            for (let i = 0; i < dealerCards.length; i++) {
                this.validateSingleStrategy('soft', handKey, dealerCards[i], actions[i]);
            }
        }

        // 验证对子策略
        for (const handKey in this.strategyTable.pairs.player_hands) {
            const actions = this.strategyTable.pairs.player_hands[handKey];
            for (let i = 0; i < dealerCards.length; i++) {
                this.validateSingleStrategy('pair', handKey, dealerCards[i], actions[i]);
            }
        }

        // 输出验证结果
        strategyValidatorLogger.info('策略验证完成', {
            total: this.validationResults.total,
            passed: this.validationResults.passed,
            failed: this.validationResults.failed,
            passRate: `${(this.validationResults.passed / this.validationResults.total * 100).toFixed(2)}%`
        });

        if (this.validationResults.failed > 0) {
            strategyValidatorLogger.warn('策略验证失败', this.validationResults.errors);
        }

        return this.validationResults;
    }

    /**
     * 获取验证结果
     * @returns {Object} 验证结果
     */
    getValidationResults() {
        return this.validationResults;
    }
}

// 创建全局自动策略验证器实例
window.autoStrategyValidator = new AutoStrategyValidator();

// 禁用自动验证策略，避免产生大量日志
// 如果需要验证策略，可以手动调用 window.autoStrategyValidator.validateAllStrategies()
/*
document.addEventListener('DOMContentLoaded', () => {
    // 延迟执行验证，确保自动策略已加载
    setTimeout(() => {
        if (window.autoStrategyValidator && window.autoStrategy && window.autoStrategy.strategyTable) {
            // 确保策略表已加载
            window.autoStrategyValidator.strategyTable = window.autoStrategy.strategyTable;
            window.autoStrategyValidator.validateAllStrategies();
        } else {
            strategyValidatorLogger.warn('策略表尚未加载，将在5秒后重试验证');
            // 如果策略表尚未加载，再次延迟尝试
            setTimeout(() => {
                if (window.autoStrategyValidator && window.autoStrategy && window.autoStrategy.strategyTable) {
                    window.autoStrategyValidator.strategyTable = window.autoStrategy.strategyTable;
                    window.autoStrategyValidator.validateAllStrategies();
                } else {
                    strategyValidatorLogger.error('策略表加载失败，无法验证');
                }
            }, 5000);
        }
    }, 2000);
});
*/

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AutoStrategyValidator
    };
}
