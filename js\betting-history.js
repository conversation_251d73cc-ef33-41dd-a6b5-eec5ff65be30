/**
 * 下注历史记录管理类
 * 用于记录和显示玩家的下注历史
 */
class BettingHistory {
    /**
     * 初始化下注历史记录
     */
    constructor() {
        // 初始化历史记录数组
        this.history = [];

        // 初始化玩家历史记录
        this.playerHistory = {};

        // 初始化游戏局数计数器
        this.gameCount = 0;

        // 当前页码
        this.currentPage = 1;

        // 每页显示的记录数
        this.itemsPerPage = 50;

        // 创建历史记录UI
        this.createHistoryUI();

        // 从本地存储加载历史记录
        this.loadHistory();

        // 绑定HTML中已有的按钮事件
        this.bindButtonEvents();
    }

    /**
     * 绑定HTML中已有的按钮事件
     */
    bindButtonEvents() {
        const historyButton = document.getElementById('betting-history-btn');
        if (historyButton) {
            historyButton.addEventListener('click', () => {
                this.showHistory();
            });
        }
    }

    /**
     * 创建历史记录UI
     */
    createHistoryUI() {
        // 创建历史记录面板
        const historyPanel = document.createElement('div');
        historyPanel.id = 'betting-history-panel';
        historyPanel.className = 'history-panel';
        historyPanel.style.display = 'none';
        historyPanel.style.width = '95%'; // 扩大面板宽度
        historyPanel.style.maxWidth = '1400px'; // 设置最大宽度
        historyPanel.style.overflowY = 'hidden'; // 防止外层出现垂直滚动条

        // 添加样式到document，确保表格和筛选器样式正确
        if (!document.getElementById('betting-history-styles')) {
            const styleEl = document.createElement('style');
            styleEl.id = 'betting-history-styles';
            styleEl.textContent = `
                .history-panel {
                    background-color: #1a202c;
                    color: #e2e8f0;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1000;
                    overflow-y: auto;
                    max-height: 90vh;
                }

                .filter-container {
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }

                .filter-item {
                    margin-right: 10px;
                }

                .history-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }

                .history-table th, .history-table td {
                    padding: 8px 12px;
                    text-align: center;
                    border: 1px solid #2d3748;
                }

                .history-table th {
                    background-color: #2d3748;
                }

                .history-table tr:nth-child(even) {
                    background-color: #1e2533;
                }

                .total-stats-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }

                .total-stats-table th, .total-stats-table td {
                    padding: 8px 12px;
                    text-align: center;
                    border: 1px solid #2d3748;
                }

                .total-stats-table th {
                    background-color: #2d3748;
                }

                .profit-positive {
                    color: #48bb78;
                }

                .profit-negative {
                    color: #f56565;
                }

                .win-count {
                    color: #48bb78;
                }

                .lose-count {
                    color: #f56565;
                }

                .blackjack-count {
                    color: #f6e05e;
                }

                .hearts, .diamonds {
                    color: #ff5555;
                }

                .clubs, .spades {
                    color: #ffffff;
                }

                .pagination-button {
                    margin: 0 5px;
                    padding: 5px 10px;
                    background-color: #2d3748;
                    border: none;
                    border-radius: 4px;
                    color: #e2e8f0;
                    cursor: pointer;
                }

                .pagination-button:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                .pagination-info {
                    margin: 0 10px;
                }

                .clear-history-button {
                    background-color: #e53e3e;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-top: 10px;
                }

                .reset-filter-btn {
                    background-color: #4299e1;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                }

                select {
                    padding: 6px 10px;
                    border-radius: 4px;
                    background-color: #2d3748;
                    color: #e2e8f0;
                    border: 1px solid #4a5568;
                }
            `;
            document.head.appendChild(styleEl);
        }

        // 创建面板标题
        const panelHeader = document.createElement('div');
        panelHeader.className = 'panel-header';

        const title = document.createElement('h3');
        title.textContent = '下注历史记录';
        panelHeader.appendChild(title);

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-button';
        closeButton.textContent = '×';
        closeButton.addEventListener('click', () => {
            historyPanel.style.display = 'none';
        });
        panelHeader.appendChild(closeButton);

        historyPanel.appendChild(panelHeader);

        // 创建历史记录内容区域
        const historyContent = document.createElement('div');
        historyContent.className = 'history-content';
        historyContent.style.overflowY = 'auto'; // 确保内容区域可以滚动
        historyContent.style.maxHeight = 'calc(90vh - 60px)'; // 设置最大高度

        // 创建筛选器容器
        const filterContainer = document.createElement('div');
        filterContainer.className = 'filter-container';
        // 样式已在CSS中定义

        // 创建玩家筛选器
        const playerFilter = document.createElement('div');
        playerFilter.className = 'filter-item player-filter';
        playerFilter.style.flexBasis = '20%';

        const filterLabel = document.createElement('label');
        filterLabel.textContent = '选择玩家: ';
        filterLabel.htmlFor = 'player-filter-select';
        playerFilter.appendChild(filterLabel);

        const playerSelect = document.createElement('select');
        playerSelect.id = 'player-filter-select';

        // 添加"全部玩家"选项
        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = '全部玩家';
        playerSelect.appendChild(allOption);

        playerSelect.addEventListener('change', () => {
            this.currentPage = 1; // 重置为第1页
            this.filterHistory();
        });

        playerFilter.appendChild(playerSelect);
        filterContainer.appendChild(playerFilter);

        // 创建下注金额筛选器
        const betFilter = document.createElement('div');
        betFilter.className = 'filter-item bet-filter';
        betFilter.style.flexBasis = '20%';

        const betLabel = document.createElement('label');
        betLabel.textContent = '下注金额: ';
        betLabel.htmlFor = 'bet-filter-select';
        betFilter.appendChild(betLabel);

        const betAmountSelect = document.createElement('select');
        betAmountSelect.id = 'history-bet-filter-select';

        // 添加"全部金额"选项
        const allBetOption = document.createElement('option');
        allBetOption.value = 'all';
        allBetOption.textContent = '全部金额';
        betAmountSelect.appendChild(allBetOption);

        betAmountSelect.addEventListener('change', () => {
            this.currentPage = 1; // 重置为第1页
            this.filterHistory();
        });

        betFilter.appendChild(betAmountSelect);
        filterContainer.appendChild(betFilter);

        // 创建结果筛选器
        const resultFilter = document.createElement('div');
        resultFilter.className = 'filter-item result-filter';
        resultFilter.style.flexBasis = '20%';

        const resultLabel = document.createElement('label');
        resultLabel.textContent = '结果: ';
        resultLabel.htmlFor = 'result-filter-select';
        resultFilter.appendChild(resultLabel);

        const resultSelect = document.createElement('select');
        resultSelect.id = 'result-filter-select';

        // 添加结果选项
        const resultOptions = [
            { value: 'all', text: '全部结果' },
            { value: '赢', text: '赢' },
            { value: '输', text: '输' },
            { value: '平', text: '平' },
            { value: '黑杰克', text: '黑杰克' },
            { value: '爆牌', text: '爆牌' },
            { value: '投降', text: '投降' }
        ];

        resultOptions.forEach(option => {
            const optElement = document.createElement('option');
            optElement.value = option.value;
            optElement.textContent = option.text;
            resultSelect.appendChild(optElement);
        });

        resultSelect.addEventListener('change', () => {
            this.currentPage = 1; // 重置为第1页
            this.filterHistory();
        });

        resultFilter.appendChild(resultSelect);
        filterContainer.appendChild(resultFilter);

        // 创建操作类型筛选器
        const actionFilter = document.createElement('div');
        actionFilter.className = 'filter-item action-filter';
        actionFilter.style.flexBasis = '20%';

        const actionLabel = document.createElement('label');
        actionLabel.textContent = '操作类型: ';
        actionLabel.htmlFor = 'action-filter-select';
        actionFilter.appendChild(actionLabel);

        const actionSelect = document.createElement('select');
        actionSelect.id = 'action-filter-select';

        // 添加操作类型选项
        const actionOptions = [
            { value: 'all', text: '全部操作' },
            { value: 'double', text: '加倍' },
            { value: 'split', text: '分牌' },
            { value: 'normal', text: '普通' }
        ];

        actionOptions.forEach(option => {
            const optElement = document.createElement('option');
            optElement.value = option.value;
            optElement.textContent = option.text;
            actionSelect.appendChild(optElement);
        });

        actionSelect.addEventListener('change', () => {
            this.currentPage = 1; // 重置为第1页
            this.filterHistory();
        });

        actionFilter.appendChild(actionSelect);
        filterContainer.appendChild(actionFilter);

        // 创建重置筛选按钮
        const resetFilter = document.createElement('button');
        resetFilter.className = 'reset-filter-btn';
        resetFilter.textContent = '重置筛选';
        resetFilter.style.flexBasis = '15%';
        resetFilter.addEventListener('click', () => {
            playerSelect.value = 'all';
            betAmountSelect.value = 'all';
            resultSelect.value = 'all';
            actionSelect.value = 'all';
            this.currentPage = 1; // 重置为第1页
            this.filterHistory();
        });
        filterContainer.appendChild(resetFilter);

        historyContent.appendChild(filterContainer);

        // 创建玩家总数据区域
        const playerTotalStatsDiv = document.createElement('div');
        playerTotalStatsDiv.className = 'player-total-stats';
        playerTotalStatsDiv.id = 'player-total-stats';
        playerTotalStatsDiv.style.marginBottom = '15px';
        historyContent.appendChild(playerTotalStatsDiv);

        // 创建历史记录表格
        const historyTable = document.createElement('table');
        historyTable.className = 'history-table';
        historyTable.id = 'betHistoryTable';

        // 创建表头
        const tableHeader = document.createElement('thead');
        const headerRow = document.createElement('tr');

        // 定义表头和列宽
        const headers = [
            { text: '局号', width: '5%' },
            { text: '玩家标识', width: '10%' },
            { text: '下注金额', width: '8%' },
            { text: '真数', width: '5%' },
            { text: '结果', width: '10%' },
            { text: '玩家手牌', width: '22%' },
            { text: '庄家手牌', width: '22%' },
            { text: '盈亏', width: '8%' },
            { text: '剩余筹码', width: '10%' }
        ];

        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header.text;
            th.style.width = header.width;
            headerRow.appendChild(th);
        });

        tableHeader.appendChild(headerRow);
        historyTable.appendChild(tableHeader);

        // 创建表格主体
        const tableBody = document.createElement('tbody');
        tableBody.id = 'history-table-body';
        historyTable.appendChild(tableBody);

        // 创建分页控件容器
        const paginationContainer = document.createElement('div');
        paginationContainer.id = 'history-pagination';
        paginationContainer.className = 'history-pagination';

        historyContent.appendChild(historyTable);
        historyContent.appendChild(paginationContainer);

        // 创建清除历史按钮容器
        const clearButtonContainer = document.createElement('div');
        clearButtonContainer.style.textAlign = 'center';
        clearButtonContainer.style.marginBottom = '20px';
        clearButtonContainer.style.paddingBottom = '20px';

        // 创建清除历史按钮
        const clearButton = document.createElement('button');
        clearButton.className = 'clear-history-button';
        clearButton.textContent = '清除历史记录';
        clearButton.style.width = '200px';
        clearButton.style.height = '40px';
        clearButton.style.fontSize = '16px';
        clearButton.addEventListener('click', () => {
            if (confirm('确定要清除所有历史记录吗？此操作不可撤销。')) {
                this.clearHistory();
            }
        });

        clearButtonContainer.appendChild(clearButton);
        historyContent.appendChild(clearButtonContainer);

        historyPanel.appendChild(historyContent);

        // 将历史记录面板添加到文档
        document.body.appendChild(historyPanel);

        // 不再创建历史统计信息区域，只使用玩家总数据

        // 保存UI元素引用
        this.historyPanel = historyPanel;
        this.historyTableBody = tableBody;
        this.playerSelect = playerSelect;
        this.historyBetSelect = betAmountSelect;
        this.resultSelect = resultSelect;
        this.actionSelect = actionSelect;
        this.paginationContainer = paginationContainer;
        this.playerTotalStatsDiv = playerTotalStatsDiv;
    }

    /**
     * 显示历史记录面板
     */
    showHistory() {
        // 确保所有必要的元素都已经被初始化
        if (!this.historyPanel) {
            console.error('历史记录面板未初始化');
            return;
        }

        // 更新玩家选择下拉框
        this.updatePlayerSelector();

        // 更新下注金额选择下拉框
        this.updateBetSelector();

        // 更新历史记录表格
        if (this.playerSelect && this.historyBetSelect && this.resultSelect && this.actionSelect) {
            this.filterHistory();
        } else {
            console.error('筛选器未初始化');
        }

        // 显示面板
        this.historyPanel.style.display = 'block';
    }

    /**
     * 更新玩家选择下拉框
     */
    updatePlayerSelector() {
        // 保存当前选中的值
        const currentValue = this.playerSelect.value;

        // 清空下拉框，保留"全部玩家"选项
        while (this.playerSelect.options.length > 1) {
            this.playerSelect.remove(1);
        }

        // 创建玩家索引到名称的映射
        const playerMap = new Map();

        // 从历史记录中获取所有玩家
        this.history.forEach(record => {
            playerMap.set(record.playerIndex, record.playerName);
        });

        // 添加玩家选项
        playerMap.forEach((name, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = name;
            this.playerSelect.appendChild(option);
        });

        // 尝试恢复之前选中的值
        if (currentValue && this.playerSelect.querySelector(`option[value="${currentValue}"]`)) {
            this.playerSelect.value = currentValue;
        } else {
            this.playerSelect.value = 'all';
        }
    }

    /**
     * 更新下注金额选择下拉框
     */
    updateBetSelector() {
        // 获取下注金额选择器
        if (!this.historyBetSelect) return;

        // 保存当前选中的值
        const currentValue = this.historyBetSelect.value;

        // 清空下拉框，保留"全部金额"选项
        while (this.historyBetSelect.options.length > 1) {
            this.historyBetSelect.remove(1);
        }

        // 创建下注金额集合
        const betSet = new Set();

        // 从历史记录中获取所有原始下注金额
        this.history.forEach(record => {
            // 使用originalBet字段（如果存在），否则使用bet字段
            betSet.add(record.originalBet !== undefined ? record.originalBet : record.bet);
        });

        // 将下注金额转换为数组并排序
        const betArray = Array.from(betSet).sort((a, b) => a - b);

        // 添加下注金额选项
        betArray.forEach(bet => {
            const option = document.createElement('option');
            option.value = bet;
            option.textContent = bet;
            this.historyBetSelect.appendChild(option);
        });

        // 尝试恢复之前选中的值
        if (currentValue && this.historyBetSelect.querySelector(`option[value="${currentValue}"]`)) {
            this.historyBetSelect.value = currentValue;
        } else {
            this.historyBetSelect.value = 'all';
        }
    }

    /**
     * 筛选历史记录
     */
    filterHistory() {
        if (!this.playerSelect || !this.historyBetSelect || !this.resultSelect || !this.actionSelect) return;

        const playerFilter = this.playerSelect.value;
        const betFilter = this.historyBetSelect.value;
        const resultFilter = this.resultSelect.value;
        const actionFilter = this.actionSelect.value;

        this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, this.currentPage);
    }

    /**
     * 更新历史记录表格
     * @param {string|number} playerFilter - 玩家筛选条件，'all'表示所有玩家，数字表示特定玩家索引
     * @param {string|number} betFilter - 下注金额筛选条件，'all'表示所有金额，数字表示特定下注金额
     * @param {string} resultFilter - 结果筛选条件，'all'表示所有结果
     * @param {string} actionFilter - 操作类型筛选条件，'all'表示所有操作类型
     * @param {number} page - 当前页码，从1开始
     */
    updateHistoryTable(playerFilter = 'all', betFilter = 'all', resultFilter = 'all', actionFilter = 'all', page = 1) {
        // 清空表格内容
        this.historyTableBody.innerHTML = '';

        // 筛选历史记录
        let filteredHistory = this.history;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(record => record.playerIndex === playerIndex);
        }

        // 按原始下注金额筛选
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            filteredHistory = filteredHistory.filter(record => {
                // 使用originalBet字段（如果存在），否则使用bet字段
                const originalBet = record.originalBet !== undefined ? record.originalBet : record.bet;
                return originalBet === betAmount;
            });
        }

        // 按结果筛选
        if (resultFilter !== 'all') {
            filteredHistory = filteredHistory.filter(record => record.result === resultFilter);
        }

        // 按操作类型筛选
        if (actionFilter !== 'all') {
            switch (actionFilter) {
                case 'double':
                    filteredHistory = filteredHistory.filter(record =>
                        record.actionType === '加倍' || record.isDoubleDown === true);
                    break;
                case 'split':
                    // 判断数据来源（模拟系统/游戏本体），适配不同分牌筛选逻辑
                    const isSimData = filteredHistory.some(r => r.hasOwnProperty('game'));
                    if (isSimData) {
                        // 模拟系统数据：只要该玩家该局有多手牌且isSplit为true，筛选所有isSplit为true的手牌
                        const splitGrouped = {};
                        filteredHistory.forEach(record => {
                            const key = `${record.game}-${record.playerIndex}`;
                            if (!splitGrouped[key]) splitGrouped[key] = [];
                            splitGrouped[key].push(record);
                        });
                        filteredHistory = Object.values(splitGrouped).flatMap(records => {
                            const hasSplit = records.length > 1 && records.some(r => r.isSplit === true);
                            if (!hasSplit) return [];
                            return records.filter(r => r.isSplit === true);
                        });
                    } else {
                        // 游戏本体数据：只要该玩家该局有多手牌且isSplit为true或actionType为'分牌'，筛选所有分牌手牌
                        const splitGrouped = {};
                        filteredHistory.forEach(record => {
                            const key = `${record.gameId}-${record.playerIndex}`;
                            if (!splitGrouped[key]) splitGrouped[key] = [];
                            splitGrouped[key].push(record);
                        });
                        filteredHistory = Object.values(splitGrouped).flatMap(records => {
                            const hasSplit = records.length > 1 && records.some(r => r.isSplit === true || r.actionType === '分牌');
                            if (!hasSplit) return [];
                            return records.filter(r => r.isSplit === true || r.actionType === '分牌');
                        });
                    }
                    break;
                case 'normal':
                    filteredHistory = filteredHistory.filter(record =>
                        (record.actionType === '普通' || !record.actionType) &&
                        !record.isDoubleDown &&
                        !record.isSplit &&
                        !(record.handIdentifier && record.handIdentifier.includes('手牌')));
                    break;
            }
        }

        // 分页设置
        const totalItems = filteredHistory.length;
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);

        // 确保页码在有效范围内
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));
        this.currentPage = currentPage;

        // 计算当前页的数据范围
        const startIndex = (currentPage - 1) * this.itemsPerPage;
        const endIndex = Math.min(startIndex + this.itemsPerPage, totalItems);

        // 获取当前页的数据，不再反转顺序
        const currentPageData = filteredHistory.slice(startIndex, endIndex);

        // 按游戏局号分组记录，用于处理分牌情况
        const gameGroups = {};
        currentPageData.forEach(record => {
            if (!gameGroups[record.gameId]) {
                gameGroups[record.gameId] = [];
            }
            gameGroups[record.gameId].push(record);
        });

        // 处理每一局游戏
        Object.keys(gameGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(gameId => {
            const records = gameGroups[gameId];

            // 检查是否为分牌情况
            const isSplitHand = records.length > 1 && records.some(record =>
                record.handIndex > 0 || record.isSplit || record.actionType === '分牌' ||
                (record.handIdentifier && record.handIdentifier.includes('手牌')));

            // 对于每一局游戏，按手牌索引排序
            records.sort((a, b) => {
                const aIndex = a.handIndex !== undefined ? a.handIndex : 0;
                const bIndex = b.handIndex !== undefined ? b.handIndex : 0;
                return aIndex - bIndex;
            });

            // 处理每一手牌
            records.forEach((record, recordIndex) => {
                const row = document.createElement('tr');

                // 如果是分牌情况，为行添加特殊样式
                if (isSplitHand) {
                    row.classList.add('split-hand-row');

                    // 第一手牌添加顶部边框
                    if (recordIndex === 0) {
                        row.classList.add('first-split-hand');
                    }

                    // 最后一手牌添加底部边框
                    if (recordIndex === records.length - 1) {
                        row.classList.add('last-split-hand');
                    }
                }

                // 局号
                const gameCell = document.createElement('td');
                gameCell.textContent = record.gameId;
                row.appendChild(gameCell);

                // 玩家标识
                const playerIdentCell = document.createElement('td');
                playerIdentCell.textContent = record.playerName;

                // 显示分牌标识
                // 首先检查是否有handIdentifier字段（从模拟系统同步过来的记录会有这个字段）
                if (record.handIdentifier) {
                    const handIndicator = document.createElement('span');
                    handIndicator.className = 'hand-indicator';
                    handIndicator.textContent = ` ${record.handIdentifier}`;
                    handIndicator.style.fontSize = '0.85em';
                    handIndicator.style.color = '#60a5fa';
                    handIndicator.style.fontWeight = 'bold';
                    playerIdentCell.appendChild(handIndicator);
                    console.log(`使用handIdentifier为玩家${record.playerName}添加标识: ${record.handIdentifier}`);
                }
                // 检查是否是分牌手牌（通过actionType或isSplit标志）
                else if ((record.actionType === '分牌' || record.isSplit) && record.handIndex !== undefined) {
                    const handIndicator = document.createElement('span');
                    handIndicator.className = 'hand-indicator';
                    handIndicator.textContent = ` 手牌${record.handIndex + 1}`;
                    handIndicator.style.fontSize = '0.85em';
                    handIndicator.style.color = '#60a5fa';
                    handIndicator.style.fontWeight = 'bold';
                    playerIdentCell.appendChild(handIndicator);
                    console.log(`为玩家${record.playerName}的分牌手牌${record.handIndex + 1}添加标识`);
                }
                // 如果playerCards对象有split属性，也添加分牌标识
                else if (record.playerCards && record.playerCards.split) {
                    const handIndicator = document.createElement('span');
                    handIndicator.className = 'hand-indicator';
                    handIndicator.textContent = ` 手牌${record.handIndex + 1}`;
                    handIndicator.style.fontSize = '0.85em';
                    handIndicator.style.color = '#60a5fa';
                    handIndicator.style.fontWeight = 'bold';
                    playerIdentCell.appendChild(handIndicator);
                    console.log(`通过playerCards.split属性为玩家${record.playerName}的手牌${record.handIndex + 1}添加标识`);
                }

                row.appendChild(playerIdentCell);

            // 下注金额
            const betCell = document.createElement('td');
            betCell.textContent = record.bet;
            row.appendChild(betCell);

            // 真数
            const trueCountCell = document.createElement('td');
            trueCountCell.textContent = record.trueCount || '0';
            row.appendChild(trueCountCell);

            // 结果
            const resultCell = document.createElement('td');
            let resultText = record.result;

            // 添加加倍标识到结果文本中
            // 首先检查是否有resultWithDouble字段（从模拟系统同步过来的记录会有这个字段）
            if (record.resultWithDouble) {
                resultText = record.resultWithDouble;
            }
            // 否则，如果是加倍操作，添加加倍标识
            else if (record.actionType === '加倍' || record.isDoubleDown) {
                resultText += '【加倍】';
            }

            resultCell.textContent = resultText;
            resultCell.className = this.getResultClass(record.result);
            row.appendChild(resultCell);

            // 玩家手牌
            const playerHandCell = document.createElement('td');
            if (record.playerCards && record.playerCards.length > 0) {
                playerHandCell.innerHTML = this.formatCardSequence(record.playerCards);
            } else {
                playerHandCell.textContent = '-';
            }
            row.appendChild(playerHandCell);

            // 庄家手牌
            const dealerHandCell = document.createElement('td');
            if (record.dealerCards && record.dealerCards.length > 0) {
                dealerHandCell.innerHTML = this.formatCardSequence(record.dealerCards);
            } else {
                dealerHandCell.textContent = '-';
            }
            row.appendChild(dealerHandCell);

            // 盈亏
            const profitCell = document.createElement('td');
            profitCell.textContent = record.profit > 0 ? `+${record.profit}` : record.profit;
            profitCell.className = record.profit > 0 ? 'profit-positive' : (record.profit < 0 ? 'profit-negative' : '');
            row.appendChild(profitCell);

            // 剩余筹码
            const balanceCell = document.createElement('td');

            // 尝试获取玩家的筹码数量，优先级：
            // 1. 记录中的balance字段
            // 2. 游戏中当前玩家的筹码
            // 3. 如果都获取不到，显示'-'
            let chipValue = null;

            // 1. 首先检查记录中的balance字段
            if (record.balance !== undefined) {
                chipValue = record.balance;
            }
            // 2. 如果记录中没有有效的balance，尝试从游戏中获取
            else if (window.game && window.game.players && record.playerIndex !== undefined) {
                const player = window.game.players[record.playerIndex];
                if (player && player.chips !== undefined) {
                    chipValue = player.chips;
                    console.log(`从游戏中获取玩家${record.playerName}的筹码: ${chipValue}`);
                }
            }

            // 设置单元格内容
            if (chipValue !== null) {
                balanceCell.textContent = chipValue.toLocaleString();
            } else {
                balanceCell.textContent = '-';
            }

            row.appendChild(balanceCell);

            this.historyTableBody.appendChild(row);
            });
        });

        // 更新分页控件
        this.updatePagination(playerFilter, betFilter, resultFilter, actionFilter, currentPage, totalPages, totalItems);

        // 确保playerTotalStatsDiv存在
        if (this.playerTotalStatsDiv) {
            // 更新玩家总数据
            this.updatePlayerTotalStats(playerFilter, betFilter, resultFilter, actionFilter);
        }
    }

    /**
     * 格式化牌序列，显示操作过程
     * @param {Array} cards - 牌数组
     * @returns {string} 格式化后的牌序列HTML
     */
    formatCardSequence(cards) {
        if (!cards || !Array.isArray(cards) || cards.length === 0) return '-';

        // 将牌转换为带花色的表示
        const formattedCards = cards.map(card => {
            if (typeof card === 'string') {
                // 如果已经是字符串格式，直接返回
                return card;
            } else if (card && card.rank && card.suit) {
                // 如果是对象格式，转换为HTML带样式的字符串
                const suitMap = {
                    'hearts': { symbol: '♥', class: 'hearts' },
                    'diamonds': { symbol: '♦', class: 'diamonds' },
                    'clubs': { symbol: '♣', class: 'clubs' },
                    'spades': { symbol: '♠', class: 'spades' },
                    'h': { symbol: '♥', class: 'hearts' },
                    'd': { symbol: '♦', class: 'diamonds' },
                    'c': { symbol: '♣', class: 'clubs' },
                    's': { symbol: '♠', class: 'spades' }
                };

                // 添加样式到document，确保花色颜色正确显示
                if (!document.getElementById('card-suit-styles')) {
                    const styleEl = document.createElement('style');
                    styleEl.id = 'card-suit-styles';
                    styleEl.textContent = `
                        .hearts, .diamonds { color: #ff5555; font-weight: bold; }
                        .clubs, .spades { color: #ffffff; font-weight: bold; } /* 在深色背景上使用白色加粗 */
                    `;
                    document.head.appendChild(styleEl);
                }

                const suitInfo = suitMap[card.suit.toLowerCase()] || { symbol: card.suit, class: '' };

                return `${card.rank}<span class="${suitInfo.class}">${suitInfo.symbol}</span>`;
            } else {
                return '?';
            }
        });

        // 如果只有一张或两张牌，直接显示
        if (formattedCards.length <= 2) {
            return formattedCards.join(' ');
        }

        // 显示操作过程：前两张牌 → 第三张牌 → 第四张牌...
        let result = `${formattedCards[0]} ${formattedCards[1]}`;
        for (let i = 2; i < formattedCards.length; i++) {
            result += ` → ${formattedCards[i]}`;
        }

        return result;
    }

    /**
     * 更新分页控件
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     * @param {number} currentPage - 当前页码
     * @param {number} totalPages - 总页数
     * @param {number} totalItems - 总记录数
     */
    updatePagination(playerFilter, betFilter, resultFilter, actionFilter, currentPage, totalPages, totalItems) {
        // 清空分页容器
        this.paginationContainer.innerHTML = '';

        // 如果没有数据或只有一页，不显示分页控件
        if (totalItems <= 0 || totalPages <= 1) {
            this.paginationContainer.style.display = 'none';
            return;
        }

        this.paginationContainer.style.display = 'flex';

        // 创建首页按钮
        const firstPageButton = document.createElement('button');
        firstPageButton.className = 'pagination-button';
        firstPageButton.textContent = '首页';
        firstPageButton.disabled = currentPage === 1;
        firstPageButton.addEventListener('click', () => {
            this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, 1);
        });
        this.paginationContainer.appendChild(firstPageButton);

        // 创建上一页按钮
        const prevPageButton = document.createElement('button');
        prevPageButton.className = 'pagination-button';
        prevPageButton.textContent = '上一页';
        prevPageButton.disabled = currentPage === 1;
        prevPageButton.addEventListener('click', () => {
            this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, currentPage - 1);
        });
        this.paginationContainer.appendChild(prevPageButton);

        // 创建页码信息
        const pageInfo = document.createElement('span');
        pageInfo.className = 'pagination-info';
        pageInfo.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
        this.paginationContainer.appendChild(pageInfo);

        // 创建下一页按钮
        const nextPageButton = document.createElement('button');
        nextPageButton.className = 'pagination-button';
        nextPageButton.textContent = '下一页';
        nextPageButton.disabled = currentPage === totalPages;
        nextPageButton.addEventListener('click', () => {
            this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, currentPage + 1);
        });
        this.paginationContainer.appendChild(nextPageButton);

        // 创建尾页按钮
        const lastPageButton = document.createElement('button');
        lastPageButton.className = 'pagination-button';
        lastPageButton.textContent = '尾页';
        lastPageButton.disabled = currentPage === totalPages;
        lastPageButton.addEventListener('click', () => {
            this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, totalPages);
        });
        this.paginationContainer.appendChild(lastPageButton);
    }

    // updateHistoryStats方法已被移除，因为我们不再使用它

    /**
     * 更新玩家总数据统计
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     */
    updatePlayerTotalStats(playerFilter = 'all', betFilter = 'all', resultFilter = 'all', actionFilter = 'all') {
        // 确保playerTotalStatsDiv存在
        if (!this.playerTotalStatsDiv) return;

        // 清空玩家总数据区域
        this.playerTotalStatsDiv.innerHTML = '';

        // 筛选历史记录
        let filteredHistory = this.history;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(record => record.playerIndex === playerIndex);
        }

        // 按原始下注金额筛选
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            filteredHistory = filteredHistory.filter(record => {
                // 使用originalBet字段（如果存在），否则使用bet字段
                const originalBet = record.originalBet !== undefined ? record.originalBet : record.bet;
                return originalBet === betAmount;
            });
        }

        // 按结果筛选
        if (resultFilter !== 'all') {
            filteredHistory = filteredHistory.filter(record => record.result === resultFilter);
        }

        // 按操作类型筛选
        if (actionFilter !== 'all') {
            switch (actionFilter) {
                case 'double':
                    filteredHistory = filteredHistory.filter(record =>
                        record.actionType === '加倍' || record.isDoubleDown === true);
                    break;
                case 'split':
                    // 判断数据来源（模拟系统/游戏本体），适配不同分牌筛选逻辑
                    const isSimData = filteredHistory.some(r => r.hasOwnProperty('game'));
                    if (isSimData) {
                        // 模拟系统数据：只要该玩家该局有多手牌且isSplit为true，筛选所有isSplit为true的手牌
                        const splitGrouped = {};
                        filteredHistory.forEach(record => {
                            const key = `${record.game}-${record.playerIndex}`;
                            if (!splitGrouped[key]) splitGrouped[key] = [];
                            splitGrouped[key].push(record);
                        });
                        filteredHistory = Object.values(splitGrouped).flatMap(records => {
                            const hasSplit = records.length > 1 && records.some(r => r.isSplit === true);
                            if (!hasSplit) return [];
                            return records.filter(r => r.isSplit === true);
                        });
                    } else {
                        // 游戏本体数据：只要该玩家该局有多手牌且isSplit为true或actionType为'分牌'，筛选所有分牌手牌
                        const splitGrouped = {};
                        filteredHistory.forEach(record => {
                            const key = `${record.gameId}-${record.playerIndex}`;
                            if (!splitGrouped[key]) splitGrouped[key] = [];
                            splitGrouped[key].push(record);
                        });
                        filteredHistory = Object.values(splitGrouped).flatMap(records => {
                            const hasSplit = records.length > 1 && records.some(r => r.isSplit === true || r.actionType === '分牌');
                            if (!hasSplit) return [];
                            return records.filter(r => r.isSplit === true || r.actionType === '分牌');
                        });
                    }
                    break;
                case 'normal':
                    filteredHistory = filteredHistory.filter(record =>
                        (record.actionType === '普通' || !record.actionType) &&
                        !record.isDoubleDown &&
                        !record.isSplit &&
                        !(record.handIdentifier && record.handIdentifier.includes('手牌')));
                    break;
            }
        }

        // 如果没有历史记录，显示提示
        if (filteredHistory.length === 0) {
            const noDataMsg = document.createElement('div');
            noDataMsg.className = 'no-data-message';
            noDataMsg.textContent = '暂无符合条件的玩家数据';
            this.playerTotalStatsDiv.appendChild(noDataMsg);
            return;
        }

        // 创建总体统计区域
        const totalStatsDiv = document.createElement('div');
        totalStatsDiv.className = 'total-stats-section';

        // 计算总体统计数据
        const totalGames = filteredHistory.length;
        let totalProfit = 0;
        let winCount = 0;
        let loseCount = 0;
        let pushCount = 0;
        let blackjackCount = 0;
        let totalBet = 0;
        let doubleCount = 0;
        let splitCount = 0;
        let surrenderCount = 0;

        filteredHistory.forEach(record => {
            totalProfit += record.profit;
            totalBet += record.bet;

            if (record.result === '赢' || record.result === '黑杰克') {
                winCount++;
                if (record.result === '黑杰克') {
                    blackjackCount++;
                }
            } else if (record.result === '输' || record.result === '爆牌') {
                loseCount++;
            } else if (record.result === '投降') {
                loseCount++;
                surrenderCount++;
            } else if (record.result === '平') {
                pushCount++;
            }

            // 统计操作类型
            if (record.actionType === '加倍') {
                doubleCount++;
            } else if (record.actionType === '分牌') {
                splitCount++;
            }
        });

        const winRate = totalGames > 0 ? ((winCount / totalGames) * 100).toFixed(1) : '0.0';
        const avgBet = totalGames > 0 ? Math.round(totalBet / totalGames) : 0;

        // 创建总体统计表格
        const totalStatsTable = document.createElement('table');
        totalStatsTable.className = 'total-stats-table';

        // 创建表头
        const totalStatsHeader = document.createElement('thead');
        const totalStatsHeaderRow = document.createElement('tr');
        ['总局数', '胜率', '赢局数', '输局数', '平局数', '黑杰克', '加倍', '分牌', '投降', '总盈亏', '平均下注'].forEach(text => {
            const th = document.createElement('th');
            th.textContent = text;
            totalStatsHeaderRow.appendChild(th);
        });
        totalStatsHeader.appendChild(totalStatsHeaderRow);
        totalStatsTable.appendChild(totalStatsHeader);

        // 创建表体
        const totalStatsBody = document.createElement('tbody');
        const totalStatsRow = document.createElement('tr');

        // 总局数
        const totalGamesCell = document.createElement('td');
        totalGamesCell.textContent = totalGames;
        totalStatsRow.appendChild(totalGamesCell);

        // 胜率
        const winRateCell = document.createElement('td');
        winRateCell.textContent = `${winRate}%`;
        totalStatsRow.appendChild(winRateCell);

        // 赢局数
        const winCountCell = document.createElement('td');
        winCountCell.textContent = winCount;
        winCountCell.className = 'win-count';
        totalStatsRow.appendChild(winCountCell);

        // 输局数
        const loseCountCell = document.createElement('td');
        loseCountCell.textContent = loseCount;
        loseCountCell.className = 'lose-count';
        totalStatsRow.appendChild(loseCountCell);

        // 平局数
        const pushCountCell = document.createElement('td');
        pushCountCell.textContent = pushCount;
        totalStatsRow.appendChild(pushCountCell);

        // 黑杰克
        const blackjackCountCell = document.createElement('td');
        blackjackCountCell.textContent = blackjackCount;
        blackjackCountCell.className = 'blackjack-count';
        totalStatsRow.appendChild(blackjackCountCell);

        // 加倍次数
        const doubleCountCell = document.createElement('td');
        doubleCountCell.textContent = doubleCount;
        totalStatsRow.appendChild(doubleCountCell);

        // 分牌次数
        const splitCountCell = document.createElement('td');
        splitCountCell.textContent = splitCount;
        totalStatsRow.appendChild(splitCountCell);

        // 投降次数
        const surrenderCountCell = document.createElement('td');
        surrenderCountCell.textContent = surrenderCount;
        totalStatsRow.appendChild(surrenderCountCell);

        // 总盈亏
        const totalProfitCell = document.createElement('td');
        totalProfitCell.textContent = totalProfit > 0 ? `+${totalProfit}` : totalProfit;
        totalProfitCell.className = totalProfit > 0 ? 'profit-positive' : (totalProfit < 0 ? 'profit-negative' : '');
        totalStatsRow.appendChild(totalProfitCell);

        // 平均下注
        const avgBetCell = document.createElement('td');
        avgBetCell.textContent = avgBet;
        totalStatsRow.appendChild(avgBetCell);

        totalStatsBody.appendChild(totalStatsRow);
        totalStatsTable.appendChild(totalStatsBody);
        totalStatsDiv.appendChild(totalStatsTable);

        this.playerTotalStatsDiv.appendChild(totalStatsDiv);
    }

    /**
     * 获取结果对应的CSS类
     * @param {string} result - 结果文本
     * @returns {string} CSS类名
     */
    getResultClass(result) {
        switch (result) {
            case '赢':
            case '黑杰克':
                return 'result-win';
            case '输':
            case '爆牌':
            case '投降':
                return 'result-lose';
            case '平':
                return 'result-push';
            default:
                return '';
        }
    }

    /**
     * 格式化时间戳为可读时间
     * @param {number} timestamp - 时间戳
     * @returns {string} 格式化后的时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');

        return `${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 记录游戏结果
     * @param {number} playerIndex - 玩家索引
     * @param {string} playerName - 玩家名称
     * @param {number} bet - 下注金额
     * @param {string} result - 游戏结果
     * @param {number} profit - 盈亏金额
     * @param {number} originalBet - 原始下注金额，不包括加倍部分
     * @param {string} actionType - 操作类型（加倍、投降、分牌等）
     * @param {Array} playerCards - 玩家手牌
     * @param {Array} dealerCards - 庄家手牌
     * @param {number} handIndex - 手牌索引，用于分牌情况
     * @param {number} trueCount - 当前真数
     * @param {number} balance - 当前剩余筹码
     */
    recordGame(playerIndex, playerName, bet, result, profit, originalBet, actionType, playerCards, dealerCards, handIndex = 0, trueCount = 0, balance) {
        // 如果没有提供balance，尝试从游戏中获取当前玩家的筹码
        if (balance === undefined && window.game && window.game.players) {
            const player = window.game.players[playerIndex];
            if (player) {
                balance = player.chips;
                console.log(`从游戏中获取玩家${playerName}的筹码: ${balance}`);
            }
        }
        // 增加游戏局数
        this.gameCount++;

        // 如果没有提供原始下注金额，则假设与当前下注金额相同
        // 对于加倍的情况，originalBet应该是bet的一半
        if (originalBet === undefined) {
            // 检查结果文本中是否包含"加倍"标记
            if (result.includes('加倍') || result.includes('【加倍】')) {
                originalBet = Math.round(bet / 2); // 原始下注是加倍后金额的一半
            } else {
                originalBet = bet; // 没有加倍，原始下注等于当前下注
            }
        }

        // 如果没有提供操作类型，尝试从结果文本中推断
        if (!actionType) {
            if (result.includes('加倍') || result.includes('【加倍】')) {
                actionType = '加倍';
            } else if (result.includes('投降')) {
                actionType = '投降';
            } else if (result.includes('分牌') || result.includes('【分牌】')) {
                actionType = '分牌';
            } else {
                actionType = '普通';
            }
        }

        // 创建记录对象
        const record = {
            gameId: this.gameCount,
            playerIndex: playerIndex,
            playerName: playerName,
            handIndex: handIndex,
            bet: bet,
            originalBet: originalBet,
            result: result,
            profit: profit,
            timestamp: Date.now(),
            actionType: actionType,
            playerCards: playerCards || [],
            dealerCards: dealerCards || [],
            trueCount: trueCount,
            balance: balance, // 保留原始传入的balance值，即使是undefined
            isDoubleDown: actionType === '加倍',
            // 修改分牌标识逻辑：
            // 1. 如果操作类型是'分牌'，则标记为分牌
            // 2. 如果手牌来自分牌（通过playerCards.split或手牌对象中的split属性判断）
            isSplit: actionType === '分牌' ||
                     (playerCards && playerCards.split) ||
                     (playerCards && typeof playerCards === 'object' && playerCards.length > 0 && playerCards[0] && playerCards[0].split)
        };

        // 添加手牌标识字段
        if (record.isSplit && record.handIndex !== undefined) {
            record.handIdentifier = `手牌${record.handIndex + 1}`;
        }

        // 添加带加倍标识的结果文本字段
        if (record.isDoubleDown) {
            record.resultWithDouble = `${record.result}【加倍】`;
        } else {
            record.resultWithDouble = record.result;
        }

        // 如果记录中的balance仍然是undefined，再次尝试从其他来源获取
        if (record.balance === undefined) {
            // 尝试从playerCards中获取
            if (playerCards && typeof playerCards === 'object') {
                // 检查是否是手牌对象，并且有chips属性
                if (playerCards.chips !== undefined) {
                    record.balance = playerCards.chips;
                    console.log(`从playerCards对象获取筹码: ${record.balance}`);
                } else if (playerCards.player && playerCards.player.chips !== undefined) {
                    record.balance = playerCards.player.chips;
                    console.log(`从playerCards.player对象获取筹码: ${record.balance}`);
                }
            }

            // 如果仍然没有获取到balance，再次尝试从游戏中获取
            if (record.balance === undefined && window.game && window.game.players) {
                const player = window.game.players[playerIndex];
                if (player) {
                    record.balance = player.chips;
                    console.log(`最后尝试从游戏中获取玩家${playerName}的筹码: ${record.balance}`);
                }
            }
        }

        // 添加到历史记录
        this.history.push(record);

        // 如果是新玩家，初始化该玩家的历史记录
        if (!this.playerHistory[playerIndex]) {
            this.playerHistory[playerIndex] = [];
        }

        // 添加到玩家历史记录
        this.playerHistory[playerIndex].push(record);

        // 保存历史记录到本地存储
        this.saveHistory();

        // 如果历史记录面板显示中，更新显示
        if (this.historyPanel.style.display !== 'none') {
            // 更新下注金额选择器
            this.updateBetSelector();

            // 获取当前筛选条件
            if (this.playerSelect && this.historyBetSelect && this.resultSelect && this.actionSelect) {
                const playerFilter = this.playerSelect.value;
                const betFilter = this.historyBetSelect.value;
                const resultFilter = this.resultSelect.value;
                const actionFilter = this.actionSelect.value;
                this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, this.currentPage);
            }
        }
    }

    /**
     * 保存历史记录到本地存储
     */
    saveHistory() {
        try {
            localStorage.setItem('bettingHistory', JSON.stringify(this.history));
            localStorage.setItem('gameCount', this.gameCount.toString());
        } catch (e) {
            console.error('保存历史记录失败:', e);
        }
    }

    /**
     * 从本地存储加载历史记录
     */
    loadHistory() {
        try {
            const historyJson = localStorage.getItem('bettingHistory');
            const gameCount = localStorage.getItem('gameCount');

            if (historyJson) {
                this.history = JSON.parse(historyJson);

                // 重建玩家历史记录
                this.playerHistory = {};
                this.history.forEach(record => {
                    if (!this.playerHistory[record.playerIndex]) {
                        this.playerHistory[record.playerIndex] = [];
                    }
                    this.playerHistory[record.playerIndex].push(record);
                });
            }

            if (gameCount) {
                this.gameCount = parseInt(gameCount);
            }

            console.log(`加载历史记录: ${this.history.length}条记录`);
        } catch (e) {
            console.error('加载历史记录失败:', e);
            // 出错时重置历史记录
            this.history = [];
            this.playerHistory = {};
            this.gameCount = 0;
        }
    }

    /**
     * 清除历史记录
     */
    clearHistory() {
        console.log('开始全面清除历史记录和重置统计数据...');

        // 清空历史记录数组
        this.history = [];
        this.playerHistory = {};
        this.gameCount = 0;

        // 清除本地存储中的所有相关数据
        localStorage.removeItem('bettingHistory');
        localStorage.removeItem('gameCount');
        localStorage.removeItem('playerStats');
        localStorage.removeItem('longestWinStreak');
        localStorage.removeItem('longestLoseStreak');
        localStorage.removeItem('currentWinStreak');
        localStorage.removeItem('currentLoseStreak');

        // 重置全局玩家统计对象中的所有相关字段
        if (window.playerStats) {
            console.log('重置全局玩家统计对象中的所有字段');

            // 重置基本统计数据
            window.playerStats.totalBetAmount = 0;
            window.playerStats.totalWinAmount = 0;
            window.playerStats.totalLossAmount = 0;
            window.playerStats.totalProfit = 0;
            window.playerStats.splitCount = 0;

            // 强制重置连胜连败记录
            window.playerStats.longestWinStreak = 0;
            window.playerStats.longestLoseStreak = 0;
            window.playerStats.currentWinStreak = 0;
            window.playerStats.currentLoseStreak = 0;

            // 重置其他可能影响统计的字段
            window.playerStats.largestWin = 0;
            window.playerStats.largestLoss = 0;

            // 如果有玩家，使用第一个玩家的当前筹码作为最高/最低筹码
            if (window.game && window.game.players && window.game.players.length > 0) {
                const firstPlayer = window.game.players[0];
                if (firstPlayer) {
                    window.playerStats.highestChips = firstPlayer.chips || 10000;
                    window.playerStats.lowestChips = firstPlayer.chips || 10000;
                }
            } else {
                window.playerStats.highestChips = 10000;
                window.playerStats.lowestChips = 10000;
            }

            // 强制更新玩家统计面板显示
            if (typeof window.playerStats.updateStatsDisplay === 'function') {
                window.playerStats.updateStatsDisplay();
                console.log('强制更新玩家统计面板显示');
            }
        }

        // 重置游戏中所有玩家的统计数据
        if (window.game && window.game.players) {
            console.log('重置游戏中所有玩家的统计数据');
            window.game.players.forEach(player => {
                if (player && typeof player.resetStats === 'function') {
                    // 使用player的resetStats方法重置基本统计数据
                    player.resetStats();

                    // 额外确保连胜连败相关字段被重置
                    if (player.stats) {
                        player.stats.longestWinStreak = 0;
                        player.stats.longestLoseStreak = 0;
                        player.stats.currentWinStreak = 0;
                        player.stats.currentLoseStreak = 0;
                        player.stats.largestWin = 0;
                        player.stats.largestLoss = 0;
                    }
                }

                // 清除可能存在的总下注金额字段
                if (player.totalBetAmount !== undefined) {
                    player.totalBetAmount = 0;
                }
            });
        }

        // 重置运行计数
        if (window.cardCountingSystem) {
            window.cardCountingSystem.runningCount = 0;
            console.log('重置算牌系统的运行计数');
        }

        console.log('历史记录清除和统计数据重置完成');

        // 更新玩家选择下拉框
        if (this.playerSelect) {
            this.updatePlayerSelector();
        }

        // 更新下注金额选择下拉框
        if (this.historyBetSelect) {
            this.updateBetSelector();
        }

        // 更新历史记录表格
        if (this.playerSelect && this.historyBetSelect && this.resultSelect && this.actionSelect) {
            const playerFilter = 'all';
            const betFilter = 'all';
            const resultFilter = 'all';
            const actionFilter = 'all';
            this.updateHistoryTable(playerFilter, betFilter, resultFilter, actionFilter, 1);
        } else {
            // 如果筛选器未初始化，直接清空表格
            if (this.historyTableBody) {
                this.historyTableBody.innerHTML = '';
            }

            // 不再需要清空historyStats，因为它已被移除

            // 清空玩家总数据
            if (this.playerTotalStatsDiv) {
                this.playerTotalStatsDiv.innerHTML = '';
            }
        }

        console.log('历史记录已清除，所有统计数据已重置');
    }

    /**
     * 获取玩家历史记录
     * @param {number} playerIndex - 玩家索引
     * @returns {Array} 玩家历史记录
     */
    getPlayerHistory(playerIndex) {
        return this.playerHistory[playerIndex] || [];
    }

    /**
     * 获取玩家统计数据
     * @param {number} playerIndex - 玩家索引
     * @returns {Object} 玩家统计数据
     */
    getPlayerStats(playerIndex) {
        const playerHistory = this.getPlayerHistory(playerIndex);
        if (playerHistory.length === 0) {
            return {
                totalGames: 0,
                winRate: 0,
                winCount: 0,
                loseCount: 0,
                pushCount: 0,
                blackjackCount: 0,
                totalProfit: 0,
                avgBet: 0
            };
        }

        // 计算统计数据
        let totalProfit = 0;
        let winCount = 0;
        let loseCount = 0;
        let pushCount = 0;
        let blackjackCount = 0;
        let totalBet = 0;

        playerHistory.forEach(record => {
            totalProfit += record.profit;
            totalBet += record.bet;

            if (record.result === '赢' || record.result === '黑杰克') {
                winCount++;
                if (record.result === '黑杰克') {
                    blackjackCount++;
                }
            } else if (record.result === '输' || record.result === '爆牌' || record.result === '投降') {
                loseCount++;
            } else if (record.result === '平') {
                pushCount++;
            }
        });

        const totalGames = playerHistory.length;
        const winRate = totalGames > 0 ? ((winCount / totalGames) * 100).toFixed(1) : '0.0';
        const avgBet = totalGames > 0 ? Math.round(totalBet / totalGames) : 0;

        return {
            totalGames,
            winRate,
            winCount,
            loseCount,
            pushCount,
            blackjackCount,
            totalProfit,
            avgBet
        };
    }
}

// 创建全局实例
window.bettingHistory = new BettingHistory();