class Card {
    constructor(suit, rank) {
        this.suit = suit;
        this.rank = rank;
        this.hidden = false;
    }

    // 获取卡牌点数
    getValue() {
        try {
        if (this.rank === 'A') return 11;
        if (['J', 'Q', 'K'].includes(this.rank)) return 10;
            const value = parseInt(this.rank);
            if (isNaN(value)) {
                console.error('无效的卡牌点数:', this.rank, this);
                return 0; // 返回0作为回退值
            }
            return value;
        } catch (error) {
            console.error('获取卡牌点数时出错:', error, this);
            return 0; // 错误时返回0
        }
    }

    // 获取流水数值（用于计算真实牌数）
    getRunningCount() {
        // 如果存在计牌系统，使用当前选择的计牌系统
        if (window.cardCountingSystem) {
            return window.cardCountingSystem.getCardValue(this);
        }

        // 默认使用Hi-Lo系统
        const value = this.getValue();
        if (value >= 2 && value <= 6) return 1;
        if (value >= 7 && value <= 9) return 0;
        return -1; // 10点牌和A
    }

    // 获取卡牌显示文本
    toString() {
        if (this.hidden) return '?';
        const suitSymbols = {
            'hearts': '♥',
            'diamonds': '♦',
            'clubs': '♣',
            'spades': '♠'
        };
        return `${suitSymbols[this.suit]}${this.rank}`;
    }

    // 创建卡牌DOM元素
    createCardElement() {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'card' + (this.hidden ? ' hidden' : '');

        // 保存卡牌信息作为数据属性，用于后续比较
        if (!this.hidden) {
            cardDiv.dataset.suit = this.suit;
            cardDiv.dataset.rank = this.rank;
        }

        // 创建图片元素
        const cardImg = document.createElement('img');
        cardImg.className = 'card-img';

        // 花色简写映射
        const suitMappings = {
            'hearts': 'H',
            'diamonds': 'D',
            'clubs': 'C',
            'spades': 'S'
        };

        if (!this.hidden) {
            // 使用对应的正面牌图片
            cardImg.src = `PNG/${this.rank}${suitMappings[this.suit]}.png`;
            cardImg.alt = `${this.rank} of ${this.suit}`;
        } else {
            // 使用卡牌背面图片
            cardImg.src = 'PNG/red_back.png';
            cardImg.alt = 'Card back';
        }

        cardDiv.appendChild(cardImg);
        return cardDiv;
    }

    // 设置卡牌是否隐藏
    setHidden(hidden) {
        this.hidden = hidden;
    }
}