/**
 * BackgroundRunner.js - 高性能后台运行解决方案
 * 优化版本 2.0 - 进一步降低资源消耗，提高响应速度和稳定性
 */
(function() {
    // 创建日志记录器
    const logger = window.Logger ? window.Logger.getLogger('BackgroundRunner') : {
        debug: () => {},
        info: () => {},
        warn: console.warn,
        error: console.error
    };

    // 性能优化配置项
    const config = {
        checkInterval: 60,        // 处于前台时的检查间隔(ms)
        backgroundInterval: 120,  // 处于后台时的检查间隔(ms)
        maxCallbackRate: 30,      // 前台回调最大执行频率(ms)
        backgroundCallbackRate: 60, // 后台回调最大执行频率(ms)
        heartbeatInterval: 2000,  // 心跳检测间隔(ms)
        debug: false,             // 调试日志开关
        audioVolume: 0.0001,      // 音频保活音量
        useAudioKeepAlive: true   // 是否使用音频保活
    };

    // 内部变量
    let worker = null;
    let fallbackIntervalId = null;
    let heartbeatIntervalId = null;
    let lastCallbackTime = 0;
    let callbackFunction = null;
    let workerActive = true;
    let inActiveTab = !document.hidden;
    let audioContext = null;
    let audioNode = null;

    // 创建高性能后台runner对象
    window.BackgroundRunner = {
        // 公开运行状态供外部查询
        isRunning: false,

        // 初始化
        init: function(options = {}) {
            // 合并配置选项
            Object.assign(config, options);

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
                inActiveTab = !document.hidden;

                // 页面可见性变化时检查Worker状态
                if (this.isRunning) {
                    if (document.hidden) {
                        // 进入后台时，调整Worker工作频率
                        this._updateWorkerInterval(config.backgroundInterval);
                        // 启动音频保活（如果启用）
                        if (config.useAudioKeepAlive) {
                            this._startAudioKeepAlive();
                        }
                    } else {
                        // 回到前台时，恢复正常工作频率
                        this._updateWorkerInterval(config.checkInterval);
                        // 停止音频保活
                        this._stopAudioKeepAlive();
                        // 即时执行一次回调以提高响应速度
                        if (typeof callbackFunction === 'function') {
                            window.requestAnimationFrame(() => {
                                callbackFunction();
                            });
                        }
                    }
                }
            });

            logger.debug('BackgroundRunner 已初始化');

            return this;
        },

        // 启动音频保活
        _startAudioKeepAlive: function() {
            if (audioContext) return;

            try {
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // 创建振荡器
                const oscillator = audioContext.createOscillator();
                oscillator.type = 'sine';
                oscillator.frequency.value = 440; // 440Hz

                // 创建增益节点并设置极低音量
                audioNode = audioContext.createGain();
                audioNode.gain.value = config.audioVolume;

                // 连接节点
                oscillator.connect(audioNode);
                audioNode.connect(audioContext.destination);

                // 开始播放
                oscillator.start();

                logger.debug('音频保活已启动');
            } catch (e) {
                logger.warn('启动音频保活失败:', e);
            }
        },

        // 停止音频保活
        _stopAudioKeepAlive: function() {
            if (!audioContext) return;

            try {
                // 关闭音频上下文
                audioContext.close();
                audioContext = null;
                audioNode = null;

                logger.debug('音频保活已停止');
            } catch (e) {
                logger.warn('停止音频保活失败:', e);
            }
        },

        // 更新Worker的工作间隔
        _updateWorkerInterval: function(interval) {
            if (worker) {
                worker.postMessage({
                    action: 'updateInterval',
                    interval: interval
                });
            }
        },

        // 创建并启动Web Worker
        _createWorker: function() {
            if (worker) return;

            try {
                // 创建优化的Web Worker代码
                const workerCode = `
                    let intervalId = null;
                    let currentInterval = ${config.checkInterval};

                    // 高效的消息发送函数
                    function sendTick() {
                        self.postMessage({type: 'tick', timestamp: Date.now()});
                    }

                    self.onmessage = function(e) {
                        const data = e.data;

                        if (typeof data === 'object') {
                            if (data.action === 'start') {
                                if (intervalId === null) {
                                    currentInterval = data.interval || ${config.checkInterval};
                                    intervalId = setInterval(sendTick, currentInterval);
                                    setTimeout(sendTick, 10); // 立即发送一次tick减少初始延迟
                                }
                            } else if (data.action === 'stop') {
                                if (intervalId !== null) {
                                    clearInterval(intervalId);
                                    intervalId = null;
                                }
                            } else if (data.action === 'updateInterval') {
                                currentInterval = data.interval;
                                if (intervalId !== null) {
                                    clearInterval(intervalId);
                                    intervalId = setInterval(sendTick, currentInterval);
                                }
                            }
                        } else if (data === 'ping') {
                            // 响应心跳检测
                            self.postMessage({type: 'pong', timestamp: Date.now()});
                        }
                    };
                `;

                const workerBlob = new Blob([workerCode], { type: 'application/javascript' });
                worker = new Worker(URL.createObjectURL(workerBlob));

                worker.onmessage = (e) => {
                    const data = e.data;
                    if (data && data.type) {
                        if (data.type === 'tick' && typeof callbackFunction === 'function') {
                            executeCallback();
                        } else if (data.type === 'pong') {
                            workerActive = true;
                        }
                    }
                };

                // 启动Worker，并传入当前合适的间隔设置
                worker.postMessage({
                    action: 'start',
                    interval: inActiveTab ? config.checkInterval : config.backgroundInterval
                });

                // 设置心跳检测
                this._setupHeartbeat();

                logger.debug('Worker已创建并启动');
            } catch (e) {
                logger.warn('创建Worker失败，回退到setInterval:', e);
                // 回退到setInterval方案
                this._setupFallback();
            }
        },

        // 设置心跳检测
        _setupHeartbeat: function() {
            if (heartbeatIntervalId) {
                clearInterval(heartbeatIntervalId);
            }

            heartbeatIntervalId = setInterval(() => {
                if (!this.isRunning) {
                    clearInterval(heartbeatIntervalId);
                    heartbeatIntervalId = null;
                    return;
                }

                // 检测Worker是否活跃
                if (worker) {
                    workerActive = false;
                    worker.postMessage('ping');

                    // 给Worker一定时间响应
                    setTimeout(() => {
                        if (!workerActive && this.isRunning) {
                            logger.debug('Worker未响应，重启Worker');
                            // Worker没有响应，重新创建
                            this._restartWorker();
                        }
                    }, 500);
                }
            }, config.heartbeatInterval);
        },

        // 重启Worker
        _restartWorker: function() {
            if (worker) {
                try {
                    worker.terminate();
                } catch (e) {
                    logger.warn('终止Worker失败:', e);
                }
                worker = null;
            }

            // 异步创建新Worker
            setTimeout(() => {
                if (this.isRunning) {
                    this._createWorker();
                }
            }, 100);
        },

        // 设置回退方案
        _setupFallback: function() {
            if (fallbackIntervalId === null) {
                const interval = inActiveTab ? config.checkInterval : config.backgroundInterval;
                fallbackIntervalId = setInterval(() => {
                    if (typeof callbackFunction === 'function') {
                        executeCallback();
                    }
                }, interval);

                logger.debug('已启用setInterval回退方案');
            }
        },

        // 启动后台运行
        start: function(callback) {
            if (this.isRunning) return this;

            // 保存回调函数
            if (typeof callback === 'function') {
                callbackFunction = callback;
            }

            // 创建Worker
            this._createWorker();

            // 如果在后台，启动音频保活
            if (document.hidden && config.useAudioKeepAlive) {
                this._startAudioKeepAlive();
            }

            this.isRunning = true;

            logger.info('BackgroundRunner 已启动');

            return this;
        },

        // 停止后台运行
        stop: function() {
            this.isRunning = false;

            // 停止Worker
            if (worker) {
                worker.postMessage({action: 'stop'});
                worker.terminate();
                worker = null;
            }

            // 清除心跳检测
            if (heartbeatIntervalId) {
                clearInterval(heartbeatIntervalId);
                heartbeatIntervalId = null;
            }

            // 清除回退定时器
            if (fallbackIntervalId !== null) {
                clearInterval(fallbackIntervalId);
                fallbackIntervalId = null;
            }

            // 停止音频保活
            this._stopAudioKeepAlive();

            logger.info('BackgroundRunner 已停止');

            return this;
        },

        // 强制激活 - 解决长时间运行可能导致的休眠问题
        forceActivate: function() {
            if (!this.isRunning) return this;

            // 重启Worker
            this._restartWorker();

            // 如果在后台，确保音频保活运行
            if (document.hidden && config.useAudioKeepAlive) {
                this._stopAudioKeepAlive(); // 先停止，以防有问题
                this._startAudioKeepAlive(); // 再重新启动
            }

            // 如果在活动标签页，立即执行一次回调
            if (inActiveTab && typeof callbackFunction === 'function') {
                window.requestAnimationFrame(callbackFunction);
            }

            logger.debug('BackgroundRunner 已强制激活');

            return this;
        },

        // 设置回调函数
        setCallback: function(callback) {
            if (typeof callback === 'function') {
                callbackFunction = callback;
            }
            return this;
        }
    };

    // 执行回调函数（带节流控制）
    function executeCallback() {
        const now = Date.now();
        // 根据标签页状态决定回调频率
        const effectiveRate = inActiveTab ? config.maxCallbackRate : config.backgroundCallbackRate;

        if (now - lastCallbackTime >= effectiveRate) {
            try {
                if (inActiveTab) {
                    // 在前台时使用rAF优化性能
                    window.requestAnimationFrame(callbackFunction);
                } else {
                    callbackFunction();
                }
            } catch (e) {
                logger.error('执行回调时出错:', e);
            }
            lastCallbackTime = now;
        }
    }
})();