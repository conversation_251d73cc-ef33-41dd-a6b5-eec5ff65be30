/* 玩家统计信息面板样式 */
.player-stats-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1000px; /* 增加最大宽度 */
    max-height: 90vh;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 面板标题 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(90deg, #8b5cf6, #6366f1);
    color: white;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

/* 关闭按钮 */
.close-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.close-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 内容区域 */
.stats-content {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
}

/* 玩家选择器 */
.player-selector {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}

.player-selector select {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(30, 41, 59, 0.8);
    color: white;
    font-size: 14px;
    min-width: 150px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
}

.player-selector select:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(30, 41, 59, 0.9);
}

.player-selector select:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* 统计信息网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

/* 统计信息卡片 */
.stat-card {
    background: rgba(30, 41, 59, 0.6);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: rgba(30, 41, 59, 0.8);
}

.stat-label {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-bottom: 8px;
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #e2e8f0;
    text-align: center;
}

/* 正负值颜色 */
.positive-value {
    color: #10b981;
}

.negative-value {
    color: #ef4444;
}

.neutral-value {
    color: #f59e0b;
}

/* 筛选器容器 */
.filter-container {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 60px; /* 固定高度 */
    box-sizing: border-box;
}

/* 筛选项 */
.filter-item {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 36px; /* 统一高度 */
    margin-top: 0;
    margin-bottom: 0;
}

.filter-item label {
    color: #e2e8f0;
    font-weight: 500;
    white-space: nowrap;
}

.filter-item select {
    padding: 0 12px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(30, 41, 59, 0.8);
    color: white;
    font-size: 14px;
    min-width: 150px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
    height: 36px; /* 统一高度 */
    box-sizing: border-box;
    line-height: 36px; /* 确保文本垂直居中 */
    vertical-align: middle;
}

.filter-item select:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(30, 41, 59, 0.9);
}

.filter-item select:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* 重置筛选按钮 */
.reset-filter-btn {
    padding: 0 15px;
    background: linear-gradient(135deg, #475569, #334155);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: auto;
    height: 36px; /* 与筛选框高度一致 */
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 36px; /* 确保文本垂直居中 */
    vertical-align: middle;
    box-sizing: border-box;
}

.reset-filter-btn:hover {
    background: linear-gradient(135deg, #334155, #1e293b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 新的玩家统计样式 - 优化布局 */
.player-stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.player-stats-section {
    background-color: #1e2536;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.player-stats-title {
    margin-top: 0;
    margin-bottom: 10px;
    color: #38bdf8;
    font-size: 1rem;
    border-bottom: 1px solid #38bdf8;
    padding-bottom: 4px;
}

.player-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

/* 让总体统计和记录数据区域占据两列 */
.player-stats-section:nth-child(3),
.player-stats-section:nth-child(4) {
    grid-column: span 2;
}

.player-stats-section:nth-child(3) .player-stats-grid,
.player-stats-section:nth-child(4) .player-stats-grid {
    grid-template-columns: repeat(4, 1fr);
}

.player-stats-card {
    background-color: #293547;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 80px;
}

.player-stats-card:hover {
    background-color: #354761;
}

.player-stats-card-title {
    color: #a1a1aa;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.player-stats-card-value {
    font-size: 1.4rem;
    font-weight: bold;
}

.player-stats-card-value.positive {
    color: #34d399;
}

.player-stats-card-value.negative {
    color: #f87171;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .player-stats-container {
        grid-template-columns: 1fr;
    }

    .player-stats-section:nth-child(3),
    .player-stats-section:nth-child(4) {
        grid-column: span 1;
    }

    .player-stats-section:nth-child(3) .player-stats-grid,
    .player-stats-section:nth-child(4) .player-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
