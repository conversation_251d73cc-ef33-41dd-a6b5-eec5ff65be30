/**
 * 21点算牌算法管理模块
 * 支持多种算牌算法系统，包括Hi-Lo, Omega II和Halves
 */

// 创建日志记录器
const cardCountingLogger = window.Logger ? window.Logger.getLogger('CardCounting') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

/**
 * 算牌算法系统类
 * 管理不同的算牌算法和自动下注策略
 */
class CardCountingSystem {
    constructor() {
        // 当前选择的算牌系统
        this.currentSystem = 'hi-lo'; // 默认使用Hi-Lo系统

        // 算牌系统定义
        this.systems = {
            'hi-lo': {
                name: 'Hi-<PERSON>',
                description: '最常用的算牌系统，简单易学',
                getCardValue: this.getHiLoValue,
                values: {
                    '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,
                    '7': 0, '8': 0, '9': 0,
                    '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
                }
            },
            'omega-ii': {
                name: 'Omega II',
                description: '更精确的算牌系统，适合进阶玩家',
                getCardValue: this.getOmegaIIValue,
                values: {
                    '2': 1, '3': 1, '4': 2, '5': 2, '6': 2, '7': 1,
                    '8': 0, '9': -1,
                    '10': -2, 'J': -2, 'Q': -2, 'K': -2, 'A': 0
                }
            },
            'halves': {
                name: 'Halves',
                description: '高级算牌系统，使用小数点值',
                getCardValue: this.getHalvesValue,
                values: {
                    '2': 0.5, '3': 1, '4': 1, '5': 1.5, '6': 1, '7': 0.5,
                    '8': 0, '9': -0.5,
                    '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
                }
            }
        };

        // 自动下注策略
        this.autoBetStrategy = {
            enabled: false,
            thresholds: [
                { trueCount: -999, bet: 100 }, // 默认最小下注
                { trueCount: 1, bet: 200 },
                { trueCount: 2, bet: 300 },
                { trueCount: 3, bet: 400 },
                { trueCount: 4, bet: 500 },
                { trueCount: 5, bet: 600 }
            ]
        };

        cardCountingLogger.info('算牌系统已初始化，默认使用Hi-Lo系统');
    }

    /**
     * 设置当前使用的算牌系统
     * @param {string} systemName - 算牌系统名称 ('hi-lo', 'omega-ii', 'halves')
     * @returns {boolean} 是否设置成功
     */
    setCountingSystem(systemName) {
        if (this.systems[systemName]) {
            this.currentSystem = systemName;
            cardCountingLogger.info(`已切换到${this.systems[systemName].name}算牌系统`);
            return true;
        }
        cardCountingLogger.error(`未知的算牌系统: ${systemName}`);
        return false;
    }

    /**
     * 获取当前算牌系统名称
     * @returns {string} 当前算牌系统名称
     */
    getCurrentSystem() {
        return this.currentSystem;
    }

    /**
     * 获取当前算牌系统的显示名称
     * @returns {string} 当前算牌系统的显示名称
     */
    getCurrentSystemName() {
        return this.systems[this.currentSystem].name;
    }

    /**
     * 获取卡牌在Hi-Lo系统中的计数值
     * @param {Card} card - 卡牌对象
     * @returns {number} 计数值
     */
    getHiLoValue(card) {
        if (!card || card.hidden) return 0;

        const rank = card.rank;
        if (['2', '3', '4', '5', '6'].includes(rank)) return 1;
        if (['7', '8', '9'].includes(rank)) return 0;
        return -1; // 10, J, Q, K, A
    }

    /**
     * 获取卡牌在Omega II系统中的计数值
     * @param {Card} card - 卡牌对象
     * @returns {number} 计数值
     */
    getOmegaIIValue(card) {
        if (!card || card.hidden) return 0;

        const rank = card.rank;
        if (['2', '3', '7'].includes(rank)) return 1;
        if (['4', '5', '6'].includes(rank)) return 2;
        if (rank === '8' || rank === 'A') return 0;
        if (rank === '9') return -1;
        return -2; // 10, J, Q, K
    }

    /**
     * 获取卡牌在Halves系统中的计数值
     * @param {Card} card - 卡牌对象
     * @returns {number} 计数值
     */
    getHalvesValue(card) {
        if (!card || card.hidden) return 0;

        const rank = card.rank;
        if (rank === '2' || rank === '7') return 0.5;
        if (['3', '4', '6'].includes(rank)) return 1;
        if (rank === '5') return 1.5;
        if (rank === '8') return 0;
        if (rank === '9') return -0.5;
        return -1; // 10, J, Q, K, A
    }

    /**
     * 获取卡牌在当前选择的算牌系统中的计数值
     * @param {Card} card - 卡牌对象
     * @returns {number} 计数值
     */
    getCardValue(card) {
        if (!card || card.hidden) return 0;

        const system = this.systems[this.currentSystem];
        return system.values[card.rank] || 0;
    }

    /**
     * 设置自动下注策略
     * @param {boolean} enabled - 是否启用自动下注
     * @param {Array} thresholds - 真数阈值和下注金额配置
     * @returns {boolean} 是否设置成功
     */
    setAutoBetStrategy(enabled, thresholds = null) {
        this.autoBetStrategy.enabled = enabled;

        if (thresholds && Array.isArray(thresholds)) {
            // 确保阈值按真数从小到大排序
            thresholds.sort((a, b) => a.trueCount - b.trueCount);
            this.autoBetStrategy.thresholds = thresholds;
        }

        cardCountingLogger.info(`算牌系统的自动下注策略已${enabled ? '启用' : '禁用'}`);

        // 如果启用了算牌系统的自动下注，确保游戏的自动下注也是启用的
        if (enabled && window.game) {
            // 不直接修改游戏设置菜单中的复选框状态，只确保游戏内部的自动下注逻辑是启用的
            if (!window.game.isAutoBetting) {
                window.game.isAutoBetting = true;
                cardCountingLogger.info('已自动启用游戏的自动下注功能，以配合算牌系统的自动下注策略');
            }

            // 如果当前是下注阶段，立即触发自动下注
            if (window.game.gameState === 'betting') {
                cardCountingLogger.info('当前处于下注阶段，立即触发自动下注');
                setTimeout(() => {
                    window.game.autoPlaceBets();
                }, 500);
            }
        }

        return true;
    }

    /**
     * 根据真数获取建议下注金额
     * @param {number} trueCount - 真数
     * @returns {number} 建议下注金额
     */
    getRecommendedBet(trueCount) {
        if (!this.autoBetStrategy.enabled) return 0;

        // 找到适用的阈值
        let recommendedBet = this.autoBetStrategy.thresholds[0].bet; // 默认使用最小阈值的下注额

        // 只考虑整数部分来进行下注判断 - 修复精度问题
        const flooredTrueCount = Math.floor(trueCount);
        
        for (const threshold of this.autoBetStrategy.thresholds) {
            if (flooredTrueCount >= threshold.trueCount) {
                recommendedBet = threshold.bet;
            } else {
                break;
            }
        }

        return recommendedBet;
    }
}

// 创建全局实例
window.cardCountingSystem = new CardCountingSystem();

// 显式地将CardCountingSystem类添加到window对象中
window.CardCountingSystem = CardCountingSystem;
