/* 筹码和下注区域样式 */
.chips-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    background-color: rgba(26, 36, 54, 0.8);
    border-radius: 10px;
    border: 1px solid #38bdf8;
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.bet-areas-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 15px 0;
    width: 100%;
    flex-wrap: wrap;
}

.bet-area {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: rgba(99, 102, 241, 0.2);
    border: 2px dashed #6366f1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bet-area:hover {
    background-color: rgba(99, 102, 241, 0.3);
    transform: scale(1.05);
}

.bet-area.active {
    border: 2px solid #8b5cf6;
    background-color: rgba(139, 92, 246, 0.3);
}

.bet-area .player-name {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 5px;
    text-align: center;
}

.bet-area .bet-value {
    font-size: 20px;
    font-weight: bold;
    color: #38bdf8;
    background-color: rgba(12, 21, 37, 0.8);
    padding: 5px 12px;
    border-radius: 15px;
    min-width: 70px;
    text-align: center;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(56, 189, 248, 0.3);
}

/* 当有下注时的样式 */
.bet-area.has-bet {
    border: 2px solid #38bdf8;
    background-color: rgba(56, 189, 248, 0.15);
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.3);
}

/* 筹码选择区域 - 强制横向排列 */
.chips-section {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 10px 0;
    width: 100%;
    padding: 10px;
    background-color: rgba(18, 25, 40, 0.5);
    border-radius: 8px;
}

.chip {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    border: 3px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    user-select: none;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.chip:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.chip:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.chip::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 2px dashed rgba(255, 255, 255, 0.7);
    transform: translate(-50%, -50%);
}

.chip[data-value="10"] {
    background: linear-gradient(135deg, #1e40af, #3b82f6);
}

.chip[data-value="25"] {
    background: linear-gradient(135deg, #166534, #22c55e);
}

.chip[data-value="50"] {
    background: linear-gradient(135deg, #a21caf, #d946ef);
}

.chip[data-value="100"] {
    background: linear-gradient(135deg, #b91c1c, #ef4444);
}

.chip[data-value="500"] {
    background: linear-gradient(135deg, #c2410c, #f97316);
}

.chip[data-value="1000"] {
    background: linear-gradient(135deg, #0f766e, #14b8a6);
}

.chip[data-value="2000"] {
    background: linear-gradient(135deg, #7e22ce, #a855f7);
}

.chip[data-value="5000"] {
    background: linear-gradient(135deg, #be185d, #ec4899);
}

.chip[data-value="2000"] {
    background: linear-gradient(135deg, #7e22ce, #a855f7);
}

.chip[data-value="5000"] {
    background: linear-gradient(135deg, #be185d, #ec4899);
}

.custom-chip {
    background: linear-gradient(135deg, #334155, #64748b) !important;
    font-size: 14px !important;
    cursor: pointer;
}

.custom-chip:hover {
    background: linear-gradient(135deg, #475569, #94a3b8) !important;
}

.custom-chip.selected {
    background: linear-gradient(135deg, #64748b, #94a3b8) !important;
}

.betting-controls {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 10px;
    margin-top: 15px;
    background-color: rgba(18, 25, 40, 0.7);
    border-radius: 8px;
}

.betting-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
}

.player-chips {
    font-weight: bold;
    color: #38bdf8;
}

.betting-actions {
    display: flex;
    gap: 15px;
}

.betting-actions button {
    padding: 8px 15px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s ease;
}

.confirm-bets-btn {
    background-color: #8b5cf6;
    color: #ffffff;
}

.confirm-bets-btn:hover {
    background-color: #6366f1;
    transform: translateY(-2px);
}

.clear-bets-btn {
    background-color: #1e293b;
    color: #ffffff;
    border: 1px solid #8b5cf6;
}

.clear-bets-btn:hover {
    background-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-2px);
}

.confirm-all-bets-btn {
    background-color: #16a34a;
    color: #ffffff;
    padding: 10px 20px;
    min-width: 160px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
    font-weight: bold;
    font-size: 16px;
    border: 2px solid #22c55e;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.confirm-all-bets-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
    z-index: -1;
}

.confirm-all-bets-btn:hover {
    background-color: #22c55e;
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    border-color: #4ade80;
}

.confirm-all-bets-btn:hover:before {
    left: 100%;
}

.confirm-bets-btn:active, .clear-bets-btn:active, .confirm-all-bets-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 选中筹码样式 */
.chip.selected {
    transform: translateY(-8px);
    box-shadow: 0 8px 16px rgba(255, 255, 255, 0.3);
    border: 3px solid #ffffff;
}

/* 选中筹码信息显示 */
.selected-chip-info {
    width: 100%;
    text-align: center;
    padding: 10px;
    background-color: rgba(18, 25, 40, 0.7);
    border-radius: 8px;
    margin-bottom: 10px;
    font-size: 18px;
    color: #ffffff;
    font-weight: bold;
}

#selected-chip-value {
    color: #38bdf8;
    margin-left: 5px;
    padding: 3px 10px;
    background-color: rgba(56, 189, 248, 0.2);
    border-radius: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chips-container {
        width: 95%;
        padding: 10px;
        bottom: 60px;
    }
    
    .bet-areas-container {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .bet-area {
        width: 80px;
        height: 80px;
    }
    
    .bet-area .bet-value {
        font-size: 16px;
        padding: 3px 8px;
        min-width: 60px;
    }
    
    .chip {
        width: 50px;
        height: 50px;
        font-size: 14px;
    }
    
    .chip::after {
        width: 38px;
        height: 38px;
    }
    
    .betting-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .chips-section {
        gap: 10px;
        padding: 5px;
    }
    
    .betting-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .betting-actions button {
        flex-grow: 1;
        min-width: 120px;
        margin-bottom: 5px;
    }
    
    .confirm-all-bets-btn {
        flex-basis: 100%;
        order: -1;
        margin-bottom: 10px;
        padding: 12px 20px;
        font-size: 15px;
    }
}

/* 下注控制按钮样式 */
.betting-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 20px 0;
}

.betting-buttons button {
    padding: 8px 15px;
    border-radius: 8px;
    border: none;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    min-width: 120px;
}

#clear-bets-btn-inline {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#clear-bets-btn-inline:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(239, 68, 68, 0.3);
}

#confirm-bet-btn-inline {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#confirm-bet-btn-inline:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3);
}

#confirm-all-bets-btn-inline {
    background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#confirm-all-bets-btn-inline:hover {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(56, 189, 248, 0.3);
}

#repeat-bets-btn-inline {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    font-size: 14px;
    color: white;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

#repeat-bets-btn-inline:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
}

/* 在小屏幕上调整布局 */
@media (max-width: 768px) {
    .betting-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .betting-buttons {
        margin-left: 0;
        width: 100%;
        justify-content: space-between;
    }
} 