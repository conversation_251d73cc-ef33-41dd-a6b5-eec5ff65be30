// 测试模拟引擎中AA分牌规则的脚本
console.log('开始测试模拟引擎中的AA分牌规则...');

// 模拟一个简单的测试
function testAASpitRule() {
    // 创建一个模拟的手牌对象
    const testHand = [
        { rank: 'A', suit: 'spades' },
        { rank: '3', suit: 'hearts' }
    ];
    
    // 标记为AA分牌
    testHand.isAceSplit = true;
    testHand.split = true;
    
    console.log('测试手牌:', testHand);
    console.log('isAceSplit:', testHand.isAceSplit);
    console.log('手牌长度:', testHand.length);
    
    // 测试AA分牌规则
    if (testHand.isAceSplit && testHand.length >= 2) {
        console.log('✓ AA分牌规则正确：手牌已有2张牌，应该自动停牌');
        return true;
    } else {
        console.log('✗ AA分牌规则错误：手牌应该自动停牌但没有');
        return false;
    }
}

// 运行测试
const result = testAASpitRule();
console.log('测试结果:', result ? '通过' : '失败');

// 导出测试函数以便在浏览器中使用
if (typeof window !== 'undefined') {
    window.testAASpitRule = testAASpitRule;
}
