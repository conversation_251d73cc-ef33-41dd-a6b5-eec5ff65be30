<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AA分牌修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a202c;
            color: #e2e8f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2d3748;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #48bb78;
            color: white;
        }
        .error {
            background-color: #f56565;
            color: white;
        }
        .info {
            background-color: #4299e1;
            color: white;
        }
        button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #3182ce;
        }
        .log {
            background-color: #1a202c;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AA分牌修复测试</h1>
        <p>这个测试页面用于验证模拟引擎中AA分牌后只能要一张牌的规则是否正确实现。</p>
        
        <button onclick="runTest()">运行AA分牌测试</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="results"></div>
        <div id="log" class="log"></div>
    </div>

    <!-- 引入必要的游戏文件 -->
    <script src="js/logger.js"></script>
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/autoStrategy.js"></script>
    <script src="js/card-counting.js"></script>
    <script src="js/game.js"></script>
    <script src="js/simulation-engine.js"></script>

    <script>
        let testLog = [];
        
        function log(message) {
            testLog.push(message);
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = testLog.join('<br>');
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('log').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function runTest() {
            clearLog();
            log('开始AA分牌测试...');
            
            try {
                // 创建游戏实例
                const game = new Game();
                
                // 初始化游戏
                game.initializeGame();
                
                // 添加一个玩家
                game.addPlayer('测试玩家');
                
                // 给玩家足够的筹码
                game.players[0].chips = 10000;
                
                // 创建模拟引擎
                const simulationEngine = new SimulationEngine();
                
                // 配置模拟参数
                const config = {
                    numGames: 1,
                    numDecks: 6,
                    penetration: 0.75,
                    countingSystem: 'none',
                    bettingStrategy: 'fixed',
                    fixedBetAmount: 100,
                    useBasicStrategy: true
                };
                
                // 初始化模拟引擎
                simulationEngine.initialize(config, game);
                
                log('模拟引擎初始化完成');
                
                // 手动设置AA分牌的情况
                const player = game.players[0];
                player.hands = [[]];
                player.bets = [100];
                player.chips = 9900;
                
                // 创建两张A
                const aceSpades = new Card('A', 'spades');
                const aceHearts = new Card('A', 'hearts');
                
                // 设置玩家手牌为AA
                player.hands[0] = [aceSpades, aceHearts];
                
                log('设置玩家手牌为AA: A♠ A♥');
                
                // 设置庄家手牌
                game.dealerHand = [new Card('6', 'clubs'), new Card('K', 'diamonds')];
                
                log('设置庄家手牌为: 6♣ K♦');
                
                // 模拟分牌操作
                log('开始模拟AA分牌...');
                
                // 手动执行分牌逻辑
                const hand = player.hands[0];
                const isAce = hand[0].rank === 'A';
                
                // 创建两手新牌
                const firstHand = [hand[0]];
                const secondHand = [hand[1]];
                
                // 标记为分牌手牌
                firstHand.split = true;
                secondHand.split = true;
                
                // 如果是A分牌，标记特殊状态
                if (isAce) {
                    firstHand.isAceSplit = true;
                    secondHand.isAceSplit = true;
                    log('标记AA分牌特殊状态: isAceSplit = true');
                }
                
                // 为每手牌各补一张
                const card1 = new Card('3', 'hearts');
                const card2 = new Card('5', 'diamonds');
                firstHand.push(card1);
                secondHand.push(card2);
                
                log(`第一手牌补牌: ${card1.rank}${card1.suit}`);
                log(`第二手牌补牌: ${card2.rank}${card2.suit}`);
                
                // 更新玩家手牌
                player.hands[0] = firstHand;
                player.hands.push(secondHand);
                player.bets.push(100);
                player.chips -= 100;
                
                log(`分牌后手牌状态:`);
                log(`第一手牌: ${firstHand.map(c => c.rank + c.suit).join(', ')}, isAceSplit: ${firstHand.isAceSplit}`);
                log(`第二手牌: ${secondHand.map(c => c.rank + c.suit).join(', ')}, isAceSplit: ${secondHand.isAceSplit}`);
                
                // 测试第一手牌是否会自动停牌
                log('测试第一手牌是否会自动停牌...');
                
                if (firstHand.isAceSplit && firstHand.length >= 2) {
                    log('✓ 第一手牌检测到AA分牌状态，应该自动停牌');
                    addResult('第一手牌AA分牌规则正确：自动停牌', 'success');
                } else {
                    log('✗ 第一手牌未检测到AA分牌状态');
                    addResult('第一手牌AA分牌规则错误：未自动停牌', 'error');
                }
                
                // 测试第二手牌是否会自动停牌
                log('测试第二手牌是否会自动停牌...');
                
                if (secondHand.isAceSplit && secondHand.length >= 2) {
                    log('✓ 第二手牌检测到AA分牌状态，应该自动停牌');
                    addResult('第二手牌AA分牌规则正确：自动停牌', 'success');
                } else {
                    log('✗ 第二手牌未检测到AA分牌状态');
                    addResult('第二手牌AA分牌规则错误：未自动停牌', 'error');
                }
                
                // 测试要牌逻辑
                log('测试要牌逻辑是否正确阻止AA分牌后要牌...');
                
                // 模拟要牌检查
                function testHitLogic(hand, handName) {
                    if (hand.isAceSplit && hand.length >= 2) {
                        log(`✓ ${handName}：AA分牌后正确阻止要牌`);
                        return true;
                    } else {
                        log(`✗ ${handName}：AA分牌后未阻止要牌`);
                        return false;
                    }
                }
                
                const firstHandCorrect = testHitLogic(firstHand, '第一手牌');
                const secondHandCorrect = testHitLogic(secondHand, '第二手牌');
                
                if (firstHandCorrect && secondHandCorrect) {
                    addResult('AA分牌要牌限制正确实现', 'success');
                } else {
                    addResult('AA分牌要牌限制实现有误', 'error');
                }
                
                log('测试完成！');
                addResult('AA分牌修复测试完成', 'info');
                
            } catch (error) {
                log(`测试过程中出现错误: ${error.message}`);
                addResult(`测试失败: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        // 页面加载完成后显示说明
        window.addEventListener('load', function() {
            addResult('页面加载完成，点击"运行AA分牌测试"开始测试', 'info');
        });
    </script>
</body>
</html>
