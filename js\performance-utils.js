/**
 * 性能优化工具函数
 * 优化版本 - 减少内存占用和CPU使用
 */

// 节流函数 - 限制函数在特定时间内最多执行一次
function throttle(func, limit) {
    let lastFunc;
    let lastRan;
    return function() {
        const context = this;
        const args = arguments;
        if (!lastRan) {
            func.apply(context, args);
            lastRan = Date.now();
        } else {
            clearTimeout(lastFunc);
            lastFunc = setTimeout(function() {
                if ((Date.now() - lastRan) >= limit) {
                    func.apply(context, args);
                    lastRan = Date.now();
                }
            }, limit - (Date.now() - lastRan));
        }
    };
}

// 防抖函数 - 延迟执行函数，直到特定时间内不再触发
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// DOM元素缓存 - 避免频繁查询DOM
const elementCache = {
    cache: new Map(),

    // 获取DOM元素，优先从缓存获取
    get(selector) {
        if (!this.cache.has(selector)) {
            const element = document.getElementById(selector);
            if (element) {
                this.cache.set(selector, element);
            }
        }
        return this.cache.get(selector);
    },

    // 获取多个DOM元素，优先从缓存获取
    getAll(selectors) {
        const elements = {};
        selectors.forEach(selector => {
            elements[selector] = this.get(selector);
        });
        return elements;
    },

    // 批量更新DOM元素文本内容，只在内容变化时更新
    updateTextContent(updates) {
        for (const [selector, content] of Object.entries(updates)) {
            const element = this.get(selector);
            if (element && element.textContent !== String(content)) {
                element.textContent = content;
            }
        }
    },

    // 清除缓存
    clear() {
        this.cache.clear();
    }
};

// 事件委托函数 - 减少事件监听器数量
function delegate(element, eventType, selector, handler) {
    element.addEventListener(eventType, function(event) {
        let target = event.target;

        // 向上遍历DOM树，查找匹配的选择器
        while (target && target !== element) {
            if (target.matches(selector)) {
                // 使用正确的this绑定和事件对象调用处理程序
                handler.call(target, event);
                return;
            }
            target = target.parentElement;
        }
    });
}

// 性能监控工具 - 仅在开发环境中使用
const PerformanceMonitor = {
    // 是否为开发环境
    isDev: window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1'),

    // FPS监控
    FPSMonitor: {
        frameCount: 0,
        lastTime: 0,
        fps: 0,
        isRunning: false,

        // 启动FPS监控
        start() {
            // 仅在开发环境中启用
            if (!PerformanceMonitor.isDev) return;
            if (this.isRunning) return;

            this.isRunning = true;
            this.lastTime = performance.now();
            this.frameCount = 0;
            this.fps = 0;

            this._createUI();
            this._loop();

            log.debug('FPS监控已启动');
        },

        // 停止FPS监控
        stop() {
            if (!this.isRunning) return;
            this.isRunning = false;

            const displayElement = document.getElementById('fps-monitor');
            if (displayElement) {
                displayElement.remove();
            }

            log.debug('FPS监控已停止');
        },

        // 内部：创建UI显示元素
        _createUI() {
            let displayElement = document.getElementById('fps-monitor');

            if (!displayElement) {
                displayElement = document.createElement('div');
                displayElement.id = 'fps-monitor';
                Object.assign(displayElement.style, {
                    position: 'fixed',
                    top: '10px',
                    right: '10px',
                    background: 'rgba(0, 0, 0, 0.7)',
                    color: '#38bdf8',
                    padding: '6px 10px',
                    borderRadius: '4px',
                    fontSize: '14px',
                    fontFamily: 'monospace',
                    zIndex: '9999',
                    pointerEvents: 'none'
                });
                document.body.appendChild(displayElement);
            }
        },

        // 内部：帧循环
        _loop() {
            if (!this.isRunning) return;

            this.frameCount++;
            const now = performance.now();
            const elapsed = now - this.lastTime;

            // 每秒更新一次FPS计数
            if (elapsed >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / elapsed);
                this.frameCount = 0;
                this.lastTime = now;

                // 更新UI显示
                const displayElement = document.getElementById('fps-monitor');
                if (displayElement) {
                    displayElement.textContent = `FPS: ${this.fps}`;

                    // 根据FPS值变化颜色
                    if (this.fps >= 55) {
                        displayElement.style.color = '#10b981'; // 绿色
                    } else if (this.fps >= 30) {
                        displayElement.style.color = '#f59e0b'; // 黄色
                    } else {
                        displayElement.style.color = '#ef4444'; // 红色
                    }
                }
            }

            requestAnimationFrame(() => this._loop());
        }
    }
};

// 导出工具函数
window.PerformanceUtils = {
    throttle,
    debounce,
    elementCache,
    delegate,
    FPSMonitor: PerformanceMonitor.FPSMonitor
};