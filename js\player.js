/**
 * 创建玩家日志记录器
 * 用于跟踪和记录玩家类中的各种操作、状态变化和错误
 * 如果全局Logger存在则使用它，否则使用简单的控制台日志功能
 */
const playerLogger = window.Logger ? window.Logger.getLogger('Player') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

/**
 * 玩家类
 * 代表21点游戏中的一位玩家实体，管理玩家的所有状态和操作
 * 维护玩家的手牌、筹码、下注、游戏状态和统计数据
 * 支持多手牌操作（分牌）、各种下注操作和结果统计
 */
class Player {
    /**
     * 创建一个玩家对象
     * @param {string} name - 玩家名称，用于在游戏界面和日志中标识玩家
     */
    constructor(name) {
        this.name = name;                // 玩家名称
        this.hands = [[]];               // 玩家的手牌集合，支持分牌，使用二维数组存储(第一维是手牌索引，第二维是手牌中的卡牌)
        this.currentHandIndex = 0;       // 当前操作的手牌索引(分牌后会有多个手牌)
        this.stats = {
            wins: 0,                     // 获胜次数
            losses: 0,                   // 失败次数
            pushes: 0,                   // 平局次数(和庄家点数相同)
            blackjacks: 0,               // 黑杰克次数(首两张牌为A+10点牌)
            totalScore: 0,               // 总得分(历史兼容字段，现在主要记录筹码盈亏)
            chipProfit: 0,               // 筹码盈亏(所有游戏的净筹码变化)
            doubles: 0,                  // 加倍操作次数
            splits: 0,                   // 分牌操作次数
            surrenders: 0,               // 投降操作次数
            maxChips: 10000,             // 最高筹码记录(游戏过程中筹码的最高值)
            minChips: 10000              // 最低筹码记录(游戏过程中筹码的最低值)
        };
        this.gameState = 'waiting';      // 玩家游戏状态: waiting(等待下注), playing(游戏进行中), ended(游戏结束)

        // 筹码相关属性
        this.chips = 10000;              // 当前持有的筹码数量，默认初始值为10000
        this.bets = [0];                 // 当前各手牌的实际下注金额(加倍后会变化)
        this.initialBets = [0];          // 各手牌的初始下注金额(记录原始下注，加倍后不变)
        this.betStatus = 'waiting';      // 下注状态: waiting(待下注), confirmed(已确认下注)
    }

    /**
     * 重置玩家状态
     * 清空手牌和下注，恢复为等待状态，但保留当前筹码数量
     * 用于开始新一局游戏时重置玩家状态
     */
    reset() {
        // 保存当前筹码数量
        const currentChips = this.chips;

        // 重置手牌和游戏状态
        this.hands = [[]];
        this.currentHandIndex = 0;
        this.gameState = 'waiting';

        // 重置下注状态
        this.bets = [0];
        this.initialBets = [0];
        this.betStatus = 'waiting';

        // 确保筹码数量不变
        this.chips = currentChips;

        playerLogger.debug(`[reset] 玩家${this.name}状态已重置，当前筹码:${this.chips}`);
    }

    /**
     * 获取当前操作的手牌
     * 根据currentHandIndex获取当前活动的手牌(分牌后会有多个手牌)
     * @returns {Array<Card>} 当前手牌的卡牌数组
     */
    getCurrentHand() {
        return this.hands[this.currentHandIndex];
    }

    /**
     * 获取当前手牌的下注金额
     * 根据currentHandIndex获取当前活动手牌的下注金额
     * @returns {number} 当前手牌的下注金额，如果不存在则返回0
     */
    getCurrentBet() {
        return this.bets[this.currentHandIndex] || 0;
    }

    /**
     * 获取指定手牌的初始下注金额
     * 返回下注时的原始金额，不包括加倍后增加的部分
     * @param {number|null} handIndex - 手牌索引，如果为null则使用当前活动手牌
     * @returns {number} 初始下注金额，如果不存在则返回0
     */
    getInitialBet(handIndex = null) {
        if (handIndex === null) {
            handIndex = this.currentHandIndex;
        }
        return this.initialBets[handIndex] || 0;
    }

    /**
     * 检查当前手牌是否可以分牌
     * 判断条件：手牌必须只有两张牌，且两张牌的点数相同
     * 特殊情况：对A按面值判断而非点数(A为11点但视为相同牌)
     * @returns {boolean} 当前手牌是否可以分牌
     */
    canSplit() {
        const currentHand = this.getCurrentHand();

        // 确保手牌存在且有两张牌
        if (!currentHand || currentHand.length !== 2 || !currentHand[0] || !currentHand[1]) {
            playerLogger.debug('分牌检查失败: 手牌不存在或不是两张牌');
            return false;
        }

        // 获取两张牌的点数
        const value1 = currentHand[0].getValue();
        const value2 = currentHand[1].getValue();

        // 获取两张牌的牌面
        const rank1 = currentHand[0].rank;
        const rank2 = currentHand[1].rank;

        // 处理AA特殊情况 - 即使A在计算点数时为11，在分牌判断时也应该视为相同牌
        if (rank1 === 'A' && rank2 === 'A') {
            playerLogger.debug('分牌检查: AA可以分牌');
            return true;
        }

        // 常规情况 - 两张牌点数相同
        const result = value1 === value2;
        playerLogger.debug(`分牌检查: ${rank1}${rank2} ${result ? '可以' : '不可以'}分牌，点数 ${value1}=${value2}`);
        return result;
    }

    /**
     * 更新玩家统计信息
     * 根据游戏结果更新胜负次数统计
     * @param {string} result - 游戏结果: 'win'(胜利), 'lose'(失败), 'push'(平局)
     */
    updateStats(result) {
        if (result === 'win') {
            this.stats.wins++;
        } else if (result === 'lose') {
            this.stats.losses++;
        }
    }

    /**
     * 计算并获取玩家当前胜率
     * 胜率 = 获胜次数 / (获胜次数 + 失败次数) * 100%
     * @returns {string} 格式化后的胜率百分比，保留一位小数
     */
    getWinRate() {
        const total = this.stats.wins + this.stats.losses;
        return total > 0 ? ((this.stats.wins / total) * 100).toFixed(1) : '0.0';
    }

    /**
     * 重置玩家所有统计数据
     * 清空胜负记录、筹码盈亏和操作统计
     * 通常在开始新游戏或玩家要求重置统计时使用
     */
    resetStats() {
        this.stats = {
            wins: 0,
            losses: 0,
            pushes: 0,
            blackjacks: 0,
            totalScore: 0,
            chipProfit: 0,
            doubles: 0,
            doubleWins: 0,
            splits: 0,
            surrenders: 0,
            maxChips: 10000,
            minChips: 10000,
            // 确保重置连胜连败相关字段
            longestWinStreak: 0,
            longestLoseStreak: 0,
            currentWinStreak: 0,
            currentLoseStreak: 0,
            largestWin: 0,
            largestLoss: 0
        };

        // 清除可能存在的总下注金额字段
        if (this.totalBetAmount !== undefined) {
            this.totalBetAmount = 0;
            playerLogger.debug(`[resetStats] 重置玩家${this.name}的总下注金额`);
        }

        playerLogger.debug(`[resetStats] 玩家${this.name}的统计数据已完全重置`);
    }

    /**
     * 为玩家添加筹码
     * 增加玩家当前的筹码余额，用于补充筹码或奖励
     * @param {number} amount - 要添加的筹码数量，必须为正数
     * @returns {boolean} 是否添加成功
     */
    addChips(amount) {
        if (amount <= 0) return false;
        this.chips += amount;
        return true;
    }

    /**
     * 玩家下注操作
     * 从玩家筹码中扣除下注金额，并记录到对应手牌的下注中
     * @param {number} amount - 下注金额，必须为正数且不超过玩家当前筹码
     * @param {number} handIndex - 手牌索引，默认为0(第一手牌)
     * @returns {boolean} 是否下注成功
     */
    placeBet(amount, handIndex = 0) {
        // 确保下注金额为有效数字
        amount = parseInt(amount);
        if (isNaN(amount) || amount <= 0) return false;

        // 检查是否有足够的筹码
        if (amount > this.chips) return false;

        // 设置下注金额
        if (this.bets.length <= handIndex) {
            this.bets[handIndex] = 0;
            this.initialBets[handIndex] = 0;
        }

        // 记录当前下注状态
        playerLogger.debug(`[placeBet] 玩家${this.name}下注前 - 筹码:${this.chips}, 当前下注:${this.bets[handIndex]}`);

        // 从玩家筹码中扣除下注金额
        this.chips -= amount;

        // 使用赋值而不是累加，避免重复扣除
        this.bets[handIndex] = amount;
        // 更新初始下注金额
        this.initialBets[handIndex] = amount;

        playerLogger.debug(`[placeBet] 玩家${this.name}下注后 - 筹码:${this.chips}, 当前下注:${this.bets[handIndex]}`);

        return true;
    }

    /**
     * 确认玩家下注
     * 将下注状态从'waiting'改为'confirmed'，表示玩家已完成下注
     * 只有确认下注后才能开始游戏
     * @returns {boolean} 是否确认成功，如果没有有效下注则返回false
     */
    confirmBet() {
        if (this.bets[0] <= 0) return false;
        this.betStatus = 'confirmed';
        return true;
    }

    /**
     * 清除指定手牌的下注，并返还筹码
     * 将已下注的筹码退还给玩家，并清零下注金额
     * @param {number} handIndex - 手牌索引，默认为0(第一手牌)
     * @returns {boolean} 是否清除成功
     */
    clearBet(handIndex = 0) {
        if (handIndex >= this.bets.length) return false;
        this.chips += this.bets[handIndex];
        this.bets[handIndex] = 0;
        this.initialBets[handIndex] = 0;
        return true;
    }

    /**
     * 加倍下注
     * 用于执行21点中的"加倍"(Double Down)操作
     * 从玩家筹码中再扣除一次当前下注金额，使总下注翻倍
     * 并标记该手牌已加倍，之后只能再摸一张牌
     * @param {number|null} handIndex - 手牌索引，如果为null则使用当前手牌
     * @returns {boolean} 是否加倍成功
     */
    doubleBet(handIndex = null) {
        // 如果没有指定手牌索引，使用当前手牌索引
        if (handIndex === null) {
            handIndex = this.currentHandIndex;
        }

        // 检查是否有足够的筹码
        const currentBet = this.bets[handIndex] || 0;
        if (currentBet <= 0 || currentBet > this.chips) return false;

        // 加倍下注 - initialBets不变，保持原始下注记录
        this.chips -= currentBet;
        this.bets[handIndex] += currentBet;
        // 标记手牌为已加倍（如果手牌不存在则创建）
        if (!this.hands[handIndex]) {
            this.hands[handIndex] = [];
        }
        this.hands[handIndex].doubled = true;
        return true;
    }

    /**
     * 分牌时为新手牌设置下注
     * 在执行分牌操作时，为新生成的手牌设置与原手牌相同的下注金额
     * 从玩家筹码中扣除与当前手牌相同的下注金额
     * @returns {boolean} 是否成功为分牌设置下注
     */
    splitBet() {
        const currentBet = this.bets[this.currentHandIndex];
        if (currentBet <= 0 || currentBet > this.chips) return false;

        // 为新手牌设置相同的下注金额
        this.chips -= currentBet;
        this.bets.push(currentBet);
        // 同时为新手牌设置相同的初始下注金额
        this.initialBets.push(currentBet);
        return true;
    }

    /**
     * 获取玩家当前筹码余额
     * @returns {number} 当前持有的筹码数量
     */
    getChips() {
        return this.chips;
    }

    /**
     * 获取玩家所有手牌的下注总额
     * 计算并返回所有手牌的下注总和
     * @returns {number} 所有手牌的下注总额
     */
    getTotalBets() {
        return this.bets.reduce((sum, bet) => sum + bet, 0);
    }

    /**
     * 返还筹码 - 根据游戏结果结算并返还筹码
     * 根据不同的结算倍数计算返还金额，并更新玩家的筹码余额
     * @param {number|null} handIndex - 手牌索引，如果为null则返还所有手牌的下注
     * @param {number} multiplier - 返还倍数:
     *   - 0: 输牌，不返还筹码
     *   - 1: 平局，返还原下注额
     *   - 2: 普通赢牌，返还2倍下注(本金+赢得等额筹码)
     *   - 2.5: 黑杰克，返还2.5倍下注(本金+赢得1.5倍筹码)
     * @returns {number} 返还的总金额(包括本金)
     */
    returnBet(handIndex = null, multiplier = 1) {
        if (handIndex === null) {
            // 返还所有手牌的下注
            let totalReturn = 0;
            for (let i = 0; i < this.bets.length; i++) {
                // 如果multiplier为0（输牌），直接标记下注为0并返回0
                if (multiplier <= 0) {
                    this.bets[i] = 0;
                    continue;
                }

                // 否则计算返还金额并加到玩家筹码中
                const returnAmount = this.bets[i] * multiplier;
                this.chips += returnAmount;
                this.bets[i] = 0;
                totalReturn += returnAmount;
            }
            return totalReturn;
        } else {
            // 返还指定手牌的下注
            if (handIndex >= this.bets.length) return 0;

            // 如果multiplier为0（输牌），直接标记下注为0并返回0
            if (multiplier <= 0) {
                this.bets[handIndex] = 0;
                return 0;
            }

            // 否则计算返还金额并加到玩家筹码中
            const returnAmount = this.bets[handIndex] * multiplier;
            this.chips += returnAmount;
            this.bets[handIndex] = 0;
            return returnAmount;
        }
    }

    /**
     * 检查玩家是否破产（筹码为0或负数）
     * 用于判断玩家是否还能继续游戏
     * @returns {boolean} 是否破产
     */
    isBankrupt() {
        return this.chips <= 0;
    }
}