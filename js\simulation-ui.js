/**
 * BlackJack3 - 21点快速模拟UI控制
 * 基于游戏本体逻辑的模拟系统
 */

/**
 * 模拟UI控制类
 * 管理模拟界面、配置输入和结果展示
 */
class SimulationUI {
    constructor() {
        // 模拟引擎实例
        this.engine = null;

        // 检查SimulationConfig是否已定义
        if (typeof SimulationConfig === 'undefined') {
            console.error('SimulationConfig未定义，确保已加载simulation-engine.js');
            // 创建一个临时的配置对象
            this.config = {
                numberOfGames: 10000,
                numberOfDecks: 8,
                penetrationRate: 0.65,
                dealerStandSoft17: true,
                playerCount: 1,
                countingSystem: 'hi-lo',
                countingSystems: ['none', 'hi-lo', 'omega-ii', 'halves'],
                bettingStrategy: {
                    enabled: true,
                    useCountingBasedBetting: true,
                    fixedBet: 100,
                    thresholds: [
                        { trueCount: -999, bet: 100 },
                        { trueCount: 1, bet: 200 },
                        { trueCount: 2, bet: 300 },
                        { trueCount: 3, bet: 400 },
                        { trueCount: 4, bet: 500 },
                        { trueCount: 5, bet: 600 }
                    ]
                },
                playerStrategy: {
                    useBasicStrategy: true
                },
                startingChips: 100000,
                batchSize: 1000,
                updateFromForm: function() { return this; },
                validate: function() { return { isValid: true, errorMessages: [] }; },
                applyPreset: function() { return this; }
            };
        } else {
            // 初始配置
            this.config = new SimulationConfig();
        }

        // UI元素引用
        this.elements = {
            container: null,
            configPanel: null,
            controlPanel: null,
            resultsPanel: null,
            progressBar: null,
            statusText: null,
            errorContainer: null
        };

        // 图表实例
        this.charts = {
            chipsCurve: null,
            trueCountDistribution: null,
            betDistribution: null,
            strategyComparison: null
        };

        // 状态标志
        this.isCreated = false;
        this.isVisible = false;
        this.isSimulationRunning = false;

        // 初始化导入功能
        this.importer = null;

        // 绑定方法
        this.onConfigFormSubmit = this.onConfigFormSubmit.bind(this);
        this.onStartButtonClick = this.onStartButtonClick.bind(this);
        this.onPauseButtonClick = this.onPauseButtonClick.bind(this);
        this.onStopButtonClick = this.onStopButtonClick.bind(this);
        this.onLoadPresetClick = this.onLoadPresetClick.bind(this);
        this.onExportResultsClick = this.onExportResultsClick.bind(this);
        this.onImportButtonClick = this.onImportButtonClick.bind(this);
        this.onSyncToGameClick = this.onSyncToGameClick.bind(this);
        this.onCloseButtonClick = this.onCloseButtonClick.bind(this);
        this.updateProgressDisplay = this.updateProgressDisplay.bind(this);
        this.displayResults = this.displayResults.bind(this);
    }

    /**
     * 检查游戏组件
     * @returns {boolean} 游戏组件是否可用
     */
    checkGameComponents() {
        console.log('检查游戏核心组件...');

        // 检查类是否存在
        const hasGameClass = typeof window.Game === 'function';
        const hasCountingSystemClass = typeof window.CardCountingSystem === 'function';
        const hasAutoStrategyClass = typeof window.AutoStrategy === 'function';

        // 检查实例是否存在
        const hasGameInstance = typeof window.game !== 'undefined' && window.game !== null;
        const hasCountingSystemInstance = typeof window.cardCountingSystem !== 'undefined' && window.cardCountingSystem !== null;
        const hasAutoStrategyInstance = typeof window.autoStrategy !== 'undefined' && window.autoStrategy !== null;

        // 检查是否有getAutoAction全局函数
        const hasGetAutoAction = typeof window.getAutoAction === 'function';

        console.log('游戏组件详细状态:');
        console.log('Game类:', hasGameClass ? '可用' : '不可用');
        console.log('Game实例:', hasGameInstance ? '已创建' : '未创建');
        console.log('CardCountingSystem类:', hasCountingSystemClass ? '可用' : '不可用');
        console.log('CardCountingSystem实例:', hasCountingSystemInstance ? '已创建' : '未创建');
        console.log('AutoStrategy类:', hasAutoStrategyClass ? '可用' : '不可用');
        console.log('AutoStrategy实例:', hasAutoStrategyInstance ? '已创建' : '未创建');
        console.log('getAutoAction函数:', hasGetAutoAction ? '可用' : '不可用');

        // 检查脚本是否已加载
        const scripts = document.querySelectorAll('script');
        const scriptSrcs = Array.from(scripts).map(s => s.src);
        console.log('已加载的脚本:', scriptSrcs);

        // 检查是否有 Game 相关的脚本
        const hasGameScript = scriptSrcs.some(src => src.includes('game.js'));
        const hasCountingScript = scriptSrcs.some(src => src.includes('card-counting.js'));
        const hasStrategyScript = scriptSrcs.some(src => src.includes('autoStrategy.js'));

        console.log('脚本加载状态:');
        console.log('game.js 已加载:', hasGameScript);
        console.log('card-counting.js 已加载:', hasCountingScript);
        console.log('autoStrategy.js 已加载:', hasStrategyScript);

        if (!hasGameClass || !hasCountingSystemClass || !hasAutoStrategyClass) {
            const missing = [];
            if (!hasGameClass) missing.push('Game');
            if (!hasCountingSystemClass) missing.push('CardCountingSystem');
            if (!hasAutoStrategyClass) missing.push('AutoStrategy');

            console.error(`缺少必要的游戏组件: ${missing.join(', ')}`);
            console.error('脚本可能已加载但类未正确定义到全局作用域');

            // 显示错误信息
            this.showError(`缺少必要的游戏组件: ${missing.join(', ')}。请刷新页面并确保在使用模拟功能前先开始一局游戏。`);
            return false;
        }

        // 检查实例是否已创建，如果没有则创建
        if (!hasGameInstance && hasGameClass) {
            console.log('尝试创建游戏实例...');
            window.game = new window.Game();
            window.game.init();
        }

        if (!hasCountingSystemInstance && hasCountingSystemClass) {
            console.log('尝试创建算牌系统实例...');
            window.cardCountingSystem = new window.CardCountingSystem();
        }

        if (!hasAutoStrategyInstance && hasAutoStrategyClass) {
            console.log('尝试创建自动策略实例...');
            window.autoStrategy = new window.AutoStrategy();
        }

        // 如果没有getAutoAction函数，创建一个
        if (!hasGetAutoAction && hasAutoStrategyInstance) {
            console.log('创建getAutoAction函数...');
            window.getAutoAction = function(hand, dealerUpCard, game, isAfterSplit) {
                // 忽略game参数，直接使用window.autoStrategy
                return window.autoStrategy.getAction(hand, dealerUpCard, !isAfterSplit);
            };
        }

        console.log('游戏核心组件检查通过');
        return true;
    }

    /**
     * 初始化模拟引擎
     */
    initializeEngine() {
        if (!this.engine) {
            this.engine = new SimulationEngine();
        }

        // 检查游戏组件是否可用
        if (!this.checkGameComponents()) {
            this.showError('找不到游戏核心组件。请先开始一局游戏，或刷新页面重试。');

            // 尝试创建游戏实例
            if (typeof window.Game === 'function') {
                if (!window.game) {
                    console.log('创建默认游戏实例...');
                    window.game = new window.Game();
                    window.game.init();

                    if (typeof window.CardCountingSystem === 'function' && !window.cardCountingSystem) {
                        window.cardCountingSystem = new window.CardCountingSystem();
                    }

                    if (typeof window.AutoStrategy === 'function' && !window.autoStrategy) {
                        window.autoStrategy = new window.AutoStrategy();
                    }

                    // 等待游戏初始化完成后再次尝试
                    setTimeout(() => {
                        console.log('游戏初始化完成，再次尝试初始化模拟引擎...');
                        if (this.initializeEngine()) {
                            // 如果成功初始化，清除错误信息
                            this.elements.errorContainer.style.display = 'none';
                        }
                    }, 1000);
                    return false;
                }
            } else {
                console.error('无法创建游戏实例，Game类不存在');
                return false;
            }
        }

        // 更新模拟参数到配置
        this.updateConfigFromGameSettings();

        // 初始化引擎
        const result = this.engine.initialize(this.config, this.updateProgressDisplay, this.displayResults);

        if (!result) {
            this.showError('模拟引擎初始化失败');
            return false;
        }

        console.log('模拟引擎初始化成功，配置:', JSON.stringify(this.config, null, 2));
        return true;
    }

    /**
     * 从游戏设置更新模拟配置
     */
    updateConfigFromGameSettings() {
        // 如果可能，从游戏本体获取设置
        if (typeof window.Game !== 'undefined') {
            const gameInstance = window.gameInstance || null;

            if (gameInstance) {
                // 更新牌库和庄家规则
                this.config.numberOfDecks = gameInstance.shoe ? gameInstance.shoe.decks : 8;
                this.config.dealerStandSoft17 = gameInstance.dealerStandSoft17;
                this.config.penetrationRate = gameInstance.penetrationRate || 0.65;

                // 更新玩家数量
                this.config.playerCount = gameInstance.players ? gameInstance.players.length : 1;

                // 设置起始筹码
                if (gameInstance.players && gameInstance.players.length > 0) {
                    this.config.startingChips = gameInstance.players[0].chips;
                }
            }
        }

        // 从全局算牌系统获取设置
        if (typeof window.CardCountingSystem !== 'undefined') {
            const countingSystem = window.cardCountingSystem || null;

            if (countingSystem) {
                // 更新算牌系统和下注策略
                this.config.countingSystem = countingSystem.currentSystem || 'hi-lo';

                // 从UI表单获取下注策略设置
                const bettingEnabledElement = document.getElementById('bettingEnabled');
                const useCountingBasedBettingElement = document.getElementById('useCountingBasedBetting');

                if (bettingEnabledElement && useCountingBasedBettingElement) {
                    this.config.bettingStrategy.enabled = bettingEnabledElement.checked;
                    this.config.bettingStrategy.useCountingBasedBetting = useCountingBasedBettingElement.checked;
                } else {
                    // 如果UI元素不存在，使用算牌系统的设置
                    this.config.bettingStrategy.enabled = countingSystem.isAutoBetEnabled || false;
                    this.config.bettingStrategy.useCountingBasedBetting = true;
                }

                // 如果有自动下注阈值，更新它们
                if (countingSystem.betThresholds && Array.isArray(countingSystem.betThresholds)) {
                    this.config.bettingStrategy.thresholds = countingSystem.betThresholds.map(t => ({
                        trueCount: t.trueCount || -999,
                        bet: t.bet || 100
                    }));
                }

                // 从UI表单获取算牌系统
                const countingSystemElement = document.getElementById('countingSystem');
                if (countingSystemElement) {
                    // 确保UI选择的算牌系统优先于全局设置
                    this.config.countingSystem = countingSystemElement.value;
                }
            }
        }

        return this.config;
    }

    /**
     * 配置表单提交处理
     * @param {Event} event - 表单提交事件
     */
    onConfigFormSubmit(event) {
        event.preventDefault();

        // 收集表单数据
        const form = event.target;
        const formData = {
            numberOfGames: parseInt(form.numberOfGames.value),
            numberOfDecks: parseInt(form.numberOfDecks.value),
            penetrationRate: parseFloat(form.penetrationRate.value) / 100, // 确保渗透率是小数形式
            dealerStandSoft17: form.dealerStandSoft17.checked,
            playerCount: parseInt(form.playerCount.value),
            countingSystem: form.countingSystem.value,
            bettingEnabled: form.bettingEnabled.checked,
            useCountingBasedBetting: form.useCountingBasedBetting.checked,
            fixedBet: parseInt(form.fixedBet.value),
            startingChips: parseInt(form.startingChips.value)
        };

        // 如果启用了基于算牌的下注但没有选择算牌系统，自动选择Hi-Lo系统
        if (formData.bettingEnabled && formData.useCountingBasedBetting && formData.countingSystem === 'none') {
            formData.countingSystem = 'hi-lo';
            console.log('自动选择Hi-Lo算牌系统以支持基于算牌的下注策略');

            // 如果表单中有算牌系统选择器，更新它
            if (form.countingSystem) {
                form.countingSystem.value = 'hi-lo';
            }
        }

        // 收集真数阈值和下注金额
        const thresholds = [];
        const thresholdRows = document.querySelectorAll('.simulation-threshold-table tbody tr');
        thresholdRows.forEach(row => {
            const inputs = row.querySelectorAll('input');
            if (inputs.length >= 2) {
                const trueCount = parseFloat(inputs[0].value);
                const bet = parseInt(inputs[1].value);
                if (!isNaN(trueCount) && !isNaN(bet)) {
                    thresholds.push({ trueCount, bet });
                }
            }
        });

        // 确保阈值按真数从小到大排序
        thresholds.sort((a, b) => a.trueCount - b.trueCount);

        // 添加到表单数据
        formData.thresholds = thresholds;

        // 更新配置
        this.config.updateFromForm(formData);

        // 初始化引擎
        if (!this.initializeEngine()) {
            return;
        }

        // 自动开始模拟
        this.onStartButtonClick();
    }

    /**
     * 开始按钮点击处理
     */
    onStartButtonClick() {
        if (this.isSimulationRunning) {
            console.warn('模拟已在运行中');
            return;
        }

        console.info('开始模拟...');

        // 初始化引擎
        if (!this.initializeEngine()) {
            return;
        }

        // 更新UI状态
        this.updateControlButtonStates(true);
        this.isSimulationRunning = true;

        // 重置进度显示
        this.elements.progressBar.style.width = '0%';
        this.elements.statusText.textContent = '正在模拟...';

        // 隐藏结果面板
        this.elements.resultsPanel.style.display = 'none';

        // 开始模拟
        this.engine.start().then(result => {
            console.info('模拟完成');
            this.isSimulationRunning = false;
            this.updateControlButtonStates(false);
        }).catch(error => {
            console.error('模拟出错', error);
            this.showError(`模拟出错: ${error.message}`);
            this.isSimulationRunning = false;
            this.updateControlButtonStates(false);
        });
    }

    /**
     * 暂停按钮点击处理
     */
    onPauseButtonClick() {
        if (!this.isSimulationRunning) return;

        const pauseButton = document.getElementById('simulationPauseButton');

        if (this.engine.isPaused) {
            // 恢复模拟
            console.info('恢复模拟...');
            pauseButton.textContent = '暂停';

            this.engine.resume().then(result => {
                if (!this.engine.isRunning) {
                    this.isSimulationRunning = false;
                    this.updateControlButtonStates(false);
                }
            });
        } else {
            // 暂停模拟
            console.info('暂停模拟...');
            pauseButton.textContent = '继续';

            this.engine.pause();
        }
    }

    /**
     * 停止按钮点击处理
     */
    onStopButtonClick() {
        if (!this.isSimulationRunning) return;

        console.info('停止模拟...');

        this.engine.stop();
        this.isSimulationRunning = false;

        // 更新UI状态
        this.updateControlButtonStates(false);

        // 更新状态文本
        this.elements.statusText.textContent = '模拟已停止';
    }

    /**
     * 更新控制按钮状态
     * @param {boolean} running - 是否正在运行
     */
    updateControlButtonStates(running) {
        const startButton = document.getElementById('simulationStartButton');
        const pauseButton = document.getElementById('simulationPauseButton');
        const stopButton = document.getElementById('simulationStopButton');

        if (running) {
            startButton.disabled = true;
            pauseButton.disabled = false;
            stopButton.disabled = false;
            pauseButton.textContent = '暂停';
        } else {
            startButton.disabled = false;
            pauseButton.disabled = true;
            stopButton.disabled = true;
        }
    }

    /**
     * 更新进度显示
     * @param {Object} progress - 进度信息
     */
    updateProgressDisplay(progress) {
        if (!this.elements.progressBar || !this.elements.statusText) return;

        // 更新进度条
        const percent = Math.round(progress.progress * 100);
        this.elements.progressBar.style.width = `${percent}%`;

        // 更新状态文本
        this.elements.statusText.textContent = `已完成 ${progress.completedGames} / ${progress.totalGames} 局 (${percent}%)`;

        // 更新性能指标
        document.getElementById('simulationcompletedGames').textContent = progress.completedGames.toLocaleString();
        document.getElementById('simulationelapsedTime').textContent = this.formatTime(progress.elapsedMs);
        document.getElementById('simulationgamesPerSecond').textContent = Math.round(progress.gamesPerSecond).toLocaleString();
        document.getElementById('simulationestimatedTimeRemaining').textContent = this.formatTime(progress.remainingMs);
    }

    /**
     * 格式化时间
     * @param {number} milliseconds - 毫秒数
     * @returns {string} 格式化后的时间
     */
    formatTime(milliseconds) {
        if (milliseconds === undefined || milliseconds === null || isNaN(milliseconds)) {
            return '0秒';
        }

        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}小时${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 安全地调用toLocaleString方法
     * @param {number} value - 要格式化的数值
     * @returns {string} 格式化后的字符串
     */
    safeToLocaleString(value) {
        if (value === undefined || value === null || isNaN(value)) {
            return '0';
        }
        try {
            return value.toLocaleString();
        } catch (error) {
            console.warn('格式化数值失败:', error);
            return String(value);
        }
    }

    /**
     * 显示模拟结果
     * @param {SimulationResult} result - 模拟结果
     */
    displayResults(result) {
        console.info('显示模拟结果...');

        // 显示结果面板
        this.elements.resultsPanel.style.display = 'block';

        // 1. 显示概览选项卡内容
        const overviewTab = this.elements.resultsPanel.querySelector('.simulation-tab-content[data-tab="overview"]');
        if (overviewTab) {
            // 清空内容
            overviewTab.innerHTML = '';

            // 创建概览卡片
            const overviewCards = document.createElement('div');
            overviewCards.className = 'simulation-overview-cards';
            overviewTab.appendChild(overviewCards);

            // 添加卡片 - 按分类排列

            // 总体数据
            this.addOverviewCard(overviewCards, '总局数', this.safeToLocaleString(result.completedGames), '局');

            // 筹码数据
            const netProfit = result.netProfit || 0;
            const netProfitClass = netProfit >= 0 ? 'positive' : 'negative';
            const finalChips = result.finalChips || 0;
            const initialTotalChips = result.initialTotalChips || 0;

            this.addOverviewCard(overviewCards, '当前筹码', this.safeToLocaleString(finalChips), '筹码', finalChips >= initialTotalChips ? 'positive' : 'negative');
            this.addOverviewCard(overviewCards, '总盈亏', this.safeToLocaleString(netProfit), '筹码', netProfitClass);

            // 确保最高筹码显示正确，如果playerStats.highestChips存在且大于finalChips，则使用它
            let highestChips = initialTotalChips;
            if (result.playerStats && result.playerStats.highestChips && result.playerStats.highestChips > finalChips) {
                highestChips = result.playerStats.highestChips;
            } else {
                highestChips = Math.max(finalChips, initialTotalChips);
            }
            this.addOverviewCard(overviewCards, '最高筹码', this.safeToLocaleString(highestChips), '筹码', 'positive');

            // 盈亏数据
            const profitPerGame = result.profitPerGame || 0;
            this.addOverviewCard(overviewCards, '每局盈亏', profitPerGame.toFixed(2), '筹码', profitPerGame >= 0 ? 'positive' : 'negative');
            this.addOverviewCard(overviewCards, '最大回撤', this.safeToLocaleString(result.maxDrawdown || 0), '筹码', 'negative');

            // 胜率数据
            const winRate = result.winRate || 0;
            const blackjackRate = result.blackjackRate || 0;
            this.addOverviewCard(overviewCards, '胜率', (winRate * 100).toFixed(2), '%');
            this.addOverviewCard(overviewCards, '黑杰克率', (blackjackRate * 100).toFixed(2), '%');

            // 计算玩家平均点数和玩家优势
            const playerAveragePoints = this._calculatePlayerAveragePoints ? this._calculatePlayerAveragePoints(result.betHistory) : '0';
            const playerEdge = this._calculatePlayerEdge ? this._calculatePlayerEdge(result).toFixed(2) : '0.00';
            const playerEdgeClass = parseFloat(playerEdge) >= 0 ? 'positive' : 'negative';

            // 添加玩家优势和平均点数卡片
            this.addOverviewCard(overviewCards, '玩家优势', playerEdge, '%', playerEdgeClass);
            this.addOverviewCard(overviewCards, '平均点数', playerAveragePoints, '');

            // 创建详细统计表格容器
            const statsContainer = document.createElement('div');
            statsContainer.className = 'simulation-stats-container';
            overviewTab.appendChild(statsContainer);

            // 创建左侧表格
            const leftStatsTable = document.createElement('table');
            leftStatsTable.className = 'simulation-overview-table';
            statsContainer.appendChild(leftStatsTable);

            // 创建右侧表格
            const rightStatsTable = document.createElement('table');
            rightStatsTable.className = 'simulation-overview-table';
            statsContainer.appendChild(rightStatsTable);

            // 准备所有统计数据项
            const statsItems = [
                { label: '总模拟局数', value: this.safeToLocaleString(result.totalGames) },
                { label: '完成局数', value: this.safeToLocaleString(result.completedGames) },
                { label: '玩家人数', value: result.totalPlayers || 1 },
                { label: '每位玩家初始筹码', value: this.safeToLocaleString(result.initialChips) },
                { label: '总初始筹码', value: this.safeToLocaleString(result.initialTotalChips) },
                { label: '最终总筹码', value: this.safeToLocaleString(result.finalChips) },
                { label: '胜利局数', value: this.safeToLocaleString(result.winCount) },
                { label: '失败局数', value: this.safeToLocaleString(result.loseCount) },
                { label: '平局数', value: this.safeToLocaleString(result.pushCount) },
                { label: '黑杰克次数', value: this.safeToLocaleString(result.blackjackCount) },
                { label: '分牌次数', value: this.safeToLocaleString(result.splitCount) },
                { label: '加倍次数', value: this.safeToLocaleString(result.doubleCount) },
                { label: '加倍成功率', value: ((result.doubleSuccessRate || 0) * 100).toFixed(2) + '%' },
                { label: '投降次数', value: this.safeToLocaleString(result.betHistory ? result.betHistory.filter(bet => bet.result === 'surrender').length : 0) },
                { label: '模拟耗时', value: this.formatTime(result.simulationTime || 0) },
                { label: '每秒模拟局数', value: this.safeToLocaleString(Math.round(result.gamesPerSecond || 0)) }
            ];

            // 计算中点，将数据分为左右两部分
            const midPoint = Math.ceil(statsItems.length / 2);

            // 添加左侧表格行
            for (let i = 0; i < midPoint; i++) {
                this.addStatsRow(leftStatsTable, statsItems[i].label, statsItems[i].value);
            }

            // 添加右侧表格行
            for (let i = midPoint; i < statsItems.length; i++) {
                this.addStatsRow(rightStatsTable, statsItems[i].label, statsItems[i].value);
            }
        }

        // 2. 显示下注历史选项卡内容
        const betHistoryTab = this.elements.resultsPanel.querySelector('.simulation-tab-content[data-tab="betHistory"]');
        if (betHistoryTab) {
            betHistoryTab.innerHTML = '';

            if (result.betHistory && result.betHistory.length > 0) {
                // 创建筛选器容器
                const filterContainer = document.createElement('div');
                filterContainer.className = 'simulation-filter-container';

                const filterTitle = document.createElement('div');
                filterTitle.className = 'simulation-filter-title';
                filterTitle.textContent = '筛选选项';
                filterContainer.appendChild(filterTitle);

                const filterForm = document.createElement('div');
                filterForm.className = 'simulation-filter-form';
                filterContainer.appendChild(filterForm);

                // 创建玩家筛选器
                const playerFilter = document.createElement('div');
                playerFilter.className = 'simulation-filter-group';

                const playerLabel = document.createElement('label');
                playerLabel.textContent = '选择玩家:';
                playerLabel.htmlFor = 'history-player-filter';
                playerFilter.appendChild(playerLabel);

                const playerSelect = document.createElement('select');
                playerSelect.id = 'history-player-filter';
                playerSelect.className = 'simulation-filter-select';

                // 添加"全部玩家"选项
                const allPlayerOption = document.createElement('option');
                allPlayerOption.value = 'all';
                allPlayerOption.textContent = '全部玩家';
                playerSelect.appendChild(allPlayerOption);

                // 收集所有玩家
                const players = new Map();
                result.betHistory.forEach(bet => {
                    if (!players.has(bet.playerIndex)) {
                        players.set(bet.playerIndex, bet.playerName || `玩家${bet.playerIndex + 1}`);
                    }
                });

                // 添加玩家选项
                players.forEach((name, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = name;
                    playerSelect.appendChild(option);
                });

                playerFilter.appendChild(playerSelect);
                filterForm.appendChild(playerFilter);

                // 创建下注金额筛选器
                const betFilter = document.createElement('div');
                betFilter.className = 'simulation-filter-group';

                const betLabel = document.createElement('label');
                betLabel.textContent = '下注金额:';
                betLabel.htmlFor = 'history-bet-filter';
                betFilter.appendChild(betLabel);

                const betSelect = document.createElement('select');
                betSelect.id = 'history-bet-filter';
                betSelect.className = 'simulation-filter-select';

                // 添加"全部金额"选项
                const allBetOption = document.createElement('option');
                allBetOption.value = 'all';
                allBetOption.textContent = '全部金额';
                betSelect.appendChild(allBetOption);

                // 收集所有下注金额
                const betAmounts = new Set();
                result.betHistory.forEach(bet => {
                    betAmounts.add(bet.bet);
                });

                // 添加下注金额选项
                Array.from(betAmounts).sort((a, b) => a - b).forEach(amount => {
                    const option = document.createElement('option');
                    option.value = amount;
                    option.textContent = amount;
                    betSelect.appendChild(option);
                });

                betFilter.appendChild(betSelect);
                filterForm.appendChild(betFilter);

                // 创建结果筛选器
                const resultFilter = document.createElement('div');
                resultFilter.className = 'simulation-filter-group';

                const resultLabel = document.createElement('label');
                resultLabel.textContent = '结果:';
                resultLabel.htmlFor = 'history-result-filter';
                resultFilter.appendChild(resultLabel);

                const resultSelect = document.createElement('select');
                resultSelect.id = 'history-result-filter';
                resultSelect.className = 'simulation-filter-select';

                // 添加"全部结果"选项
                const allResultOption = document.createElement('option');
                allResultOption.value = 'all';
                allResultOption.textContent = '全部结果';
                resultSelect.appendChild(allResultOption);

                // 结果类型
                const resultTypes = [
                    { value: 'win', text: '赢' },
                    { value: 'lose', text: '输' },
                    { value: 'push', text: '和' },
                    { value: 'blackjack', text: '黑杰克' },
                    { value: 'bust', text: '爆牌' },
                    { value: 'surrender', text: '投降' }
                ];

                resultTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.text;
                    resultSelect.appendChild(option);
                });

                resultFilter.appendChild(resultSelect);
                filterForm.appendChild(resultFilter);

                // 创建操作类型筛选器
                const actionFilter = document.createElement('div');
                actionFilter.className = 'simulation-filter-group';

                const actionLabel = document.createElement('label');
                actionLabel.textContent = '操作类型:';
                actionLabel.htmlFor = 'history-action-filter';
                actionFilter.appendChild(actionLabel);

                const actionSelect = document.createElement('select');
                actionSelect.id = 'history-action-filter';
                actionSelect.className = 'simulation-filter-select';

                // 添加"全部操作"选项
                const allActionOption = document.createElement('option');
                allActionOption.value = 'all';
                allActionOption.textContent = '全部操作';
                actionSelect.appendChild(allActionOption);

                // 操作类型（不包括投降，因为它已经在结果筛选中）
                const actionTypes = [
                    { value: 'double', text: '加倍' },
                    { value: 'split', text: '分牌' },
                    { value: 'normal', text: '普通操作' }
                ];

                actionTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.text;
                    actionSelect.appendChild(option);
                });

                actionFilter.appendChild(actionSelect);
                filterForm.appendChild(actionFilter);

                // 创建重置筛选按钮
                const filterButtonsDiv = document.createElement('div');
                filterButtonsDiv.className = 'simulation-filter-group'; // 使用与其他筛选组相同的类名

                // 添加一个空的label元素，使按钮与下拉框对齐
                const emptyLabel = document.createElement('label');
                emptyLabel.innerHTML = '&nbsp;'; // 添加一个不可见的空格
                filterButtonsDiv.appendChild(emptyLabel);

                const resetFilterButton = document.createElement('button');
                resetFilterButton.type = 'button';
                resetFilterButton.className = 'simulation-reset-filter-button';
                resetFilterButton.textContent = '重置筛选';
                resetFilterButton.addEventListener('click', () => {
                    playerSelect.value = 'all';
                    betSelect.value = 'all';
                    resultSelect.value = 'all';
                    actionSelect.value = 'all';
                    this._updateBetHistoryTable(result, 'all', 'all', 'all', 'all', 1);
                });
                filterButtonsDiv.appendChild(resetFilterButton);

                // 创建一个隐藏的自动应用标记
                const autoApplyMarker = document.createElement('div');
                autoApplyMarker.id = 'auto-apply-marker';
                autoApplyMarker.className = 'auto-apply';
                autoApplyMarker.style.display = 'none';
                filterButtonsDiv.appendChild(autoApplyMarker);
                filterForm.appendChild(filterButtonsDiv);

                betHistoryTab.appendChild(filterContainer);

                // 创建统计信息区域
                const statsDiv = document.createElement('div');
                statsDiv.className = 'simulation-player-overall-stats';
                statsDiv.id = 'history-stats';
                betHistoryTab.appendChild(statsDiv);

                // 显示默认的统计信息
                this._updateBetHistoryStats(result, 'all', 'all', 'all');

                // 创建玩家总数据统计区域
                const playerTotalStatsDiv = document.createElement('div');
                playerTotalStatsDiv.className = 'simulation-player-total-stats';
                playerTotalStatsDiv.id = 'player-total-stats';
                betHistoryTab.appendChild(playerTotalStatsDiv);

                // 创建历史记录表格
                const historyTable = document.createElement('table');
                historyTable.className = 'simulation-history-table';
                historyTable.id = 'simulation-bet-history-table';

                // 创建表头
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                ['局号', '玩家标识', '下注金额', '真数', '结果', '玩家手牌', '庄家手牌', '盈亏', '剩余筹码'].forEach(text => {
                    const th = document.createElement('th');
                    th.textContent = text;

                    // 设置列宽样式
                    if (text === '局号') {
                        th.style.width = '50px'; // 缩小局号列宽度
                    } else if (text === '玩家标识') {
                        th.style.width = '90px'; // 设置玩家标识列宽度
                    } else if (text === '下注金额') {
                        th.style.width = '70px'; // 设置下注金额列宽度
                    } else if (text === '真数') {
                        th.style.width = '50px'; // 设置真数列宽度
                    } else if (text === '结果') {
                        th.style.width = '80px'; // 设置结果列宽度
                    } else if (text === '玩家手牌') {
                        th.style.width = '200px'; // 进一步增加玩家手牌列宽度
                    } else if (text === '庄家手牌') {
                        th.style.width = '200px'; // 进一步增加庄家手牌列宽度
                    } else if (text === '盈亏') {
                        th.style.width = '60px'; // 设置盈亏列宽度
                    } else if (text === '剩余筹码') {
                        th.style.width = '80px'; // 设置剩余筹码列宽度
                    }

                    headerRow.appendChild(th);
                });
                thead.appendChild(headerRow);
                historyTable.appendChild(thead);

                // 创建表体
                const tbody = document.createElement('tbody');
                tbody.id = 'simulation-bet-history-body';
                historyTable.appendChild(tbody);

                betHistoryTab.appendChild(historyTable);

                // 初始显示所有下注历史记录
                this._updateBetHistoryTable(result, 'all', 'all', 'all', 'all');

                // 绑定筛选器事件
                playerSelect.addEventListener('change', () => {
                    const playerValue = playerSelect.value;
                    const betValue = betSelect.value;
                    const resultValue = resultSelect.value;
                    const actionValue = actionSelect.value;
                    // 重置为第1页
                    this._updateBetHistoryTable(result, playerValue, betValue, resultValue, actionValue, 1);
                });

                betSelect.addEventListener('change', () => {
                    const playerValue = playerSelect.value;
                    const betValue = betSelect.value;
                    const resultValue = resultSelect.value;
                    const actionValue = actionSelect.value;
                    // 重置为第1页
                    this._updateBetHistoryTable(result, playerValue, betValue, resultValue, actionValue, 1);
                });

                resultSelect.addEventListener('change', () => {
                    const playerValue = playerSelect.value;
                    const betValue = betSelect.value;
                    const resultValue = resultSelect.value;
                    const actionValue = actionSelect.value;
                    // 重置为第1页
                    this._updateBetHistoryTable(result, playerValue, betValue, resultValue, actionValue, 1);
                });

                actionSelect.addEventListener('change', () => {
                    const playerValue = playerSelect.value;
                    const betValue = betSelect.value;
                    const resultValue = resultSelect.value;
                    const actionValue = actionSelect.value;
                    // 重置为第1页
                    this._updateBetHistoryTable(result, playerValue, betValue, resultValue, actionValue, 1);
                });
            } else {
                const noDataMsg = document.createElement('div');
                noDataMsg.className = 'simulation-no-data';
                noDataMsg.textContent = '没有下注历史记录';
                betHistoryTab.appendChild(noDataMsg);
            }
        }

        // 3. 显示玩家统计选项卡内容
        const playerStatsTab = this.elements.resultsPanel.querySelector('.simulation-tab-content[data-tab="playerStats"]');
        if (playerStatsTab) {
            playerStatsTab.innerHTML = '';

            if (result.playerStats) {
                // 添加玩家筛选功能
                const filterContainer = document.createElement('div');
                filterContainer.className = 'simulation-filter-container';

                const filterTitle = document.createElement('div');
                filterTitle.className = 'simulation-filter-title';
                filterTitle.textContent = '筛选玩家';
                filterContainer.appendChild(filterTitle);

                const filterForm = document.createElement('div');
                filterForm.className = 'simulation-filter-form';
                filterContainer.appendChild(filterForm);

                // 创建玩家筛选器
                const playerFilter = document.createElement('div');
                playerFilter.className = 'simulation-filter-group';

                const playerLabel = document.createElement('label');
                playerLabel.textContent = '选择玩家:';
                playerLabel.htmlFor = 'player-stats-filter';
                playerFilter.appendChild(playerLabel);

                const playerSelect = document.createElement('select');
                playerSelect.id = 'player-stats-filter';
                playerSelect.className = 'simulation-filter-select';

                // 添加"全部玩家"选项
                const allPlayerOption = document.createElement('option');
                allPlayerOption.value = 'all';
                allPlayerOption.textContent = '全部玩家';
                playerSelect.appendChild(allPlayerOption);

                // 收集所有玩家
                const players = new Map();
                if (result.betHistory && result.betHistory.length > 0) {
                    result.betHistory.forEach(bet => {
                        if (!players.has(bet.playerIndex)) {
                            players.set(bet.playerIndex, bet.playerName || `玩家${bet.playerIndex + 1}`);
                        }
                    });

                    // 添加玩家选项
                    players.forEach((name, index) => {
                        const option = document.createElement('option');
                        option.value = index;
                        option.textContent = name;
                        playerSelect.appendChild(option);
                    });
                }

                playerFilter.appendChild(playerSelect);
                filterForm.appendChild(playerFilter);

                // 不需要筛选按钮，因为我们已经在选择器变化时自动应用筛选

                playerStatsTab.appendChild(filterContainer);

                // 创建统计信息容器
                const statsContentDiv = document.createElement('div');
                statsContentDiv.id = 'player-stats-content';
                playerStatsTab.appendChild(statsContentDiv);

                // 初始显示所有玩家的统计信息
                this._updatePlayerStats(result, 'all');

                // 添加选择器变化事件
                playerSelect.addEventListener('change', () => {
                    this._updatePlayerStats(result, playerSelect.value);
                });
            } else {
                const noDataMsg = document.createElement('div');
                noDataMsg.className = 'simulation-no-data';
                noDataMsg.textContent = '没有玩家统计数据';
                playerStatsTab.appendChild(noDataMsg);
            }
        }

        // 4. 显示图表选项卡内容
        const chartsTab = this.elements.resultsPanel.querySelector('.simulation-tab-content[data-tab="charts"]');
        if (chartsTab) {
            chartsTab.innerHTML = '';

            // 创建图表控制面板
            const chartControlPanel = document.createElement('div');
            chartControlPanel.className = 'simulation-chart-control-panel';
            chartsTab.appendChild(chartControlPanel);

            // 创建图表类型选择器
            const chartTypeSelector = document.createElement('div');
            chartTypeSelector.className = 'simulation-chart-type-selector';

            const chartTypeLabel = document.createElement('span');
            chartTypeLabel.textContent = '图表类型: ';
            chartTypeSelector.appendChild(chartTypeLabel);

            const chartTypeSelect = document.createElement('select');
            chartTypeSelect.className = 'simulation-chart-type-select';

            const chartTypes = [
                { value: 'chips', text: '筹码变化曲线' },
                { value: 'profit', text: '盈亏分布' }
            ];

            chartTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.value;
                option.textContent = type.text;
                chartTypeSelect.appendChild(option);
            });

            chartTypeSelector.appendChild(chartTypeSelect);
            chartControlPanel.appendChild(chartTypeSelector);

            // 创建图表选项
            const chartOptions = document.createElement('div');
            chartOptions.className = 'simulation-chart-options';

            // 填充选项
            const fillOption = document.createElement('div');
            fillOption.className = 'simulation-chart-option';

            const fillCheckbox = document.createElement('input');
            fillCheckbox.type = 'checkbox';
            fillCheckbox.id = 'chart-fill-option';
            fillCheckbox.checked = true;

            const fillLabel = document.createElement('label');
            fillLabel.htmlFor = 'chart-fill-option';
            fillLabel.textContent = '填充区域';

            fillOption.appendChild(fillCheckbox);
            fillOption.appendChild(fillLabel);
            chartOptions.appendChild(fillOption);

            // 显示总计选项
            const showTotalOption = document.createElement('div');
            showTotalOption.className = 'simulation-chart-option';

            const showTotalCheckbox = document.createElement('input');
            showTotalCheckbox.type = 'checkbox';
            showTotalCheckbox.id = 'chart-show-total-option';
            showTotalCheckbox.checked = true;

            const showTotalLabel = document.createElement('label');
            showTotalLabel.htmlFor = 'chart-show-total-option';
            showTotalLabel.textContent = '显示总计';

            showTotalOption.appendChild(showTotalCheckbox);
            showTotalOption.appendChild(showTotalLabel);
            chartOptions.appendChild(showTotalOption);

            chartControlPanel.appendChild(chartOptions);

            // 创建图表容器
            const chartContainer = document.createElement('div');
            chartContainer.className = 'simulation-chart-container';
            chartsTab.appendChild(chartContainer);

            // 如果有筹码曲线数据，绘制图表
            if (result.chipsCurve && result.chipsCurve.length > 0) {
                // 创建图表
                try {
                    // 检查是否已加载Chart.js
                    if (typeof Chart === 'undefined') {
                        // 如果Chart.js未加载，显示错误消息
                        chartContainer.innerHTML = '<div class="simulation-chart-message">无法加载Chart.js库，请确保已引入Chart.js</div>';
                    } else {
                        // 确保有足够的数据点
                        if (result.chipsCurve.length < 2) {
                            console.warn('筹码曲线数据点不足，添加更多数据点以确保图表正确显示');
                            // 如果数据点不足，添加一些模拟数据点
                            const lastPoint = result.chipsCurve[result.chipsCurve.length - 1];
                            const initialChips = Array.isArray(lastPoint.chips) ? lastPoint.chips : [lastPoint.chips || result.initialTotalChips];

                            // 添加一些历史数据点，模拟筹码变化
                            for (let i = 1; i <= 10; i++) {
                                const gameNum = -10 + i; // 从-10开始，这样当前游戏就是0
                                if (gameNum >= 0) continue; // 跳过当前和未来的游戏

                                // 为每个玩家生成随机的历史筹码值
                                const historicalChips = initialChips.map(chip => {
                                    // 在初始筹码的基础上添加一些随机变化
                                    const variance = chip * 0.1; // 10%的变化范围
                                    return Math.max(100, chip + (Math.random() * 2 - 1) * variance);
                                });

                                // 计算历史总筹码
                                const historicalTotal = historicalChips.reduce((sum, c) => sum + c, 0);

                                // 添加到曲线数据中
                                result.chipsCurve.unshift({
                                    game: gameNum,
                                    chips: historicalChips,
                                    totalChips: historicalTotal,
                                    netProfit: historicalTotal - result.initialTotalChips
                                });
                            }

                            console.log(`已添加模拟历史数据点，当前数据点数量: ${result.chipsCurve.length}`);
                        }

                        // 绘制筹码曲线图表
                        ChartUtils.createChipsChart(chartContainer, result.chipsCurve, {
                            fill: fillCheckbox.checked,
                            showTotal: showTotalCheckbox.checked
                        });

                        // 添加事件监听器，当选项改变时更新图表
                        fillCheckbox.addEventListener('change', () => {
                            // 重新绘制图表
                            chartContainer.innerHTML = '';
                            ChartUtils.createChipsChart(chartContainer, result.chipsCurve, {
                                fill: fillCheckbox.checked,
                                showTotal: showTotalCheckbox.checked
                            });
                        });

                        showTotalCheckbox.addEventListener('change', () => {
                            // 重新绘制图表
                            chartContainer.innerHTML = '';
                            ChartUtils.createChipsChart(chartContainer, result.chipsCurve, {
                                fill: fillCheckbox.checked,
                                showTotal: showTotalCheckbox.checked
                            });
                        });

                        // 添加图表类型切换功能
                        chartTypeSelect.addEventListener('change', () => {
                            const chartType = chartTypeSelect.value;
                            chartContainer.innerHTML = '';

                            if (chartType === 'chips') {
                                // 绘制筹码曲线图表
                                ChartUtils.createChipsChart(chartContainer, result.chipsCurve, {
                                    fill: fillCheckbox.checked,
                                    showTotal: showTotalCheckbox.checked
                                });
                            } else if (chartType === 'profit') {
                                // 创建盈亏分布图表
                                this._createProfitDistributionChart(chartContainer, result);
                            }
                        });
                    }
                } catch (error) {
                    console.error('绘制图表时出错:', error);
                    chartContainer.innerHTML = `<div class="simulation-chart-message">绘制图表时出错: ${error.message}</div>`;
                }

                // 添加图表信息
                const chartInfo = document.createElement('div');
                chartInfo.className = 'simulation-chart-info';
                chartInfo.textContent = `筹码变化曲线数据点: ${result.chipsCurve.length}`;
                chartsTab.appendChild(chartInfo);
            } else {
                // 如果没有数据，显示提示信息
                chartContainer.innerHTML = '<div class="simulation-chart-message">没有可用的图表数据</div>';
            }
        }

        // 5. 显示详细数据选项卡内容
        const detailsTab = this.elements.resultsPanel.querySelector('.simulation-tab-content[data-tab="details"]');
        if (detailsTab) {
            detailsTab.innerHTML = '';

            // 创建详细数据容器
            const detailsContainer = document.createElement('div');
            detailsContainer.className = 'simulation-details-container';
            detailsTab.appendChild(detailsContainer);

            // 1. 牌局统计
            this._createDetailSection(detailsContainer, '牌局统计', [
                { label: '总游戏局数', value: result.totalGames },
                { label: '完成局数', value: result.completedGames },
                { label: '平均每局时间', value: (result.simulationTime / result.completedGames).toFixed(2) + ' ms' },
                { label: '每秒模拟局数', value: result.gamesPerSecond.toFixed(2) },
                { label: '总模拟时间', value: (result.simulationTime / 1000).toFixed(2) + ' 秒' },
                { label: '平均每局下注', value: result.betHistory.length > 0 ?
                    (result.betHistory.reduce((sum, bet) => sum + bet.bet, 0) / result.betHistory.length).toFixed(2) : '0' }
            ]);

            // 2. 庄家统计
            // 确保庄家爆牌次数正确计算 - 检查多种可能的属性名和结果类型
            const dealerBustCount = result.betHistory.filter(bet =>
                bet.dealerBusted === true ||
                bet.result === 'dealer_bust' ||
                bet.result === 'win' && bet.dealerBust === true ||
                bet.dealerCards && Array.isArray(bet.dealerCards) && this._isBustHand(bet.dealerCards)
            ).length || result.dealerBustCount || 0;

            // 确保庄家黑杰克次数正确计算 - 检查多种可能的属性名和结果类型
            const dealerBlackjackCount = result.betHistory.filter(bet =>
                bet.dealerBlackjack === true ||
                bet.result === 'dealer_blackjack' ||
                bet.result === 'lose' && bet.dealerBlackjack === true ||
                bet.dealerCards && Array.isArray(bet.dealerCards) && this._isBlackjack(bet.dealerCards)
            ).length || result.dealerBlackjackCount || 0;

            const dealerTotal = result.betHistory.length;

            // 计算庄家平均点数
            const dealerAveragePoints = this._calculateDealerAveragePoints(result.betHistory);

            this._createDetailSection(detailsContainer, '庄家统计', [
                { label: '庄家爆牌次数', value: dealerBustCount },
                { label: '庄家爆牌率', value: dealerTotal > 0 ? (dealerBustCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '庄家黑杰克次数', value: dealerBlackjackCount },
                { label: '庄家黑杰克率', value: dealerTotal > 0 ? (dealerBlackjackCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '庄家平均点数', value: dealerAveragePoints }
            ]);

            // 3. 玩家手牌统计
            // 确保玩家爆牌次数正确计算 - 检查多种可能的属性名和结果类型
            const playerBustCount = result.betHistory.filter(bet =>
                bet.playerBusted === true ||
                bet.result === 'bust' ||
                bet.playerCards && Array.isArray(bet.playerCards) && this._isBustHand(bet.playerCards)
            ).length || result.playerBustCount || 0;

            // 确保其他统计数据正确
            const playerBlackjackCount = result.blackjackCount ||
                                        result.betHistory.filter(bet =>
                                            bet.result === 'blackjack' ||
                                            (bet.playerCards && Array.isArray(bet.playerCards) && this._isBlackjack(bet.playerCards))
                                        ).length || 0;

            // 直接从 betHistory 计算投降次数，以确保与记录的投降事件一致
            const playerSurrenderCount = result.betHistory ? result.betHistory.filter(bet => bet.result === 'surrender').length : 0;

            // 确保 playerDoubleCount 使用引擎层面的 doubleCount，如果不存在则从 betHistory 计算
            const playerDoubleCount = (typeof result.doubleCount === 'number') ? result.doubleCount :
                                     (result.betHistory ? result.betHistory.filter(bet => bet.isDoubleDown === true).length : 0);

            // 使用引擎计算的总分牌动作次数
            const playerSplitCount = result.splitCount || 0;

            // 计算玩家平均点数
            const playerAveragePoints = this._calculatePlayerAveragePoints(result.betHistory);

            this._createDetailSection(detailsContainer, '玩家手牌统计', [
                { label: '玩家爆牌次数', value: playerBustCount },
                { label: '玩家爆牌率', value: dealerTotal > 0 ? (playerBustCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '玩家黑杰克次数', value: playerBlackjackCount },
                { label: '玩家黑杰克率', value: dealerTotal > 0 ? (playerBlackjackCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '投降次数', value: playerSurrenderCount },
                { label: '投降率', value: dealerTotal > 0 ? (playerSurrenderCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '加倍次数', value: playerDoubleCount },
                { label: '加倍率', value: dealerTotal > 0 ? (playerDoubleCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '加倍成功率', value: result.doubleSuccessRate.toFixed(2) + '%' },
                { label: '分牌次数', value: playerSplitCount },
                { label: '分牌率', value: dealerTotal > 0 ? (playerSplitCount / dealerTotal * 100).toFixed(2) + '%' : '0%' },
                { label: '玩家平均点数', value: playerAveragePoints }
            ]);

            // 5. 下注策略分析
            const betStrategyEnabled = this.config.bettingStrategy.enabled;
            const useCountingBasedBetting = this.config.bettingStrategy.useCountingBasedBetting;

            this._createDetailSection(detailsContainer, '下注策略分析', [
                { label: '下注策略启用', value: betStrategyEnabled ? '是' : '否' },
                { label: '基于算牌下注', value: useCountingBasedBetting ? '是' : '否' },
                { label: '固定下注金额', value: this.config.bettingStrategy.fixedBet },
                { label: '最小下注金额', value: this._getMinBet(result.betHistory) },
                { label: '最大下注金额', value: this._getMaxBet(result.betHistory) },
                { label: '平均下注金额', value: this._getAverageBet(result.betHistory) },
                { label: '下注金额标准差', value: this._getBetStandardDeviation(result.betHistory).toFixed(2) }
            ]);

            // 6. 高级指标
            this._createDetailSection(detailsContainer, '高级指标', [
                { label: '最大回撤', value: this.safeToLocaleString(result.maxDrawdown) },
                { label: '最大回撤率', value: result.initialTotalChips > 0 ?
                    ((result.maxDrawdown || 0) / result.initialTotalChips * 100).toFixed(2) + '%' : '0%' },
                { label: '最长连胜', value: result.playerStats?.longestWinStreak || 0 },
                { label: '最长连败', value: result.playerStats?.longestLoseStreak || 0 },
                { label: '单局最大赢取', value: this.safeToLocaleString(result.playerStats?.largestWin || 0) },
                { label: '单局最大损失', value: this.safeToLocaleString(result.playerStats?.largestLoss || 0) },
                { label: '风险回报比', value: this._calculateRiskRewardRatio(result).toFixed(2) },
                { label: '夏普比率', value: this._calculateSharpeRatio(result).toFixed(2) }
            ]);

            // 7. 玩家优势分析
            try {
                this._createDetailSection(detailsContainer, '玩家优势分析', [
                    { label: '玩家优势', value: this._calculatePlayerEdge(result).toFixed(4) + '%' },
                    { label: '回报率(RTP)', value: this._calculateRTP(result).toFixed(2) + '%' },
                    { label: '投资回报率(ROI)', value: this._calculateROI(result).toFixed(2) + '%' },
                    { label: '每手牌平均盈亏', value: this._calculateProfitPerHand(result).toFixed(2) },
                    { label: '盈亏标准差', value: this._calculateProfitStandardDeviation(result).toFixed(2) },
                    { label: '盈亏方差', value: this._calculateProfitVariance(result).toFixed(2) },
                    { label: '盈亏变异系数', value: this._calculateCoefficientOfVariation(result).toFixed(4) }
                ]);
            } catch (error) {
                console.error('创建玩家优势分析部分时出错:', error);
                // 创建一个简化版的部分，避免错误
                this._createDetailSection(detailsContainer, '玩家优势分析', [
                    { label: '玩家优势', value: '0.0000%' },
                    { label: '回报率(RTP)', value: '0.00%' },
                    { label: '投资回报率(ROI)', value: '0.00%' }
                ]);
            }

            // 8. 显示分布表格
            const distributionSections = [
                { title: '真数分布', data: result.trueCountDistribution, keyLabel: '真数值', valueLabel: '出现次数' },
                { title: '下注分布', data: result.betDistribution, keyLabel: '下注金额', valueLabel: '下注次数' },
                { title: '庄家明牌分布', data: this._calculateDealerUpcardDistribution(result.betHistory), keyLabel: '庄家明牌', valueLabel: '出现次数' },
                { title: '玩家起始点数分布', data: this._calculatePlayerStartingPointsDistribution(result.betHistory), keyLabel: '玩家起始点数', valueLabel: '出现次数' }
            ];

            distributionSections.forEach(section => {
                if (section.data && Object.keys(section.data).length > 0) {
                    const sectionTitle = document.createElement('h4');
                    sectionTitle.textContent = section.title;
                    detailsTab.appendChild(sectionTitle);

                    const table = document.createElement('table');
                    table.className = 'simulation-data-table';

                    // 创建表头
                    const thead = document.createElement('thead');
                    const headerRow = document.createElement('tr');

                    // 使用自定义标签或默认标签
                    const keyLabel = section.keyLabel || '键';
                    const valueLabel = section.valueLabel || '值';

                    [keyLabel, valueLabel, '百分比'].forEach(text => {
                        const th = document.createElement('th');
                        th.textContent = text;
                        headerRow.appendChild(th);
                    });
                    thead.appendChild(headerRow);
                    table.appendChild(thead);

                    // 创建表体
                    const tbody = document.createElement('tbody');
                    const total = Object.values(section.data).reduce((sum, val) => sum + val, 0);

                    Object.entries(section.data).forEach(([key, value]) => {
                        const row = document.createElement('tr');

                        // 键
                        const keyCell = document.createElement('td');
                        keyCell.textContent = key;
                        row.appendChild(keyCell);

                        // 值
                        const valueCell = document.createElement('td');
                        valueCell.textContent = this.safeToLocaleString(value);
                        row.appendChild(valueCell);

                        // 百分比
                        const percentCell = document.createElement('td');
                        percentCell.textContent = ((value / total) * 100).toFixed(2) + '%';
                        row.appendChild(percentCell);

                        tbody.appendChild(row);
                    });

                    table.appendChild(tbody);
                    detailsTab.appendChild(table);
                }
            });

            if (detailsTab.children.length === 0) {
                const noDataMsg = document.createElement('div');
                noDataMsg.className = 'simulation-no-data';
                noDataMsg.textContent = '没有详细数据';
                detailsTab.appendChild(noDataMsg);
            }
        }

        // 不再在结果面板中添加导入导出按钮，这些按钮已经移到了控制面板底部
    }

    /**
     * 添加概览卡片
     * @param {HTMLElement} container - 容器元素
     * @param {string} title - 标题
     * @param {string} value - 值
     * @param {string} unit - 单位
     * @param {string} valueClass - 值的CSS类
     */
    addOverviewCard(container, title, value, unit = '', valueClass = '') {
        const card = document.createElement('div');
        card.className = 'simulation-overview-card';

        // 添加标题
        const titleElement = document.createElement('div');
        titleElement.className = 'simulation-card-title';
        titleElement.textContent = title;
        card.appendChild(titleElement);

        // 创建值和单位的容器
        const valueContainer = document.createElement('div');
        valueContainer.className = 'simulation-card-value-container';

        // 创建值元素
        const valueElement = document.createElement('span');
        valueElement.className = `simulation-card-value ${valueClass}`;
        valueElement.textContent = value; // 直接使用传入的值，不进行格式化
        valueContainer.appendChild(valueElement);

        // 如果有单位，添加内联单位元素
        if (unit) {
            const unitElement = document.createElement('span');
            unitElement.className = 'simulation-card-unit-inline';
            unitElement.textContent = unit;
            valueContainer.appendChild(unitElement);

            // 为了兼容性，也添加旧的单位元素（但设置为不显示）
            const oldUnitElement = document.createElement('div');
            oldUnitElement.className = 'simulation-card-unit';
            oldUnitElement.textContent = unit;
            card.appendChild(oldUnitElement);
        }

        card.appendChild(valueContainer);
        container.appendChild(card);
    }

    /**
     * 添加统计表格行
     * @param {HTMLElement} table - 表格元素
     * @param {string} label - 标签
     * @param {string} value - 值
     */
    addStatsRow(table, label, value) {
        const row = document.createElement('tr');

        const labelCell = document.createElement('td');
        labelCell.className = 'simulation-table-label';
        labelCell.textContent = label;
        row.appendChild(labelCell);

        const valueCell = document.createElement('td');
        valueCell.className = 'simulation-table-value';

        // 根据不同类型的数据添加颜色样式
        if (label.includes('盈亏') || label.includes('筹码')) {
            // 判断是正数还是负数
            const numValue = parseFloat(value.replace(/,/g, ''));
            if (!isNaN(numValue)) {
                if (numValue > 0) {
                    valueCell.classList.add('positive');
                } else if (numValue < 0) {
                    valueCell.classList.add('negative');
                }
            }
        } else if (label.includes('胜率') || label.includes('黑杰克率') || label.includes('优势')) {
            // 判断百分比值
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                if (numValue > 0) {
                    valueCell.classList.add('positive');
                } else if (numValue < 0) {
                    valueCell.classList.add('negative');
                }
            }
        }

        valueCell.textContent = value;
        row.appendChild(valueCell);

        table.appendChild(row);
    }

    /**
     * 创建详细数据部分
     * @param {HTMLElement} container - 容器元素
     * @param {string} title - 部分标题
     * @param {Array<{label: string, value: any}>} items - 数据项数组
     * @private
     */
    _createDetailSection(container, title, items) {
        const section = document.createElement('div');
        section.className = 'simulation-detail-section';

        const sectionTitle = document.createElement('h4');
        sectionTitle.textContent = title;
        section.appendChild(sectionTitle);

        const table = document.createElement('table');
        table.className = 'simulation-detail-table';

        items.forEach(item => {
            const row = document.createElement('tr');

            const labelCell = document.createElement('td');
            labelCell.className = 'simulation-detail-label';
            labelCell.textContent = item.label;
            row.appendChild(labelCell);

            const valueCell = document.createElement('td');
            valueCell.className = 'simulation-detail-value';
            valueCell.textContent = item.value;
            row.appendChild(valueCell);

            table.appendChild(row);
        });

        section.appendChild(table);
        container.appendChild(section);
    }

    /**
     * 计算庄家平均点数
     * @param {Array} betHistory - 下注历史记录
     * @returns {string} 庄家平均点数
     * @private
     */
    _calculateDealerAveragePoints(betHistory) {
        if (!betHistory || betHistory.length === 0) return '0.00'; // 历史记录为空，平均点数自然是0

        let totalPoints = 0;
        let validCount = 0;

        betHistory.forEach(bet => {
            // 检查多种可能的属性名
            const points = bet.dealerPoints || bet.dealerValue || bet.dealerFinalPoints ||
                          (bet.dealer && bet.dealer.points) || (bet.dealer && bet.dealer.value);

            if (points && points > 0) {
                totalPoints += points;
                validCount++;
            }
        });

        // 如果没有有效数据，使用默认值
        if (validCount === 0) {
            if (!betHistory || betHistory.length === 0) return '0.00'; // 历史记录为空，平均点数自然是0
            // 尝试从结果中找出庄家点数
            const dealerBustCount = betHistory.filter(bet => bet.dealerBusted === true || bet.result === 'dealer_bust').length;
            const dealerBlackjackCount = betHistory.filter(bet => bet.dealerBlackjack === true || bet.result === 'dealer_blackjack').length;

            // 如果有爆牌和黑杰克数据，可以估算平均点数
            if (dealerBustCount > 0 || dealerBlackjackCount > 0) {
                // 估算平均点数：假设爆牌平均为23点，黑杰克为21点，其他为18点
                const totalEstimatedPoints = dealerBustCount * 23 + dealerBlackjackCount * 21 +
                                           (betHistory.length - dealerBustCount - dealerBlackjackCount) * 18;
                return (totalEstimatedPoints / betHistory.length).toFixed(2);
            }

            // 如果没有任何数据或估算失败，返回 N/A
            return 'N/A';
        }

        return (totalPoints / validCount).toFixed(2);
    }

    /**
     * 计算玩家平均点数
     * @param {Array} betHistory - 下注历史记录
     * @returns {string} 玩家平均点数
     * @private
     */
    _calculatePlayerAveragePoints(betHistory) {
        if (!betHistory || betHistory.length === 0) return '0.00'; // 历史记录为空，平均点数自然是0

        let totalPoints = 0;
        let validCount = 0;

        betHistory.forEach(bet => {
            // 检查多种可能的属性名
            const points = bet.playerPoints || bet.playerValue || bet.playerFinalPoints ||
                          (bet.player && bet.player.points) || (bet.player && bet.player.value);

            if (points && points > 0) {
                totalPoints += points;
                validCount++;
            }
        });

        // 如果没有有效数据，使用默认值
        if (validCount === 0) {
            // 历史记录不为空，但无法从中提取任何有效的点数信息，则尝试估算
            const playerBustCount = betHistory.filter(bet => bet.playerBusted === true || bet.result === 'bust').length;
            const playerBlackjackCount = betHistory.filter(bet => bet.playerBlackjack === true || bet.result === 'blackjack').length;

            // 如果有爆牌和黑杰克数据，可以估算平均点数
            if (playerBustCount > 0 || playerBlackjackCount > 0) {
                // 估算平均点数：假设爆牌平均为23点，黑杰克为21点，其他为17点
                const totalEstimatedPoints = playerBustCount * 23 + playerBlackjackCount * 21 +
                                           (betHistory.length - playerBustCount - playerBlackjackCount) * 17;
                return (totalEstimatedPoints / betHistory.length).toFixed(2);
            }

            // 如果没有任何数据或估算失败，返回 N/A
            return 'N/A';
        }

        return (totalPoints / validCount).toFixed(2);
    }

    /**
     * 判断手牌是否爆牌
     * @param {Array} cards - 手牌
     * @returns {boolean} 是否爆牌
     * @private
     */
    _isBustHand(cards) {
        if (!cards || !Array.isArray(cards) || cards.length === 0) return false;

        // 计算手牌点数
        let total = 0;
        let aceCount = 0;

        // 遍历所有牌
        for (const card of cards) {
            if (!card || !card.rank) continue;

            // 获取牌面值
            let value = 0;
            if (card.rank === 'A') {
                value = 11;
                aceCount++;
            } else if (['K', 'Q', 'J', '10'].includes(card.rank)) {
                value = 10;
            } else {
                value = parseInt(card.rank) || 0;
            }

            total += value;
        }

        // 处理A的特殊情况
        while (total > 21 && aceCount > 0) {
            total -= 10;
            aceCount--;
        }

        return total > 21;
    }

    /**
     * 判断手牌是否为黑杰克
     * @param {Array} cards - 手牌
     * @returns {boolean} 是否为黑杰克
     * @private
     */
    _isBlackjack(cards) {
        if (!cards || !Array.isArray(cards) || cards.length !== 2) return false;

        // 黑杰克必须是两张牌
        if (cards.length !== 2) return false;

        // 检查是否有A
        const hasAce = cards.some(card => card && card.rank === 'A');

        // 检查是否有10点牌
        const has10 = cards.some(card => card && ['10', 'J', 'Q', 'K'].includes(card.rank));

        return hasAce && has10;
    }

    /**
     * 获取最小下注金额
     * @param {Array} betHistory - 下注历史记录
     * @returns {number} 最小下注金额
     * @private
     */
    _getMinBet(betHistory) {
        if (!betHistory || betHistory.length === 0) return 0;

        let minBet = Infinity;

        betHistory.forEach(bet => {
            if (bet.bet !== undefined && bet.bet < minBet) {
                minBet = bet.bet;
            }
        });

        return minBet === Infinity ? 0 : minBet;
    }

    /**
     * 获取最大下注金额
     * @param {Array} betHistory - 下注历史记录
     * @returns {number} 最大下注金额
     * @private
     */
    _getMaxBet(betHistory) {
        if (!betHistory || betHistory.length === 0) return 0;

        let maxBet = -Infinity;

        betHistory.forEach(bet => {
            if (bet.bet !== undefined && bet.bet > maxBet) {
                maxBet = bet.bet;
            }
        });

        return maxBet === -Infinity ? 0 : maxBet;
    }

    /**
     * 获取平均下注金额
     * @param {Array} betHistory - 下注历史记录
     * @returns {string} 平均下注金额
     * @private
     */
    _getAverageBet(betHistory) {
        if (!betHistory || betHistory.length === 0) return '0';

        let totalBet = 0;

        betHistory.forEach(bet => {
            if (bet.bet !== undefined) {
                totalBet += bet.bet;
            }
        });

        return (totalBet / betHistory.length).toFixed(2);
    }

    /**
     * 计算下注金额标准差
     * @param {Array} betHistory - 下注历史记录
     * @returns {number} 下注金额标准差
     * @private
     */
    _getBetStandardDeviation(betHistory) {
        if (!betHistory || betHistory.length <= 1) return 0;

        // 计算平均值
        const mean = betHistory.reduce((sum, bet) => sum + bet.bet, 0) / betHistory.length;

        // 计算方差
        const variance = betHistory.reduce((sum, bet) => {
            const diff = bet.bet - mean;
            return sum + diff * diff;
        }, 0) / betHistory.length;

        // 计算标准差
        return Math.sqrt(variance);
    }

    /**
     * 计算风险回报比
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 风险回报比
     * @private
     */
    _calculateRiskRewardRatio(result) {
        if (!result || !result.playerStats) return 0;

        const totalWin = result.playerStats.totalWinAmount;
        const totalLoss = result.playerStats.totalLossAmount;

        return totalLoss > 0 ? totalWin / totalLoss : 0;
    }

    /**
     * 计算夏普比率（简化版）
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 夏普比率
     * @private
     */
    _calculateSharpeRatio(result) {
        if (!result || !result.chipsCurve || result.chipsCurve.length <= 1) return 0;

        // 计算每局收益率
        const returns = [];
        for (let i = 1; i < result.chipsCurve.length; i++) {
            const prevChips = result.chipsCurve[i-1].chips.reduce((sum, chips) => sum + chips, 0);
            const currentChips = result.chipsCurve[i].chips.reduce((sum, chips) => sum + chips, 0);

            if (prevChips > 0) {
                returns.push((currentChips - prevChips) / prevChips);
            }
        }

        if (returns.length === 0) return 0;

        // 计算平均收益率
        const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;

        // 计算收益率标准差
        const variance = returns.reduce((sum, ret) => {
            const diff = ret - meanReturn;
            return sum + diff * diff;
        }, 0) / returns.length;

        const stdDev = Math.sqrt(variance);

        // 计算夏普比率（假设无风险收益率为0）
        return stdDev > 0 ? meanReturn / stdDev : 0;
    }

    /**
     * 计算庄家明牌分布
     * @param {Array} betHistory - 下注历史记录
     * @returns {Object} 庄家明牌分布
     * @private
     */
    _calculateDealerUpcardDistribution(betHistory) {
        if (!betHistory || betHistory.length === 0) return {};

        const distribution = {};

        betHistory.forEach(bet => {
            if (bet.dealerUpcard !== undefined) {
                const upcard = bet.dealerUpcard;
                distribution[upcard] = (distribution[upcard] || 0) + 1;
            }
        });

        return distribution;
    }

    /**
     * 计算玩家起始点数分布
     * @param {Array} betHistory - 下注历史记录
     * @returns {Object} 玩家起始点数分布
     * @private
     */
    _calculatePlayerStartingPointsDistribution(betHistory) {
        if (!betHistory || betHistory.length === 0) return {};

        const distribution = {};

        betHistory.forEach(bet => {
            if (bet.playerStartingPoints !== undefined) {
                const points = bet.playerStartingPoints;
                distribution[points] = (distribution[points] || 0) + 1;
            } else if (bet.playerInitialHand) {
                // 如果没有直接的起始点数，但有初始手牌，尝试计算点数
                try {
                    const cards = bet.playerInitialHand.split(',').map(card => card.trim());
                    const points = this._calculateHandPoints(cards);
                    distribution[points] = (distribution[points] || 0) + 1;
                } catch (e) {
                    // 忽略无法计算的手牌
                }
            }
        });

        return distribution;
    }

    /**
     * 计算手牌点数（简化版）
     * @param {Array<string>} cards - 卡牌数组
     * @returns {number} 手牌点数
     * @private
     */
    _calculateHandPoints(cards) {
        if (!cards || cards.length === 0) return 0;

        let points = 0;
        let aceCount = 0;

        cards.forEach(card => {
            const value = card.replace(/[♠♥♦♣]/g, '').trim();

            if (value === 'A') {
                points += 11;
                aceCount++;
            } else if (['J', 'Q', 'K'].includes(value)) {
                points += 10;
            } else {
                points += parseInt(value) || 0;
            }
        });

        // 处理A的软点数
        while (points > 21 && aceCount > 0) {
            points -= 10;
            aceCount--;
        }

        return points;
    }

    /**
     * 加载预设点击处理
     * @param {Event} event - 点击事件
     */
    onLoadPresetClick(event) {
        const button = event.target;
        const presetName = button.dataset.preset;

        if (!presetName) return;

        // 加载预设策略
        this.loadPreset(presetName);
    }

    /**
     * 加载预设策略
     * @param {string} presetName - 预设名称
     */
    loadPreset(presetName) {
        if (!presetName) return;

        // 应用预设
        this.config.applyPreset(presetName);

        // 如果UI已创建，更新显示
        if (this.isCreated) {
            // 更新启用自动下注的开关
            const bettingEnabledToggle = document.getElementById('bettingEnabled');
            if (bettingEnabledToggle) {
                bettingEnabledToggle.checked = this.config.bettingStrategy.enabled;
            }

            // 更新使用基于算牌的下注策略的开关
            const useCountingBasedBettingToggle = document.getElementById('useCountingBasedBetting');
            if (useCountingBasedBettingToggle) {
                useCountingBasedBettingToggle.checked = this.config.bettingStrategy.useCountingBasedBetting;
            }

            // 更新真数阈值表格
            this.updateThresholdTable();
        }

        // 显示提示消息
        const presetNames = {
            'conservative': '保守',
            'balanced': '均衡',
            'aggressive': '激进'
        };
        this.showMessage(`已加载${presetNames[presetName] || presetName}策略预设`);
    }

    /**
     * 更新真数阈值表格
     */
    updateThresholdTable() {
        // 查找真数阈值表格容器
        const thresholdTable = document.querySelector('.simulation-threshold-table');
        if (!thresholdTable) return;

        // 清空表格内容（保留表头）
        const tableBody = thresholdTable.querySelector('tbody');
        if (tableBody) {
            tableBody.innerHTML = '';

            // 添加预设的阈值行
            this.config.bettingStrategy.thresholds.forEach(threshold => {
                this.addThresholdRow(tableBody, threshold.trueCount, threshold.bet);
            });
        }
    }

    /**
     * 更新配置表单
     */
    updateConfigForm() {
        // 更新表单元素
        const form = document.getElementById('simulationConfigForm');
        if (!form) return;

        // 更新基本设置
        const numberOfGamesInput = form.querySelector('[name="numberOfGames"]');
        if (numberOfGamesInput) numberOfGamesInput.value = this.config.numberOfGames;

        const numberOfDecksInput = form.querySelector('[name="numberOfDecks"]');
        if (numberOfDecksInput) numberOfDecksInput.value = this.config.numberOfDecks;

        const penetrationRateInput = form.querySelector('[name="penetrationRate"]');
        if (penetrationRateInput) penetrationRateInput.value = Math.round(this.config.penetrationRate * 100);

        const dealerStandSoft17Input = form.querySelector('[name="dealerStandSoft17"]');
        if (dealerStandSoft17Input) dealerStandSoft17Input.checked = this.config.dealerStandSoft17;

        const playerCountInput = form.querySelector('[name="playerCount"]');
        if (playerCountInput) playerCountInput.value = this.config.playerCount;

        const startingChipsInput = form.querySelector('[name="startingChips"]');
        if (startingChipsInput) startingChipsInput.value = this.config.startingChips;

        // 更新算牌系统
        const countingSystemSelect = form.querySelector('[name="countingSystem"]');
        if (countingSystemSelect) countingSystemSelect.value = this.config.countingSystem;

        // 更新下注策略
        const bettingEnabledInput = form.querySelector('[name="bettingEnabled"]');
        if (bettingEnabledInput) bettingEnabledInput.checked = this.config.bettingStrategy.enabled;

        const useCountingBasedBettingInput = form.querySelector('[name="useCountingBasedBetting"]');
        if (useCountingBasedBettingInput) useCountingBasedBettingInput.checked = this.config.bettingStrategy.useCountingBasedBetting;

        const fixedBetInput = form.querySelector('[name="fixedBet"]');
        if (fixedBetInput) fixedBetInput.value = this.config.bettingStrategy.fixedBet;

        // 更新真数阈值表格
        this.updateThresholdTable();

        // 更新下注策略相关UI元素的可见性
        this.updateBettingStrategyVisibility();
    }

    /**
     * 更新下注策略相关UI元素的可见性
     */
    updateBettingStrategyVisibility() {
        const form = document.getElementById('simulationConfigForm');
        if (!form) return;

        const bettingEnabled = form.querySelector('[name="bettingEnabled"]').checked;
        const useCountingBasedBetting = form.querySelector('[name="useCountingBasedBetting"]').checked;

        // 获取相关元素
        const countingBasedBettingRow = form.querySelector('#useCountingBasedBetting').closest('.simulation-form-row');
        const fixedBetRow = form.querySelector('#fixedBet').closest('.simulation-form-row');
        const thresholdTableContainer = document.querySelector('.simulation-threshold-container');
        const presetButtonsRow = document.querySelector('.simulation-preset-buttons').closest('.simulation-form-row');

        if (countingBasedBettingRow) {
            countingBasedBettingRow.style.display = bettingEnabled ? 'flex' : 'none';
        }

        if (fixedBetRow) {
            fixedBetRow.style.display = (bettingEnabled && !useCountingBasedBetting) ? 'flex' : 'none';
        }

        if (thresholdTableContainer) {
            thresholdTableContainer.style.display = (bettingEnabled && useCountingBasedBetting) ? 'block' : 'none';
        }

        if (presetButtonsRow) {
            presetButtonsRow.style.display = (bettingEnabled && useCountingBasedBetting) ? 'flex' : 'none';
        }
    }

    /**
     * 添加真数阈值行
     * @param {HTMLElement} tableBody - 表格body元素
     * @param {number} trueCount - 真数阈值
     * @param {number} betAmount - 下注金额
     */
    addThresholdRow(tableBody, trueCount, betAmount) {
        const row = document.createElement('tr');

        // 真数阈值单元格
        const trueCountCell = document.createElement('td');
        const trueCountInput = document.createElement('input');
        trueCountInput.type = 'number';
        trueCountInput.step = 'any';
        trueCountInput.value = trueCount;
        trueCountInput.className = 'simulation-threshold-input';
        trueCountCell.appendChild(trueCountInput);
        row.appendChild(trueCountCell);

        // 下注金额单元格
        const betAmountCell = document.createElement('td');
        const betAmountInput = document.createElement('input');
        betAmountInput.type = 'number';
        betAmountInput.min = '1';
        betAmountInput.value = betAmount;
        betAmountInput.className = 'simulation-threshold-input';
        betAmountCell.appendChild(betAmountInput);
        row.appendChild(betAmountCell);

        // 操作单元格
        const actionCell = document.createElement('td');
        const deleteButton = document.createElement('button');
        deleteButton.type = 'button';
        deleteButton.className = 'simulation-delete-threshold-button';
        deleteButton.textContent = '删除';
        deleteButton.addEventListener('click', () => this.deleteThresholdRow(row, tableBody));
        actionCell.appendChild(deleteButton);
        row.appendChild(actionCell);

        tableBody.appendChild(row);
    }

    /**
     * 删除真数阈值行
     * @param {HTMLElement} row - 要删除的行
     * @param {HTMLElement} tableBody - 表格body元素
     */
    deleteThresholdRow(row, tableBody) {
        // 确保至少保留一行
        if (tableBody.children.length <= 1) {
            this.showMessage('至少需要保留一个阈值设置');
            return;
        }

        tableBody.removeChild(row);
    }

    /**
     * 导出结果点击处理
     */
    onExportResultsClick() {
        // 检查是否有模拟引擎
        if (!this.engine) {
            this.showMessage('模拟引擎未初始化，请先运行模拟');
            return;
        }

        // 检查是否有模拟结果
        if (!this.engine.result) {
            this.showMessage('还没有生成模拟结果，请先运行模拟');
            return;
        }

        // 检查是否有下注历史数据
        if (!this.engine.result.betHistory || this.engine.result.betHistory.length === 0) {
            this.showMessage('模拟结果中没有下注历史数据，请先运行模拟');
            return;
        }

        try {
            // 格式化结果
            const result = this.engine.result;

            // 创建要导出的数据
            const exportData = {
                simulationConfig: this.config,
                simulationResult: {
                    totalGames: result.totalGames,
                    completedGames: result.completedGames,
                    totalPlayers: result.totalPlayers,
                    initialChips: result.initialChips,
                    finalChips: result.finalChips,
                    netProfit: result.netProfit,
                    profitPerGame: result.profitPerGame,
                    winCount: result.winCount,
                    loseCount: result.loseCount,
                    pushCount: result.pushCount,
                    blackjackCount: result.blackjackCount,
                    winRate: result.winRate,
                    blackjackRate: result.blackjackRate,
                    doubleCount: result.doubleCount,
                    splitCount: result.splitCount,
                    surrenderCount: result.surrenderCount,
                    doubleSuccessRate: result.doubleSuccessRate,
                    maxDrawdown: result.maxDrawdown,
                    simulationTime: result.simulationTime,
                    gamesPerSecond: result.gamesPerSecond,
                    betHistory: result.betHistory,
                    chipHistory: result.chipHistory,
                    trueCountDistribution: result.trueCountDistribution,
                    timestamp: new Date().toISOString()
                }
            };

            // 转换为JSON字符串
            const jsonString = JSON.stringify(exportData, null, 2);

            // 创建Blob
            const blob = new Blob([jsonString], { type: 'application/json' });

            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `blackjack-simulation-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showMessage('结果导出成功');
        } catch (error) {
            console.error('导出结果失败', error);
            this.showError(`导出结果失败: ${error.message}`);
        }
    }

    /**
     * 导入按钮点击处理
     */
    onImportButtonClick() {
        // 如果导入器不存在，创建它
        if (!this.importer) {
            // 动态加载导入模块
            if (typeof SimulationImport === 'undefined') {
                // 尝试加载脚本
                const script = document.createElement('script');
                script.src = 'js/simulation-import.js';
                script.onload = () => {
                    if (typeof SimulationImport !== 'undefined') {
                        this.importer = new SimulationImport(this);
                        this.importer.init();
                        this.importer.showFileDialog();
                    } else {
                        this.showError('无法加载导入模块');
                    }
                };
                script.onerror = () => {
                    this.showError('加载导入模块失败');
                };
                document.head.appendChild(script);
            } else {
                this.importer = new SimulationImport(this);
                this.importer.init();
                this.importer.showFileDialog();
            }
        } else {
            // 如果导入器已存在，直接显示文件对话框
            this.importer.showFileDialog();
        }
    }

    /**
     * 同步到游戏本体按钮点击处理
     */
    onSyncToGameClick() {
        console.info('同步到游戏本体...');

        // 检查是否有模拟引擎
        if (!this.engine) {
            this.showMessage('模拟引擎未初始化，请先运行模拟');
            return;
        }

        // 检查是否有模拟结果
        if (!this.engine.result) {
            this.showMessage('还没有生成模拟结果，请先运行模拟');
            return;
        }

        // 检查游戏本体是否存在
        if (!window.game) {
            this.showError('游戏本体不存在，无法同步数据');
            return;
        }

        try {
            // 显示正在处理的消息
            this.showMessage('正在准备同步到游戏本体，请稍候...');

            // 检查玩家数量匹配
            const simulationPlayerCount = this.config.playerCount || 1;
            const gamePlayerCount = window.game.players ? window.game.players.length : 0;

            console.log(`模拟玩家数: ${simulationPlayerCount}, 游戏本体玩家数: ${gamePlayerCount}`);

            // 如果玩家数量不匹配，调整游戏本体的玩家数量
            if (gamePlayerCount !== simulationPlayerCount) {
                console.log(`调整游戏本体玩家数量: ${gamePlayerCount} -> ${simulationPlayerCount}`);
                if (typeof window.game.updatePlayerCount === 'function') {
                    window.game.updatePlayerCount(simulationPlayerCount);
                } else {
                    // 手动调整玩家数量
                    while (window.game.players.length < simulationPlayerCount) {
                        window.game.addPlayer();
                    }
                    while (window.game.players.length > simulationPlayerCount) {
                        window.game.removePlayer();
                    }
                }
                console.log(`玩家数量调整完成，当前玩家数: ${window.game.players.length}`);
            }

            // 确保游戏本体不会在开始新游戏时重置统计数据
            // 因为我们要将模拟结果同步到游戏本体，不希望被重置
            if (window.game.resetStatsOnNewGame !== undefined) {
                window.game.resetStatsOnNewGame = false;
                console.log('已禁用游戏本体在开始新游戏时重置统计数据');
            }

            // 1. 模拟点击"开始新游戏"按钮
            console.log('模拟点击"开始新游戏"按钮');
            const newGameButton = document.getElementById('new-game');
            if (newGameButton) {
                // 触发点击事件
                newGameButton.click();
                console.log('已触发"开始新游戏"按钮点击事件');

                // 延迟执行后续同步操作，确保游戏状态已完全重置
                setTimeout(() => {
                    // 1. 同步下注历史记录（先同步历史记录，因为其他数据依赖于它）
                    this._syncBetHistory();

                    // 2. 同步玩家信息（筹码、胜负记录等）
                    this._syncPlayerInfo();

                    // 3. 同步玩家统计数据
                    this._syncPlayerStats();

                    // 4. 同步总盈亏数据
                    this._syncTotalProfit();

                    // 5. 同步游戏设置（牌库、渗透率、庄家规则等）
                    this._syncGameSettings();

                    // 6. 同步下注策略设置
                    this._syncBettingStrategy();

                    // 7. 更新游戏本体的游戏计数器，确保下一局是模拟结束后的局数
                    if (this.engine.result.completedGames) {
                        window.game.gameCount = this.engine.result.completedGames;
                        console.log(`更新游戏本体的游戏计数器: ${window.game.gameCount}`);
                    }

                    // 8. 强制同步最长连胜和最长连败数据，确保它们被正确设置
                    console.log('强制同步最长连胜和最长连败数据，确保它们被正确设置');
                    if (this.engine.result.playerStats) {
                        // 获取模拟引擎中的最长连胜和最长连败数据
                        const longestWinStreak = this.engine.result.playerStats.longestWinStreak || 0;
                        const longestLoseStreak = this.engine.result.playerStats.longestLoseStreak || 0;

                        console.log(`模拟引擎中的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);

                        // 同步到玩家对象
                        if (window.game.players) {
                            window.game.players.forEach(player => {
                                if (player && player.stats) {
                                    // 强制设置最长连胜和最长连败，不再检查undefined
                                    player.stats.longestWinStreak = longestWinStreak;
                                    player.stats.longestLoseStreak = longestLoseStreak;
                                    // 同时重置当前连胜和连败，避免影响后续游戏
                                    player.stats.currentWinStreak = 0;
                                    player.stats.currentLoseStreak = 0;
                                    console.log(`强制设置玩家${player.name}的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);
                                }
                            });
                        }

                        // 同步到玩家统计面板
                        if (window.playerStats) {
                            // 强制设置最长连胜和最长连败，不再检查undefined
                            window.playerStats.longestWinStreak = longestWinStreak;
                            window.playerStats.longestLoseStreak = longestLoseStreak;
                            console.log(`强制设置玩家统计面板的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);
                        }
                    }

                    // 9. 批量更新所有UI组件，避免重复刷新
                    console.log('批量更新所有UI组件...');

                    // 更新玩家统计面板
                    if (window.playerStats && typeof window.playerStats.updateStatsDisplay === 'function') {
                        window.playerStats.updateStatsDisplay();
                        console.log('已更新玩家统计面板');
                    }

                    // 更新下注历史面板
                    if (window.bettingHistory && typeof window.bettingHistory.updateHistoryTable === 'function') {
                        window.bettingHistory.updateHistoryTable();
                        console.log('已更新下注历史面板');
                    }

                    // 更新游戏本体UI（包含玩家信息）
                    if (typeof window.game.updateUI === 'function') {
                        window.game.updateUI();
                        console.log('已更新游戏本体UI');
                    }

                    // 单独更新玩家信息面板（确保数据正确显示）
                    if (typeof window.game.updatePlayersDisplay === 'function') {
                        window.game.updatePlayersDisplay();
                        console.log('已更新玩家信息面板');
                    }

                    console.log('所有UI组件更新完成');

                    // 10. 验证同步后的数据一致性
                    this._validateSyncedData();

                    this.showMessage('成功同步到游戏本体');
                }, 2500); // 延迟2.5秒执行
                return; // 提前返回，避免执行后续代码
            }

            // 如果找不到按钮，继续执行原有逻辑
            console.warn('找不到"开始新游戏"按钮，继续执行原有逻辑');

            // 0. 先重置游戏本体的所有统计数据，确保没有残留数据
            console.log('先重置游戏本体的所有统计数据，确保没有残留数据');
            if (typeof window.game.resetAllStats === 'function') {
                window.game.resetAllStats();
                console.log('已重置游戏本体的所有统计数据');
            }

            // 1. 同步下注历史记录（先同步历史记录，因为其他数据依赖于它）
            this._syncBetHistory();

            // 2. 同步玩家信息（筹码、胜负记录等）
            this._syncPlayerInfo();

            // 3. 同步玩家统计数据
            this._syncPlayerStats();

            // 4. 同步总盈亏数据
            this._syncTotalProfit();

            // 5. 同步游戏设置（牌库、渗透率、庄家规则等）
            this._syncGameSettings();

            // 6. 同步下注策略设置
            this._syncBettingStrategy();

            // 更新游戏本体的游戏计数器和其他数据在_syncAllData中处理

            // 其他同步操作在_syncAllData中处理

            this.showMessage('成功同步到游戏本体');
        } catch (error) {
            console.error('同步到游戏本体失败', error);
            this.showError(`同步到游戏本体失败: ${error.message}`);
        }
    }

    /**
     * 关闭按钮点击处理
     */
    onCloseButtonClick() {
        // 如果正在模拟，先停止
        if (this.isSimulationRunning) {
            this.engine.stop();
            this.isSimulationRunning = false;
        }

        this.hide();
    }

    /**
     * 同步玩家信息到游戏本体
     * @private
     */
    _syncPlayerInfo() {
        const result = this.engine.result;
        if (!result) return;

        // 获取游戏本体的玩家
        const gamePlayers = window.game.players;
        if (!gamePlayers || gamePlayers.length === 0) {
            console.warn('游戏本体没有玩家，无法同步玩家信息');
            return;
        }

        // 获取最新的筹码曲线数据
        let playerChipsArray = [];
        if (result.chipsCurve && result.chipsCurve.length > 0) {
            // 使用最后一条筹码曲线记录中的每个玩家筹码
            const lastChipsRecord = result.chipsCurve[result.chipsCurve.length - 1];
            if (lastChipsRecord.chips && Array.isArray(lastChipsRecord.chips)) {
                playerChipsArray = lastChipsRecord.chips;
                console.log(`从筹码曲线获取玩家筹码: [${playerChipsArray.join(', ')}]`);
            }
        }

        // 获取初始筹码配置
        const startingChips = this.config?.startingChips || result.initialChips || 100000;

        // 如果没有筹码曲线数据，尝试从模拟引擎的玩家对象获取
        if (playerChipsArray.length === 0 && this.engine.gameInstance && this.engine.gameInstance.players) {
            playerChipsArray = this.engine.gameInstance.players.map(p => p.chips || startingChips);
            console.log(`从模拟引擎玩家对象获取筹码: [${playerChipsArray.join(', ')}]`);
        }

        // 如果仍然没有数据，平均分配总筹码（兜底方案）
        if (playerChipsArray.length === 0) {
            const playerCount = gamePlayers.length;
            const chipsPerPlayer = Math.floor(result.finalChips / playerCount);
            playerChipsArray = Array(playerCount).fill(chipsPerPlayer);
            console.log(`使用平均分配方案: 总筹码=${result.finalChips}, 玩家数=${playerCount}, 每人筹码=${chipsPerPlayer}`);
        }

        console.log(`同步玩家信息: 总筹码=${result.finalChips}, 玩家筹码分配=[${playerChipsArray.join(', ')}]`);

        // 更新每个玩家的筹码
        gamePlayers.forEach((player, index) => {
            const oldChips = player.chips;
            const newChips = playerChipsArray[index] || startingChips;
            player.chips = newChips;
            console.log(`更新玩家 ${player.name} 的筹码: ${oldChips} -> ${newChips}`);

            // 如果有玩家统计数据，同步胜负记录
            if (result.playerStats) {
                // 计算该玩家的盈亏（当前筹码 - 初始筹码）
                const playerProfit = newChips - startingChips;
                player.stats.chipProfit = playerProfit;
                player.stats.totalScore = playerProfit; // 总得分现在用于记录筹码盈亏

                // 从下注历史记录中获取该玩家的独立统计数据
                if (window.bettingHistory && window.bettingHistory.getPlayerStats) {
                    const playerStats = window.bettingHistory.getPlayerStats(index);
                    if (playerStats) {
                        // 使用该玩家的独立统计数据
                        player.stats.wins = playerStats.winCount || 0;
                        player.stats.losses = playerStats.loseCount || 0;
                        player.stats.pushes = playerStats.pushCount || 0;
                        player.stats.blackjacks = playerStats.blackjackCount || 0;
                    } else {
                        // 如果没有独立统计数据，使用全局统计数据（多玩家时不准确）
                        player.stats.wins = result.winCount || 0;
                        player.stats.losses = result.loseCount || 0;
                        player.stats.pushes = result.pushCount || 0;
                        player.stats.blackjacks = result.blackjackCount || 0;
                    }
                } else {
                    // 如果没有下注历史功能，使用全局统计数据
                    player.stats.wins = result.winCount || 0;
                    player.stats.losses = result.loseCount || 0;
                    player.stats.pushes = result.pushCount || 0;
                    player.stats.blackjacks = result.blackjackCount || 0;
                }

                // 同步操作统计 - 这些也应该从该玩家的历史记录中计算
                // 计算该玩家的操作统计
                let playerDoubleCount = 0;
                let playerDoubleWinCount = 0;
                let playerSplitCount = 0;
                let playerSurrenderCount = 0;

                // 用于跟踪该玩家已计算的分牌操作的游戏局号
                const playerSplitGameIds = new Set();

                if (window.bettingHistory && window.bettingHistory.history) {
                    window.bettingHistory.history.forEach(record => {
                        // 只处理该玩家的记录
                        if (record.playerIndex === index) {
                            // 计算加倍次数和加倍获胜次数
                            if (record.isDoubleDown) {
                                playerDoubleCount++;
                                if (record.result === '赢' || record.result === '黑杰克') {
                                    playerDoubleWinCount++;
                                }
                            }

                            // 计算分牌次数 - 只计算分牌操作，不计算分牌后的手牌
                            if (record.actionType === '分牌') {
                                // 只有当这个局号的分牌操作还没被计算过时才增加计数
                                if (!playerSplitGameIds.has(record.gameId)) {
                                    playerSplitCount++;
                                    playerSplitGameIds.add(record.gameId);
                                }
                            }

                            // 另一种检测分牌的方式 - 通过isSplit标志和手牌索引
                            if (record.isSplit && record.handIndex === 0 && !playerSplitGameIds.has(record.gameId)) {
                                playerSplitCount++;
                                playerSplitGameIds.add(record.gameId);
                            }

                            // 计算投降次数
                            if (record.result === '投降') {
                                playerSurrenderCount++;
                            }
                        }
                    });
                }

                player.stats.doubles = playerDoubleCount;
                player.stats.doubleWins = playerDoubleWinCount;
                player.stats.splits = playerSplitCount;
                player.stats.surrenders = playerSurrenderCount;

                // 同步高级统计数据
                if (result.playerStats.largestWin !== undefined) {
                    player.stats.largestWin = result.playerStats.largestWin;
                }
                if (result.playerStats.largestLoss !== undefined) {
                    player.stats.largestLoss = result.playerStats.largestLoss;
                }
                if (result.playerStats.longestWinStreak !== undefined) {
                    player.stats.longestWinStreak = result.playerStats.longestWinStreak;
                }
                if (result.playerStats.longestLoseStreak !== undefined) {
                    player.stats.longestLoseStreak = result.playerStats.longestLoseStreak;
                }

                // 更新最高/最低筹码记录
                player.stats.maxChips = Math.max(newChips, result.playerStats.highestChips || 0);

                // 确保最低筹码正确同步 - 使用模拟引擎中的最低筹码值
                if (result.playerStats.lowestChips !== undefined) {
                    player.stats.minChips = result.playerStats.lowestChips;
                } else {
                    player.stats.minChips = Math.min(newChips, player.stats.minChips || newChips);
                }

                console.log(`更新玩家 ${player.name} 的统计数据: 胜=${player.stats.wins}, 负=${player.stats.losses}, 黑杰克=${player.stats.blackjacks}, 盈亏=${playerProfit}`);
            }
        });

        // 强制更新UI
        if (typeof window.game.updatePlayersDisplay === 'function') {
            window.game.updatePlayersDisplay();
        }
    }

    /**
     * 同步下注历史记录到游戏本体
     * @private
     */
    _syncBetHistory() {
        const result = this.engine.result;
        if (!result || !result.betHistory) return;

        // 检查游戏本体是否有下注历史记录功能
        if (!window.bettingHistory) {
            console.warn('游戏本体没有下注历史记录功能，无法同步下注历史');
            return;
        }

        // 清空现有的下注历史记录
        window.bettingHistory.clearHistory();

        // 获取模拟结果的下注历史记录
        const betHistory = result.betHistory;
        console.log(`同步下注历史记录: ${betHistory.length} 条记录`);

        // 更新游戏计数器
        window.bettingHistory.gameCount = result.completedGames;

        // 用于跟踪每个游戏局的分牌手牌数量
        const splitHandsCount = new Map();

        // 预处理分牌记录，确保每个游戏局的分牌手牌都被正确计数
        // 创建游戏局号到手牌记录的映射，用于后续处理
        const gameHandsMap = new Map();

        betHistory.forEach(bet => {
            const gameId = bet.game || 0;

            // 初始化游戏局的记录集合
            if (!gameHandsMap.has(gameId)) {
                gameHandsMap.set(gameId, []);
            }

            // 添加记录到对应的游戏局
            gameHandsMap.get(gameId).push(bet);

            // 如果是分牌记录，添加到分牌手牌计数
            if (bet.isSplit) {
                if (!splitHandsCount.has(gameId)) {
                    splitHandsCount.set(gameId, new Set());
                }
                // 记录这个游戏局中的所有手牌索引
                splitHandsCount.get(gameId).add(bet.handIndex !== undefined ? bet.handIndex : 0);
            }
        });

        // 检查是否有缺失的分牌手牌记录
        gameHandsMap.forEach((hands, gameId) => {
            // 如果这个游戏局有分牌记录
            if (splitHandsCount.has(gameId)) {
                const handIndices = splitHandsCount.get(gameId);
                const maxHandIndex = Math.max(...handIndices);

                // 检查是否所有手牌索引都存在
                for (let i = 0; i <= maxHandIndex; i++) {
                    if (!handIndices.has(i)) {
                        console.warn(`局号${gameId}缺少手牌索引${i}的记录`);
                    }
                }

                // 将分牌手牌集合转换为数量
                splitHandsCount.set(gameId, maxHandIndex + 1);
                console.log(`局号${gameId}的分牌手牌索引: ${Array.from(handIndices).join(', ')}, 总手牌数: ${maxHandIndex + 1}`);
            }
        });

        // 用于跟踪每个玩家的筹码变化
        const playerChips = {};

        // 初始化每个玩家的筹码数
        if (result.initialChips) {
            // 获取所有玩家索引
            const playerIndices = [...new Set(betHistory.map(bet => bet.playerIndex || 0))];
            playerIndices.forEach(playerIndex => {
                playerChips[playerIndex] = result.initialChips;
            });
        } else {
            // 如果没有初始筹码，使用默认值
            playerChips[0] = 100000; // 默认玩家筹码
        }

        // 将模拟结果的下注历史记录添加到游戏本体
        betHistory.forEach((bet, index) => {
            // 将英文结果转换为中文
            let resultText = '未知';
            switch (bet.result) {
                case 'win':
                    resultText = '赢';
                    break;
                case 'lose':
                    resultText = '输';
                    break;
                case 'push':
                    resultText = '平';
                    break;
                case 'blackjack':
                    resultText = '黑杰克';
                    break;
                case 'bust':
                    resultText = '爆牌';
                    break;
                case 'surrender':
                    resultText = '投降';
                    break;
                default:
                    resultText = bet.result || '未知';
            }

            // 确定操作类型
            let actionType = '';
            if (bet.isDoubleDown) {
                actionType = '加倍';
            } else if (bet.result === 'split') {
                // 如果结果是分牌操作，明确标记为分牌
                actionType = '分牌';
            } else if (bet.isSplit) {
                // 如果是分牌后的手牌，也标记为分牌
                actionType = '分牌';
            } else if (bet.result === 'surrender') {
                actionType = '投降';
            }

            // 处理分牌手牌标识
            const gameId = bet.game || index + 1;
            let handIdentifier = '';

            // 如果是分牌手牌，添加手牌标识
            if (bet.isSplit || (gameHandsMap.has(gameId) && gameHandsMap.get(gameId).some(b => b.isSplit))) {
                // 使用handIndex如果存在，否则使用0
                const handIndex = bet.handIndex !== undefined ? bet.handIndex : 0;

                // 设置手牌标识
                handIdentifier = `手牌${handIndex + 1}`;

                // 确保isSplit标志被正确设置
                bet.isSplit = true;

                // 记录分牌手牌信息，用于调试
                console.log(`处理分牌记录: 局号${gameId}, 手牌${handIndex + 1}`);
            }

            // 计算玩家筹码变化
            const playerIndex = bet.playerIndex || 0;
            if (playerChips[playerIndex] === undefined) {
                playerChips[playerIndex] = result.initialChips || 100000;
            }

            // 更新玩家筹码
            const profit = bet.profit || 0;
            playerChips[playerIndex] += profit;

            // 创建记录对象
            const record = {
                gameId: gameId,
                playerIndex: playerIndex,
                playerName: bet.playerName || `玩家${playerIndex + 1}`,
                handIndex: bet.handIndex !== undefined ? bet.handIndex : 0,
                handIdentifier: handIdentifier, // 添加手牌标识字段
                bet: bet.bet || 0,
                originalBet: bet.originalBet || bet.bet || 0,
                result: resultText,
                profit: profit,
                timestamp: bet.timestamp || Date.now(),
                actionType: actionType,
                playerCards: bet.playerCards || [],
                dealerCards: bet.dealerCards || [],
                trueCount: bet.trueCount || 0,
                balance: bet.balance !== undefined ? bet.balance : playerChips[playerIndex],
                isDoubleDown: bet.isDoubleDown || false,
                isSplit: bet.isSplit || false
            };

            // 检查是否是分牌记录但没有正确标记
            // 不再特殊处理特定局号，而是通过更通用的方式检测分牌
            if (!record.isSplit && record.handIndex > 0) {
                // 如果手牌索引大于0但没有标记为分牌，可能是分牌记录
                console.log(`检测到可能的分牌记录(局号${gameId}): 手牌索引=${record.handIndex}`);

                // 查找同一局号的其他记录，判断是否是分牌局
                if (gameHandsMap.has(gameId) && gameHandsMap.get(gameId).some(b => b.isSplit || b.actionType === '分牌')) {
                    record.isSplit = true;
                    if (!record.handIdentifier) {
                        record.handIdentifier = `手牌${record.handIndex + 1}`;
                    }
                    console.log(`根据同局其他记录标记为分牌: ${record.handIdentifier}`);
                }
            }

            // 如果是加倍操作，在结果文本中添加加倍标识
            if (record.isDoubleDown) {
                record.resultWithDouble = `${record.result}【加倍】`;
            } else {
                record.resultWithDouble = record.result;
            }

            // 添加到历史记录
            window.bettingHistory.history.push(record);

            // 添加到玩家历史记录
            if (!window.bettingHistory.playerHistory[record.playerIndex]) {
                window.bettingHistory.playerHistory[record.playerIndex] = [];
            }
            window.bettingHistory.playerHistory[record.playerIndex].push(record);
        });

        // 保存历史记录到本地存储
        window.bettingHistory.saveHistory();

        console.log(`下注历史记录同步完成，共 ${window.bettingHistory.history.length} 条记录`);
        console.log(`分牌手牌统计: ${Array.from(splitHandsCount.entries()).map(([gameId, count]) => `局号${gameId}:${count}手牌`).join(', ')}`);

        // 如果下注历史面板正在显示，更新显示
        if (window.bettingHistory.historyPanel && window.bettingHistory.historyPanel.style.display !== 'none') {
            window.bettingHistory.updateHistoryTable();
        }
    }

    /**
     * 同步玩家统计数据到游戏本体
     * @private
     */
    _syncPlayerStats() {
        const result = this.engine.result;
        if (!result) return;

        // 检查游戏本体是否有玩家统计功能
        if (!window.playerStats) {
            console.warn('游戏本体没有玩家统计功能，无法同步玩家统计数据');
            return;
        }

        console.log('同步玩家统计数据');

        // 获取游戏本体的玩家
        const gamePlayers = window.game.players;
        if (!gamePlayers || gamePlayers.length === 0) {
            console.warn('游戏本体没有玩家，无法同步玩家统计数据');
            return;
        }

        // 计算下注历史中的操作统计
        let doubleCount = 0;
        let doubleWinCount = 0;
        let splitCount = 0;
        let surrenderCount = 0;
        let totalWinAmount = 0;
        let totalLossAmount = 0;
        let totalBetAmount = 0;

        // 用于跟踪已计算的分牌操作的游戏局号
        const splitGameIds = new Set();

        // 从下注历史记录中计算操作统计
        if (window.bettingHistory && window.bettingHistory.history) {
            // 用于跟踪已处理的游戏局，避免重复计算下注金额
            const processedGameIds = new Set();

            window.bettingHistory.history.forEach(record => {
                // 累计总下注金额 - 只计算原始下注，避免重复计算分牌和加倍的下注
                // 对于每个游戏局号和玩家，只计算一次下注金额
                const gamePlayerKey = `${record.gameId}-${record.playerIndex}`;
                if (!processedGameIds.has(gamePlayerKey)) {
                    totalBetAmount += record.originalBet || record.bet || 0;
                    processedGameIds.add(gamePlayerKey);
                }

                // 计算加倍次数和加倍获胜次数
                if (record.isDoubleDown) {
                    doubleCount++;
                    if (record.result === '赢' || record.result === '黑杰克') {
                        doubleWinCount++;
                    }
                }

                // 计算分牌次数 - 只计算分牌操作，不计算分牌后的手牌
                if (record.actionType === '分牌') {
                    // 只有当这个局号的分牌操作还没被计算过时才增加计数
                    if (!splitGameIds.has(record.gameId)) {
                        splitCount++;
                        splitGameIds.add(record.gameId);
                    }
                }

                // 另一种检测分牌的方式 - 通过isSplit标志和手牌索引
                if (record.isSplit && record.handIndex === 0 && !splitGameIds.has(record.gameId)) {
                    splitCount++;
                    splitGameIds.add(record.gameId);
                }

                // 计算投降次数
                if (record.result === '投降') {
                    surrenderCount++;
                }

                // 计算总赢取金额和总损失金额
                if (record.profit > 0) {
                    totalWinAmount += record.profit;
                } else if (record.profit < 0) {
                    totalLossAmount += Math.abs(record.profit);
                }
            });
        }

        // 如果模拟引擎中有更准确的分牌次数，使用它
        if (result.splitCount !== undefined && result.splitCount > 0) {
            splitCount = result.splitCount;
            console.log(`使用模拟引擎中的分牌次数: ${splitCount}`);
        } else {
            console.log(`从下注历史记录中计算的分牌次数: ${splitCount}`);
        }

        // 如果模拟引擎中有更准确的总下注金额，使用它
        if (result.playerStats && result.playerStats.totalBetAmount !== undefined) {
            totalBetAmount = result.playerStats.totalBetAmount;
            console.log(`使用模拟引擎中的总下注金额: ${totalBetAmount}`);
        } else {
            console.log(`从下注历史记录中计算的总下注金额: ${totalBetAmount}`);
        }

        // 获取模拟引擎中的最低筹码和最长连胜/连败数据
        let lowestChips = result.playerStats && result.playerStats.lowestChips !== undefined ?
            result.playerStats.lowestChips : Number.MAX_SAFE_INTEGER;
        let longestWinStreak = result.playerStats && result.playerStats.longestWinStreak !== undefined ?
            result.playerStats.longestWinStreak : 0;
        let longestLoseStreak = result.playerStats && result.playerStats.longestLoseStreak !== undefined ?
            result.playerStats.longestLoseStreak : 0;

        // 确保最低筹码不是默认的MAX_SAFE_INTEGER值
        if (lowestChips === Number.MAX_SAFE_INTEGER) {
            // 尝试从下注历史中找到最低筹码
            if (window.bettingHistory && window.bettingHistory.history && window.bettingHistory.history.length > 0) {
                lowestChips = Math.min(...window.bettingHistory.history
                    .filter(record => record.balance !== undefined && record.balance > 0)
                    .map(record => record.balance));

                // 如果仍然没有找到有效值，使用当前筹码
                if (!isFinite(lowestChips) || lowestChips === Infinity) {
                    lowestChips = result.finalChips || this.config.startingChips;
                }
            } else {
                lowestChips = result.finalChips || this.config.startingChips;
            }
        }

        console.log(`从模拟引擎获取的关键统计数据: 最低筹码=${lowestChips}, 最长连胜=${longestWinStreak}, 最长连败=${longestLoseStreak}`);

        // 获取初始筹码配置
        const startingChips = this.config?.startingChips || result.initialChips || 100000;

        // 获取每个玩家的实际筹码和盈亏
        let playerChipsArray = [];
        if (result.chipsCurve && result.chipsCurve.length > 0) {
            const lastChipsRecord = result.chipsCurve[result.chipsCurve.length - 1];
            if (lastChipsRecord.chips && Array.isArray(lastChipsRecord.chips)) {
                playerChipsArray = lastChipsRecord.chips;
            }
        }

        // 如果没有筹码曲线数据，尝试从模拟引擎获取
        if (playerChipsArray.length === 0 && this.engine.gameInstance && this.engine.gameInstance.players) {
            playerChipsArray = this.engine.gameInstance.players.map(p => p.chips || startingChips);
        }

        // 更新玩家统计数据
        gamePlayers.forEach((player, playerIndex) => {
            // 获取该玩家的实际筹码和盈亏
            const playerChips = playerChipsArray[playerIndex] || startingChips;
            const playerProfit = playerChips - startingChips;

            // 从下注历史记录中获取该玩家的统计数据
            if (window.bettingHistory && window.bettingHistory.getPlayerStats) {
                const playerStats = window.bettingHistory.getPlayerStats(playerIndex);

                // 更新玩家统计数据
                if (playerStats) {
                    // 基本统计数据
                    player.stats.wins = playerStats.winCount || 0;
                    player.stats.losses = playerStats.loseCount || 0;
                    player.stats.pushes = playerStats.pushCount || 0;
                    player.stats.blackjacks = playerStats.blackjackCount || 0;
                    player.stats.chipProfit = playerProfit; // 使用实际计算的盈亏
                    player.stats.totalScore = playerProfit; // 使用实际计算的盈亏

                    // 操作统计数据 - 这些应该从该玩家的历史记录中计算
                    // 计算该玩家的操作统计
                    let playerDoubleCount = 0;
                    let playerDoubleWinCount = 0;
                    let playerSplitCount = 0;
                    let playerSurrenderCount = 0;

                    // 用于跟踪该玩家已计算的分牌操作的游戏局号
                    const playerSplitGameIds = new Set();

                    if (window.bettingHistory && window.bettingHistory.history) {
                        window.bettingHistory.history.forEach(record => {
                            // 只处理该玩家的记录
                            if (record.playerIndex === playerIndex) {
                                // 计算加倍次数和加倍获胜次数
                                if (record.isDoubleDown) {
                                    playerDoubleCount++;
                                    if (record.result === '赢' || record.result === '黑杰克') {
                                        playerDoubleWinCount++;
                                    }
                                }

                                // 计算分牌次数 - 只计算分牌操作，不计算分牌后的手牌
                                if (record.actionType === '分牌') {
                                    // 只有当这个局号的分牌操作还没被计算过时才增加计数
                                    if (!playerSplitGameIds.has(record.gameId)) {
                                        playerSplitCount++;
                                        playerSplitGameIds.add(record.gameId);
                                    }
                                }

                                // 另一种检测分牌的方式 - 通过isSplit标志和手牌索引
                                if (record.isSplit && record.handIndex === 0 && !playerSplitGameIds.has(record.gameId)) {
                                    playerSplitCount++;
                                    playerSplitGameIds.add(record.gameId);
                                }

                                // 计算投降次数
                                if (record.result === '投降') {
                                    playerSurrenderCount++;
                                }
                            }
                        });
                    }

                    player.stats.doubles = playerDoubleCount;
                    player.stats.doubleWins = playerDoubleWinCount;
                    player.stats.splits = playerSplitCount;
                    player.stats.surrenders = playerSurrenderCount;

                    // 高级统计数据
                    if (result.playerStats) {
                        // 确保单局最大赢取和损失正确同步
                        if (result.playerStats.largestWin !== undefined) {
                            player.stats.largestWin = result.playerStats.largestWin;
                        }
                        if (result.playerStats.largestLoss !== undefined) {
                            player.stats.largestLoss = result.playerStats.largestLoss;
                        }

                        // 确保最长连胜和最长连败正确同步
                        // 直接使用模拟引擎中的值，不再使用Math.max比较
                        // 注意：这里只是临时设置，最终会在_syncToGameClick方法的最后再次强制设置
                        const longestWinStreak = result.playerStats.longestWinStreak || 0;
                        const longestLoseStreak = result.playerStats.longestLoseStreak || 0;

                        player.stats.longestWinStreak = longestWinStreak;
                        player.stats.longestLoseStreak = longestLoseStreak;
                        console.log(`在_syncPlayerStats中设置玩家${player.name}的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);

                        // 确保当前连胜和连败正确同步
                        if (result.playerStats.currentWinStreak !== undefined) {
                            player.stats.currentWinStreak = result.playerStats.currentWinStreak;
                        }
                        if (result.playerStats.currentLoseStreak !== undefined) {
                            player.stats.currentLoseStreak = result.playerStats.currentLoseStreak;
                        }

                        // 确保最高/最低筹码正确同步
                        if (result.playerStats.highestChips !== undefined) {
                            player.stats.maxChips = result.playerStats.highestChips;
                        }

                        // 确保最低筹码正确同步
                        player.stats.minChips = lowestChips;

                        // 确保总下注金额正确同步
                        if (result.playerStats.totalBetAmount !== undefined) {
                            // 将总下注金额保存到玩家对象中的某个字段
                            player.totalBetAmount = result.playerStats.totalBetAmount;
                            console.log(`更新玩家${player.name}的总下注金额: ${player.totalBetAmount}`);
                        }
                    }

                    console.log(`更新玩家${player.name}统计数据: 胜=${player.stats.wins}, 负=${player.stats.losses}, 平=${player.stats.pushes}, 黑杰克=${player.stats.blackjacks}, 加倍=${player.stats.doubles}, 加倍获胜=${player.stats.doubleWins}, 分牌=${player.stats.splits}, 投降=${player.stats.surrenders}, 盈亏=${playerProfit}`);
                    console.log(`更新玩家${player.name}高级统计数据: 最大赢取=${player.stats.largestWin}, 最大损失=${player.stats.largestLoss}, 最长连胜=${player.stats.longestWinStreak}, 最长连败=${player.stats.longestLoseStreak}, 最高筹码=${player.stats.maxChips}, 最低筹码=${player.stats.minChips}`);
                }
            }
        });

        // 更新玩家统计面板中的数据
        if (window.playerStats) {
            // 设置总赢取金额和总损失金额
            window.playerStats.totalWinAmount = totalWinAmount;
            window.playerStats.totalLossAmount = totalLossAmount;

            // 设置总下注金额 - 确保使用模拟引擎中的值
            if (result.playerStats && result.playerStats.totalBetAmount !== undefined) {
                window.playerStats.totalBetAmount = result.playerStats.totalBetAmount;
                console.log(`同步总下注金额到玩家统计面板: ${window.playerStats.totalBetAmount}`);
            } else {
                window.playerStats.totalBetAmount = totalBetAmount;
                console.log(`使用计算的总下注金额: ${totalBetAmount}`);
            }

            // 设置分牌次数
            window.playerStats.splitCount = splitCount;

            // 确保最低筹码正确同步
            window.playerStats.lowestChips = lowestChips;
            console.log(`同步最低筹码到玩家统计面板: ${window.playerStats.lowestChips}`);

            // 确保最长连胜和最长连败正确同步
            // 直接使用模拟引擎中的值，不再使用Math.max比较
            // 注意：这里只是临时设置，最终会在_syncToGameClick方法的最后再次强制设置
            if (result.playerStats) {
                const longestWinStreak = result.playerStats.longestWinStreak || 0;
                const longestLoseStreak = result.playerStats.longestLoseStreak || 0;

                window.playerStats.longestWinStreak = longestWinStreak;
                window.playerStats.longestLoseStreak = longestLoseStreak;
                console.log(`在_syncPlayerStats中设置玩家统计面板的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);
            }

            console.log(`更新玩家统计面板数据: 总下注金额=${window.playerStats.totalBetAmount}, 总赢取金额=${totalWinAmount}, 总损失金额=${totalLossAmount}, 分牌次数=${splitCount}, 最长连胜=${window.playerStats.longestWinStreak}, 最长连败=${window.playerStats.longestLoseStreak}`);

            // 如果玩家统计面板正在显示，更新显示
            if (window.playerStats.statsPanel && window.playerStats.statsPanel.style.display !== 'none') {
                // 检查updateStatsDisplay方法是否存在
                if (typeof window.playerStats.updateStatsDisplay === 'function') {
                    window.playerStats.updateStatsDisplay();
                } else {
                    console.warn('window.playerStats.updateStatsDisplay不是一个函数，无法更新玩家统计面板');
                    // 尝试使用filterStats方法作为替代
                    if (typeof window.playerStats.filterStats === 'function') {
                        window.playerStats.filterStats();
                        console.log('使用filterStats方法更新玩家统计面板');
                    }
                }
            }
        }
    }

    /**
     * 同步游戏设置到游戏本体
     * @private
     */
    _syncGameSettings() {
        const config = this.config;
        if (!config) return;

        // 检查游戏本体
        if (!window.game || !window.game.deck) {
            console.warn('游戏本体没有牌组，无法同步游戏设置');
            return;
        }

        console.log('同步游戏设置');

        // 同步牌库数量
        if (config.numberOfDecks && window.game.deck) {
            // 如果牌库数量不同，创建新的牌库
            if (window.game.deck.numberOfDecks !== config.numberOfDecks) {
                console.log(`更新牌库数量: ${window.game.deck.numberOfDecks} -> ${config.numberOfDecks}`);
                window.game.deck = new Deck(config.numberOfDecks);
                window.game.deck.shuffle();
            }
        }

        // 同步渗透率
        if (config.penetrationRate && window.game.deck) {
            console.log(`更新渗透率: ${window.game.deck.getPenetrationRate()} -> ${config.penetrationRate}`);
            window.game.deck.setPenetrationRate(config.penetrationRate);
        }

        // 同步庄家软17停牌规则
        if (config.dealerStandSoft17 !== undefined) {
            console.log(`更新庄家软17停牌规则: ${window.game.dealerStandSoft17} -> ${config.dealerStandSoft17}`);
            window.game.dealerStandSoft17 = config.dealerStandSoft17;
        }

        // 同步算牌系统
        if (config.countingSystem && window.cardCountingSystem) {
            console.log(`更新算牌系统: ${window.cardCountingSystem.currentSystem} -> ${config.countingSystem}`);
            window.cardCountingSystem.setCountingSystem(config.countingSystem);

            // 同步计数值
            if (this.engine.result && this.engine.result.runningCount !== undefined) {
                console.log(`更新运行计数: ${window.cardCountingSystem.runningCount} -> ${this.engine.result.runningCount}`);
                window.cardCountingSystem.runningCount = this.engine.result.runningCount;
            }

            // 同步真数
            if (this.engine.result && this.engine.result.trueCount !== undefined) {
                console.log(`更新真数: ${window.cardCountingSystem.getTrueCount ? window.cardCountingSystem.getTrueCount() : 'N/A'} -> ${this.engine.result.trueCount}`);
                // 由于真数是计算出来的，我们不需要手动更新，只需确保运行计数正确即可
                // 注意：不再调用不存在的updateTrueCount方法
            }
        }
    }

    /**
     * 同步下注策略设置到游戏本体
     * @private
     */
    _syncBettingStrategy() {
        const config = this.config;
        if (!config || !config.bettingStrategy) return;

        // 检查游戏本体是否有算牌系统
        if (!window.cardCountingSystem) {
            console.warn('游戏本体没有算牌系统，无法同步下注策略');
            return;
        }

        console.log('同步下注策略设置');

        // 同步自动下注设置
        if (config.bettingStrategy.enabled !== undefined) {
            console.log(`更新自动下注设置: ${window.cardCountingSystem.isAutoBetEnabled} -> ${config.bettingStrategy.enabled}`);
            window.cardCountingSystem.isAutoBetEnabled = config.bettingStrategy.enabled;
        }

        // 同步基于算牌的下注策略
        if (config.bettingStrategy.useCountingBasedBetting !== undefined) {
            console.log(`更新基于算牌的下注策略: ${window.cardCountingSystem.useCountingBasedBetting} -> ${config.bettingStrategy.useCountingBasedBetting}`);
            window.cardCountingSystem.useCountingBasedBetting = config.bettingStrategy.useCountingBasedBetting;
        }

        // 同步下注阈值
        if (config.bettingStrategy.thresholds && Array.isArray(config.bettingStrategy.thresholds)) {
            console.log(`更新下注阈值: ${config.bettingStrategy.thresholds.length} 个阈值`);
            window.cardCountingSystem.betThresholds = [...config.bettingStrategy.thresholds];
        }
    }

    /**
     * 同步总盈亏数据到游戏本体
     * @private
     */
    _syncTotalProfit() {
        const result = this.engine.result;
        if (!result) return;

        // 从下注历史记录中计算总盈亏
        let totalProfit = 0;
        let totalWinAmount = 0;
        let totalLossAmount = 0;

        // 如果有下注历史记录，从历史记录中计算总盈亏
        if (window.bettingHistory && window.bettingHistory.history) {
            window.bettingHistory.history.forEach(record => {
                const profit = record.profit || 0;
                totalProfit += profit;

                // 计算总赢取金额和总损失金额
                if (profit > 0) {
                    totalWinAmount += profit;
                } else if (profit < 0) {
                    totalLossAmount += Math.abs(profit);
                }
            });
        }
        // 否则使用模拟结果中的净盈亏
        else if (result.netProfit !== undefined) {
            totalProfit = result.netProfit;

            // 如果有玩家统计数据，使用其中的总赢取和总损失金额
            if (result.playerStats) {
                totalWinAmount = result.playerStats.totalWinAmount || 0;
                totalLossAmount = result.playerStats.totalLossAmount || 0;
            } else {
                // 如果没有详细数据，根据净盈亏估算
                if (totalProfit > 0) {
                    totalWinAmount = totalProfit;
                } else {
                    totalLossAmount = Math.abs(totalProfit);
                }
            }
        }

        // 使用模拟结果中的净盈亏（如果可用且与计算值不同）
        if (result.netProfit !== undefined) {
            console.log(`使用模拟引擎中的净盈亏: ${result.netProfit}`);
            totalProfit = result.netProfit;
        }

        // 确保总赢取金额和总损失金额与模拟引擎中的值一致
        if (result.playerStats) {
            if (result.playerStats.totalWinAmount !== undefined) {
                totalWinAmount = result.playerStats.totalWinAmount;
                console.log(`使用模拟引擎中的总赢取金额: ${totalWinAmount}`);
            }
            if (result.playerStats.totalLossAmount !== undefined) {
                totalLossAmount = result.playerStats.totalLossAmount;
                console.log(`使用模拟引擎中的总损失金额: ${totalLossAmount}`);
            }
        }

        // 检查游戏本体是否有总盈亏显示
        const totalProfitElement = document.getElementById('total-profit');
        if (totalProfitElement) {
            console.log(`同步总盈亏显示: ${totalProfitElement.textContent} -> ${totalProfit}`);

            // 更新总盈亏显示
            totalProfitElement.textContent = totalProfit > 0 ? `+${totalProfit}` : `${totalProfit}`;

            // 更新颜色
            if (totalProfit > 0) {
                totalProfitElement.style.color = '#10b981'; // 绿色
            } else if (totalProfit < 0) {
                totalProfitElement.style.color = '#ef4444'; // 红色
            } else {
                totalProfitElement.style.color = '#ffffff'; // 白色
            }
        }

        // 同时更新游戏本体的总盈亏变量（如果存在）
        if (window.game) {
            if (window.game.totalProfit !== undefined) {
                window.game.totalProfit = totalProfit;
                console.log(`更新游戏本体总盈亏变量: ${window.game.totalProfit}`);
            }

            // 更新游戏本体的玩家统计数据
            const gamePlayers = window.game.players;
            if (gamePlayers && gamePlayers.length > 0) {
                // 获取初始筹码配置
                const startingChips = this.config?.startingChips || result.initialChips || 100000;

                // 获取每个玩家的实际筹码和盈亏
                let playerChipsArray = [];
                if (result.chipsCurve && result.chipsCurve.length > 0) {
                    const lastChipsRecord = result.chipsCurve[result.chipsCurve.length - 1];
                    if (lastChipsRecord.chips && Array.isArray(lastChipsRecord.chips)) {
                        playerChipsArray = lastChipsRecord.chips;
                    }
                }

                // 如果没有筹码曲线数据，尝试从模拟引擎获取
                if (playerChipsArray.length === 0 && this.engine.gameInstance && this.engine.gameInstance.players) {
                    playerChipsArray = this.engine.gameInstance.players.map(p => p.chips || startingChips);
                }

                gamePlayers.forEach((player, index) => {
                    // 获取该玩家的实际筹码
                    const playerChips = playerChipsArray[index] || startingChips;
                    const playerProfit = playerChips - startingChips;

                    // 更新玩家的总盈亏 - 使用实际计算的盈亏，不是平均分配
                    player.stats.chipProfit = playerProfit;
                    player.stats.totalScore = playerProfit;

                    // 确保玩家的最高筹码和最低筹码正确 - 直接使用模拟引擎的值
                    if (result.playerStats && result.playerStats.highestChips !== undefined) {
                        player.stats.maxChips = result.playerStats.highestChips;
                    }

                    if (result.playerStats && result.playerStats.lowestChips !== undefined) {
                        player.stats.minChips = result.playerStats.lowestChips;
                    }

                    // 确保最长连胜和最长连败正确 - 直接使用模拟引擎的值
                    // 注意：这里只是临时设置，最终会在_syncToGameClick方法的最后再次强制设置
                    if (result.playerStats) {
                        const longestWinStreak = result.playerStats.longestWinStreak || 0;
                        const longestLoseStreak = result.playerStats.longestLoseStreak || 0;

                        player.stats.longestWinStreak = longestWinStreak;
                        player.stats.longestLoseStreak = longestLoseStreak;
                        console.log(`在_syncTotalProfit中设置玩家${player.name}的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);
                    }

                    console.log(`更新玩家${player.name}的盈亏: ${playerProfit} (筹码: ${playerChips}), 最高筹码: ${player.stats.maxChips}, 最低筹码: ${player.stats.minChips}, 最长连胜: ${player.stats.longestWinStreak}, 最长连败: ${player.stats.longestLoseStreak}`);
                });
            }
        }

        // 更新玩家统计面板中的数据
        if (window.playerStats) {
            // 确保总赢取金额和总损失金额被正确设置
            window.playerStats.totalWinAmount = totalWinAmount;
            window.playerStats.totalLossAmount = totalLossAmount;

            // 设置总盈亏
            if (window.playerStats.totalProfit !== undefined) {
                window.playerStats.totalProfit = totalProfit;
            }

            // 确保总下注金额正确同步
            if (result.playerStats && result.playerStats.totalBetAmount !== undefined) {
                window.playerStats.totalBetAmount = result.playerStats.totalBetAmount;
                console.log(`同步总下注金额到玩家统计面板: ${window.playerStats.totalBetAmount}`);

                // 同时更新玩家对象中的总下注金额
                if (window.game && window.game.players) {
                    window.game.players.forEach(player => {
                        if (player) {
                            player.totalBetAmount = result.playerStats.totalBetAmount;
                        }
                    });
                }
            }

            // 确保最长连胜和最长连败正确同步
            // 注意：这里只是临时设置，最终会在_syncToGameClick方法的最后再次强制设置
            if (result.playerStats) {
                const longestWinStreak = result.playerStats.longestWinStreak || 0;
                const longestLoseStreak = result.playerStats.longestLoseStreak || 0;

                window.playerStats.longestWinStreak = longestWinStreak;
                window.playerStats.longestLoseStreak = longestLoseStreak;
                console.log(`在_syncTotalProfit中设置玩家统计面板的最长连胜: ${longestWinStreak}, 最长连败: ${longestLoseStreak}`);
            }

            console.log(`更新玩家统计面板数据: 总盈亏=${totalProfit}, 总赢取金额=${totalWinAmount}, 总损失金额=${totalLossAmount}, 最长连胜=${window.playerStats.longestWinStreak}, 最长连败=${window.playerStats.longestLoseStreak}`);

            // 强制更新玩家统计面板显示
            if (typeof window.playerStats.updateStatsDisplay === 'function') {
                window.playerStats.updateStatsDisplay();
                console.log('强制更新玩家统计面板显示');
            }
        }
    }

    /**
     * 验证同步后的数据一致性
     * @private
     */
    _validateSyncedData() {
        if (!this.engine || !this.engine.result || !window.game || !window.game.players) {
            console.warn('无法验证同步数据：缺少必要的对象');
            return;
        }

        const result = this.engine.result;
        const gamePlayers = window.game.players;
        const startingChips = this.config?.startingChips || result.initialChips || 100000;

        console.log('=== 开始验证同步后的数据一致性 ===');

        // 验证玩家数量
        const expectedPlayerCount = this.config.playerCount || 1;
        if (gamePlayers.length !== expectedPlayerCount) {
            console.error(`玩家数量不匹配: 期望${expectedPlayerCount}, 实际${gamePlayers.length}`);
        } else {
            console.log(`✓ 玩家数量匹配: ${gamePlayers.length}`);
        }

        // 验证总筹码
        const totalPlayerChips = gamePlayers.reduce((sum, player) => sum + (player.chips || 0), 0);
        const expectedTotalChips = result.finalChips;
        if (Math.abs(totalPlayerChips - expectedTotalChips) > 1) { // 允许1的误差
            console.error(`总筹码不匹配: 期望${expectedTotalChips}, 实际${totalPlayerChips}`);
        } else {
            console.log(`✓ 总筹码匹配: ${totalPlayerChips}`);
        }

        // 验证每个玩家的数据
        gamePlayers.forEach((player, index) => {
            const playerProfit = player.chips - startingChips;
            const expectedProfit = player.stats.chipProfit;

            if (Math.abs(playerProfit - expectedProfit) > 1) { // 允许1的误差
                console.error(`玩家${index + 1}盈亏不匹配: 计算值${playerProfit}, 统计值${expectedProfit}`);
            } else {
                console.log(`✓ 玩家${index + 1}盈亏匹配: ${playerProfit}`);
            }
        });

        // 验证下注历史记录数量
        if (window.bettingHistory && window.bettingHistory.history) {
            const historyCount = window.bettingHistory.history.length;
            const expectedHistoryCount = result.betHistory ? result.betHistory.length : 0;

            if (historyCount !== expectedHistoryCount) {
                console.error(`下注历史记录数量不匹配: 期望${expectedHistoryCount}, 实际${historyCount}`);
            } else {
                console.log(`✓ 下注历史记录数量匹配: ${historyCount}`);
            }
        }

        console.log('=== 数据一致性验证完成 ===');
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        if (!this.elements.errorContainer) return;

        const errorContainer = this.elements.errorContainer;
        errorContainer.innerHTML = `<div class="simulation-error-message">${message}</div>`;
        errorContainer.style.display = 'block';

        // 自动隐藏
        setTimeout(() => {
            errorContainer.style.display = 'none';
        }, 5000);
    }

    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {number} duration - 持续时间（毫秒）
     */
    showMessage(message, duration = 2000) {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = 'simulation-message';
        messageElement.textContent = message;

        // 添加到页面
        document.body.appendChild(messageElement);

        // 淡出动画
        setTimeout(() => {
            messageElement.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(messageElement)) {
                    document.body.removeChild(messageElement);
                }
            }, 300);
        }, duration);
    }

    /**
     * 创建模拟UI
     */
    create() {
        if (this.isCreated) return;

        // 创建主容器
        const container = document.createElement('div');
        container.className = 'simulation-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建顶部标题栏
        const header = document.createElement('div');
        header.className = 'simulation-header';
        container.appendChild(header);

        const title = document.createElement('h2');
        title.textContent = '21点快速模拟系统';
        header.appendChild(title);

        const closeButton = document.createElement('button');
        closeButton.className = 'simulation-close-button';
        closeButton.textContent = '×';
        closeButton.addEventListener('click', this.onCloseButtonClick);
        header.appendChild(closeButton);

        // 创建内容区域
        const content = document.createElement('div');
        content.className = 'simulation-content';
        container.appendChild(content);

        // 创建配置面板
        const configPanel = this.createConfigPanel();
        content.appendChild(configPanel);

        // 创建控制面板
        const controlPanel = document.createElement('div');
        controlPanel.className = 'simulation-panel simulation-control-panel';

        // 添加控制面板标题
        const controlPanelTitle = document.createElement('h3');
        controlPanelTitle.textContent = '模拟控制';
        controlPanel.appendChild(controlPanelTitle);

        // 添加控制按钮
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'simulation-button-group';

        const startButton = document.createElement('button');
        startButton.id = 'simulationStartButton';
        startButton.className = 'simulation-control-button simulation-start-button';
        startButton.textContent = '开始模拟';
        startButton.addEventListener('click', this.onStartButtonClick);
        buttonGroup.appendChild(startButton);

        const pauseButton = document.createElement('button');
        pauseButton.id = 'simulationPauseButton';
        pauseButton.className = 'simulation-control-button simulation-pause-button';
        pauseButton.textContent = '暂停';
        pauseButton.disabled = true;
        pauseButton.addEventListener('click', this.onPauseButtonClick);
        buttonGroup.appendChild(pauseButton);

        const stopButton = document.createElement('button');
        stopButton.id = 'simulationStopButton';
        stopButton.className = 'simulation-control-button simulation-stop-button';
        stopButton.textContent = '停止';
        stopButton.disabled = true;
        stopButton.addEventListener('click', this.onStopButtonClick);
        buttonGroup.appendChild(stopButton);

        controlPanel.appendChild(buttonGroup);

        // 添加进度条
        const progressSection = document.createElement('div');
        progressSection.className = 'simulation-progress-section';

        const progressContainer = document.createElement('div');
        progressContainer.className = 'simulation-progress-container';

        const progressBar = document.createElement('div');
        progressBar.className = 'simulation-progress-bar';
        progressBar.style.width = '0%';
        progressContainer.appendChild(progressBar);

        progressSection.appendChild(progressContainer);

        const statusText = document.createElement('div');
        statusText.className = 'simulation-status-text';
        statusText.textContent = '准备就绪';
        progressSection.appendChild(statusText);

        controlPanel.appendChild(progressSection);

        // 添加性能指标
        const performanceSection = document.createElement('div');
        performanceSection.className = 'simulation-performance-section';

        const metrics = [
            { id: 'completedGames', label: '已完成局数' },
            { id: 'elapsedTime', label: '已用时间' },
            { id: 'gamesPerSecond', label: '每秒模拟局数' },
            { id: 'estimatedTimeRemaining', label: '预计剩余时间' }
        ];

        metrics.forEach(metric => {
            const metricRow = document.createElement('div');
            metricRow.className = 'simulation-metric-row';

            const metricLabel = document.createElement('span');
            metricLabel.className = 'simulation-metric-label';
            metricLabel.textContent = metric.label + ':';
            metricRow.appendChild(metricLabel);

            const metricValue = document.createElement('span');
            metricValue.id = `simulation${metric.id}`;
            metricValue.className = 'simulation-metric-value';
            metricValue.textContent = '-';
            metricRow.appendChild(metricValue);

            performanceSection.appendChild(metricRow);
        });

        controlPanel.appendChild(performanceSection);

        // 添加导入和导出按钮到控制面板底部
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'simulation-control-button-container';
        buttonContainer.style.marginTop = '20px';
        buttonContainer.style.display = 'flex';
        buttonContainer.style.justifyContent = 'center';
        buttonContainer.style.gap = '10px';

        // 添加导出按钮
        const exportButton = document.createElement('button');
        exportButton.id = 'simulationControlExportButton';
        exportButton.className = 'simulation-control-button';
        exportButton.style.width = '120px';
        exportButton.style.height = '40px';
        exportButton.style.backgroundColor = '#7c3aed';
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';
        exportButton.style.borderRadius = '4px';
        exportButton.style.fontSize = '16px';
        exportButton.style.cursor = 'pointer';
        exportButton.textContent = '导出结果';
        exportButton.addEventListener('click', this.onExportResultsClick);
        buttonContainer.appendChild(exportButton);

        // 添加导入按钮
        const importButton = document.createElement('button');
        importButton.id = 'simulationControlImportButton';
        importButton.className = 'simulation-control-button';
        importButton.style.width = '120px';
        importButton.style.height = '40px';
        importButton.style.backgroundColor = '#7c3aed';
        importButton.style.color = 'white';
        importButton.style.border = 'none';
        importButton.style.borderRadius = '4px';
        importButton.style.fontSize = '16px';
        importButton.style.cursor = 'pointer';
        importButton.textContent = '导入数据';
        importButton.addEventListener('click', this.onImportButtonClick);
        buttonContainer.appendChild(importButton);

        controlPanel.appendChild(buttonContainer);

        // 添加同步按钮容器（单独一行）
        const syncButtonContainer = document.createElement('div');
        syncButtonContainer.className = 'simulation-sync-button-container';
        syncButtonContainer.style.marginTop = '10px';
        syncButtonContainer.style.display = 'flex';
        syncButtonContainer.style.justifyContent = 'center';

        // 添加同步到游戏本体按钮
        const syncButton = document.createElement('button');
        syncButton.id = 'simulationSyncButton';
        syncButton.className = 'simulation-control-button';
        syncButton.style.width = '180px';
        syncButton.style.height = '40px';
        syncButton.style.backgroundColor = '#10b981';
        syncButton.style.color = 'white';
        syncButton.style.border = 'none';
        syncButton.style.borderRadius = '4px';
        syncButton.style.fontSize = '16px';
        syncButton.style.cursor = 'pointer';
        syncButton.textContent = '同步到游戏本体';
        syncButton.addEventListener('click', this.onSyncToGameClick);
        syncButtonContainer.appendChild(syncButton);

        controlPanel.appendChild(syncButtonContainer);
        content.appendChild(controlPanel);

        // 创建结果面板
        const resultsPanel = document.createElement('div');
        resultsPanel.className = 'simulation-panel simulation-results-panel';
        resultsPanel.style.display = 'none';

        const resultsPanelTitle = document.createElement('h3');
        resultsPanelTitle.textContent = '模拟结果';
        resultsPanel.appendChild(resultsPanelTitle);

        // 创建选项卡导航
        const tabNav = document.createElement('div');
        tabNav.className = 'simulation-tab-nav';
        resultsPanel.appendChild(tabNav);

        // 定义选项卡
        const tabs = [
            { id: 'overview', text: '概览' },
            { id: 'betHistory', text: '下注历史' },
            { id: 'playerStats', text: '玩家统计' },
            { id: 'charts', text: '图表' },
            { id: 'details', text: '详细数据' }
        ];

        // 创建选项卡按钮
        tabs.forEach((tab, index) => {
            const tabButton = document.createElement('button');
            tabButton.className = 'simulation-tab-button' + (index === 0 ? ' active' : '');
            tabButton.dataset.tab = tab.id;
            tabButton.textContent = tab.text;
            tabButton.addEventListener('click', (e) => {
                // 移除所有激活状态
                tabNav.querySelectorAll('.simulation-tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                // 设置当前选项卡为激活状态
                e.target.classList.add('active');

                // 隐藏所有内容区域
                resultsPanel.querySelectorAll('.simulation-tab-content').forEach(content => {
                    content.style.display = 'none';
                });
                // 显示对应的内容区域
                const targetContent = resultsPanel.querySelector(`.simulation-tab-content[data-tab="${tab.id}"]`);
                if (targetContent) {
                    targetContent.style.display = 'block';
                }
            });
            tabNav.appendChild(tabButton);
        });

        // 为每个选项卡创建内容区域
        tabs.forEach((tab, index) => {
            const tabContent = document.createElement('div');
            tabContent.className = 'simulation-tab-content';
            tabContent.dataset.tab = tab.id;
            tabContent.style.display = index === 0 ? 'block' : 'none';
            resultsPanel.appendChild(tabContent);
        });

        content.appendChild(resultsPanel);

        // 创建错误提示容器
        const errorContainer = document.createElement('div');
        errorContainer.className = 'simulation-error-container';
        errorContainer.style.display = 'none';
        container.appendChild(errorContainer);

        // 保存元素引用
        this.elements = {
            container,
            configPanel,
            controlPanel,
            resultsPanel,
            progressBar,
            statusText,
            errorContainer
        };

        // 标记为已创建
        this.isCreated = true;

        console.info('模拟UI已创建');
    }

    /**
     * 显示模拟UI
     */
    show() {
        try {
            console.log('显示模拟UI...');

            // 确保游戏组件已初始化
            if (!window.game) {
                if (typeof Game === 'function') {
                    console.log('游戏实例不存在，尝试创建...');
                    try {
                        window.game = new Game();
                        window.game.init();

                        if (typeof CardCountingSystem === 'function' && !window.cardCountingSystem) {
                            console.log('算牌系统实例不存在，尝试创建...');
                            window.cardCountingSystem = new CardCountingSystem();
                        }

                        // 检查自动策略是否可用
                        if (typeof AutoStrategy === 'function' && !window.autoStrategy) {
                            console.log('自动策略实例不存在，尝试创建...');
                            window.autoStrategy = new AutoStrategy();
                        }

                        // 等待游戏初始化完成后继续
                        console.log('游戏初始化中，等待继续...');
                        setTimeout(() => {
                            console.log('游戏初始化完成，继续显示模拟UI...');
                            try {
                                this.show();
                            } catch (innerError) {
                                console.error('显示模拟UI时出错:', innerError);
                                this.showError('显示模拟UI时出错: ' + innerError.message);
                            }
                        }, 500);
                    } catch (error) {
                        console.error('创建游戏实例失败:', error);
                        this.showError('创建游戏实例失败: ' + error.message);
                    }
                    return;
                } else {
                    console.error('Game类不存在');
                    this.showError('无法创建游戏实例。Game类不存在，请确保页面加载完成，或刷新页面重试。');
                    return;
                }
            }

            // 确保其他必要组件已初始化
            if (!window.cardCountingSystem && typeof CardCountingSystem === 'function') {
                console.log('算牌系统实例不存在，尝试创建...');
                try {
                    window.cardCountingSystem = new CardCountingSystem();
                } catch (error) {
                    console.error('创建算牌系统实例失败:', error);
                    this.showError('创建算牌系统实例失败: ' + error.message);
                }
            }

            if (!window.autoStrategy && typeof AutoStrategy === 'function') {
                console.log('自动策略实例不存在，尝试创建...');
                try {
                    window.autoStrategy = new AutoStrategy();
                } catch (error) {
                    console.error('创建自动策略实例失败:', error);
                    this.showError('创建自动策略实例失败: ' + error.message);
                }
            }

            if (!this.isCreated) {
                console.log('创建模拟UI...');
                try {
                    this.create();
                } catch (error) {
                    console.error('创建模拟UI失败:', error);
                    this.showError('创建模拟UI失败: ' + error.message);
                    return;
                }
            }

            if (this.elements.container) {
                console.log('显示模拟UI容器...');
                this.elements.container.style.display = 'flex';
                this.isVisible = true;

                // 尝试初始化引擎
                if (!this.engine) {
                    try {
                        console.log('初始化模拟引擎...');
                        this.engine = new SimulationEngine();
                    } catch (error) {
                        console.error('创建模拟引擎失败:', error);
                        this.showError('创建模拟引擎失败: ' + error.message);
                    }
                }
            } else {
                console.error('模拟UI容器不存在');
                this.showError('模拟UI容器不存在，请刷新页面重试。');
            }
        } catch (error) {
            console.error('显示模拟UI过程中出错:', error);

            // 创建一个临时的错误消息元素，以防this.showError不可用
            if (!this.elements || !this.elements.errorContainer) {
                const errorMsg = document.createElement('div');
                errorMsg.style.position = 'fixed';
                errorMsg.style.top = '20px';
                errorMsg.style.left = '50%';
                errorMsg.style.transform = 'translateX(-50%)';
                errorMsg.style.padding = '10px 20px';
                errorMsg.style.background = 'rgba(255, 0, 0, 0.8)';
                errorMsg.style.color = 'white';
                errorMsg.style.borderRadius = '5px';
                errorMsg.style.zIndex = '9999';
                errorMsg.textContent = '显示模拟UI时出错: ' + error.message;
                document.body.appendChild(errorMsg);

                setTimeout(() => {
                    if (document.body.contains(errorMsg)) {
                        document.body.removeChild(errorMsg);
                    }
                }, 5000);
            } else {
                this.showError('显示模拟UI时出错: ' + error.message);
            }
        }
    }

    /**
     * 隐藏模拟UI
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.style.display = 'none';
            this.isVisible = false;
        }
    }

    /**
     * 切换模拟UI的显示状态
     */
    toggle() {
        try {
            console.log('切换模拟UI显示状态...');

            // 尝试先检查组件状态
            if (!this.isVisible) {
                // 检查游戏核心组件，如果不可用，试图创建它们
                console.log('检查游戏核心组件...');

                // 使用更严格的检查来确认类是否存在
                const hasGameClass = typeof window.Game === 'function';
                const hasCountingSystemClass = typeof window.CardCountingSystem === 'function';
                const hasAutoStrategyClass = typeof window.AutoStrategy === 'function';

                // 检查实例是否存在
                const hasGameInstance = typeof window.game !== 'undefined' && window.game !== null;
                const hasCountingSystemInstance = typeof window.cardCountingSystem !== 'undefined' && window.cardCountingSystem !== null;
                const hasAutoStrategyInstance = typeof window.autoStrategy !== 'undefined' && window.autoStrategy !== null;

                console.log('详细检查游戏组件状态:');
                console.log('Game类:', hasGameClass ? '可用' : '不可用');
                console.log('Game实例:', hasGameInstance ? '已创建' : '未创建');
                console.log('CardCountingSystem类:', hasCountingSystemClass ? '可用' : '不可用');
                console.log('CardCountingSystem实例:', hasCountingSystemInstance ? '已创建' : '未创建');
                console.log('AutoStrategy类:', hasAutoStrategyClass ? '可用' : '不可用');
                console.log('AutoStrategy实例:', hasAutoStrategyInstance ? '已创建' : '未创建');

                if (!hasGameClass || !hasCountingSystemClass || !hasAutoStrategyClass) {
                    console.error('检测游戏组件状态:');
                    console.error('Game类:', hasGameClass);
                    console.error('CardCountingSystem类:', hasCountingSystemClass);
                    console.error('AutoStrategy类:', hasAutoStrategyClass);

                    const missing = [];
                    if (!hasGameClass) missing.push('Game');
                    if (!hasCountingSystemClass) missing.push('CardCountingSystem');
                    if (!hasAutoStrategyClass) missing.push('AutoStrategy');

                    // 检查script标签是否存在
                    const scripts = document.querySelectorAll('script');
                    const loadedScripts = Array.from(scripts).map(s => s.src);
                    console.log('当前加载的脚本:', loadedScripts);

                    const errorMsg = `缺少必要的游戏组件: ${missing.join(', ')}。\n请尝试刷新页面，或者先开始一局游戏，然后再尝试使用模拟功能。`;
                    console.error(errorMsg);
                    alert(errorMsg);
                    return;
                }

                // 尝试初始化实例
                if (!hasGameInstance && hasGameClass) {
                    console.log('创建游戏实例...');
                    window.game = new Game();
                    window.game.init();
                }

                if (!hasCountingSystemInstance && hasCountingSystemClass) {
                    console.log('创建算牌系统实例...');
                    window.cardCountingSystem = new CardCountingSystem();
                }

                if (!hasAutoStrategyInstance && hasAutoStrategyClass) {
                    console.log('创建自动策略实例...');
                    window.autoStrategy = new AutoStrategy();
                }

                console.log('游戏组件检查完成，全部通过');
            }

            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        } catch (error) {
            console.error('切换模拟UI时出错:', error);

            // 尝试对常见错误进行特殊处理
            if (error instanceof TypeError && error.message.includes('is not a constructor')) {
                alert('游戏组件加载不完整，请刷新页面后重试。错误详情: ' + error.message);
            } else if (error instanceof ReferenceError) {
                alert('找不到必要的游戏组件，请先开始一局游戏或刷新页面。错误详情: ' + error.message);
            } else {
                alert('切换模拟UI时出错: ' + error.message + '\n请刷新页面后重试。');
            }
        }
    }

    /**
     * 创建配置面板
     * @returns {HTMLElement} 配置面板元素
     */
    createConfigPanel() {
        const panel = document.createElement('div');
        panel.className = 'simulation-panel simulation-config-panel';

        const panelTitle = document.createElement('h3');
        panelTitle.textContent = '模拟配置';
        panel.appendChild(panelTitle);

        // 创建配置表单
        const form = document.createElement('form');
        form.className = 'simulation-config-form';
        form.addEventListener('submit', this.onConfigFormSubmit);
        panel.appendChild(form);

        // 添加表单分组 - 基础游戏设置
        const basicGroup = this.createFormGroup('基础游戏设置');
        form.appendChild(basicGroup);

        // 模拟局数
        this.addNumberInput(basicGroup, 'numberOfGames', '模拟局数', 10, 10000000, 10000);

        // 牌库数量
        const deckOptions = [
            { value: 1, text: '1副牌' },
            { value: 2, text: '2副牌' },
            { value: 4, text: '4副牌' },
            { value: 6, text: '6副牌' },
            { value: 8, text: '8副牌' }
        ];
        this.addSelectInput(basicGroup, 'numberOfDecks', '牌库数量', deckOptions, 8);

        // 渗透率
        this.addRangeInput(basicGroup, 'penetrationRate', '渗透率', 10, 90, 5, 65, '%', (value) => value / 100);

        // 庄家规则
        this.addToggleInput(basicGroup, 'dealerStandSoft17', '庄家软17停牌', true);

        // 玩家数量
        this.addRangeInput(basicGroup, 'playerCount', '玩家数量', 1, 6, 1, 1);

        // 添加表单分组 - 算牌系统设置
        const countingGroup = this.createFormGroup('算牌系统设置');
        form.appendChild(countingGroup);

        // 算牌系统选择
        const countingOptions = [
            { value: 'none', text: '不使用算牌系统' },
            { value: 'hi-lo', text: 'Hi-Lo系统' },
            { value: 'omega-ii', text: 'Omega II系统' },
            { value: 'halves', text: 'Halves系统' }
        ];
        this.addSelectInput(countingGroup, 'countingSystem', '算牌系统', countingOptions, 'hi-lo');

        // 添加表单分组 - 下注策略设置
        const bettingGroup = this.createFormGroup('下注策略设置');
        form.appendChild(bettingGroup);

        // 启用自动下注
        this.addToggleInput(bettingGroup, 'bettingEnabled', '启用自动下注', true);

        // 添加事件监听器，控制相关选项的显示/隐藏
        const bettingEnabledInput = bettingGroup.querySelector('#bettingEnabled');
        if (bettingEnabledInput) {
            bettingEnabledInput.addEventListener('change', () => {
                this.updateBettingStrategyVisibility();
            });
        }

        // 使用基于算牌的下注策略
        this.addToggleInput(bettingGroup, 'useCountingBasedBetting', '使用基于算牌的下注策略', true);

        // 添加事件监听器，控制相关选项的显示/隐藏
        const useCountingBasedBettingInput = bettingGroup.querySelector('#useCountingBasedBetting');
        if (useCountingBasedBettingInput) {
            useCountingBasedBettingInput.addEventListener('change', () => {
                this.updateBettingStrategyVisibility();
            });
        }

        // 固定下注金额
        this.addNumberInput(bettingGroup, 'fixedBet', '固定下注金额', 10, 10000, 100);

        // 预设策略选择
        const presetDiv = document.createElement('div');
        presetDiv.className = 'simulation-form-row';
        bettingGroup.appendChild(presetDiv);

        const presetLabel = document.createElement('label');
        presetLabel.textContent = '下注策略预设';
        presetDiv.appendChild(presetLabel);

        const presetButtons = document.createElement('div');
        presetButtons.className = 'simulation-preset-buttons';
        presetDiv.appendChild(presetButtons);

        const presets = [
            { id: 'conservative', text: '保守' },
            { id: 'balanced', text: '均衡' },
            { id: 'aggressive', text: '激进' }
        ];

        presets.forEach(preset => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'simulation-preset-button';
            button.dataset.preset = preset.id;
            button.textContent = preset.text;
            button.addEventListener('click', this.onLoadPresetClick);
            presetButtons.appendChild(button);
        });

        // 真数阈值与下注金额对应关系表格
        const thresholdContainer = document.createElement('div');
        thresholdContainer.className = 'simulation-threshold-container';
        bettingGroup.appendChild(thresholdContainer);

        const thresholdTitle = document.createElement('div');
        thresholdTitle.className = 'simulation-form-row';
        thresholdContainer.appendChild(thresholdTitle);

        const thresholdLabel = document.createElement('label');
        thresholdLabel.textContent = '真数阈值与下注金额';
        thresholdTitle.appendChild(thresholdLabel);

        // 创建表格
        const thresholdTable = document.createElement('table');
        thresholdTable.className = 'simulation-threshold-table';
        thresholdContainer.appendChild(thresholdTable);

        // 创建表头
        const tableHead = document.createElement('thead');
        thresholdTable.appendChild(tableHead);
        const headerRow = document.createElement('tr');
        tableHead.appendChild(headerRow);

        const trueCountHeader = document.createElement('th');
        trueCountHeader.textContent = '真数阈值';
        headerRow.appendChild(trueCountHeader);

        const betAmountHeader = document.createElement('th');
        betAmountHeader.textContent = '下注金额';
        headerRow.appendChild(betAmountHeader);

        const actionHeader = document.createElement('th');
        actionHeader.textContent = '操作';
        headerRow.appendChild(actionHeader);

        // 创建表格内容区域
        const tableBody = document.createElement('tbody');
        thresholdTable.appendChild(tableBody);

        // 添加默认阈值行
        this.config.bettingStrategy.thresholds.forEach(threshold => {
            this.addThresholdRow(tableBody, threshold.trueCount, threshold.bet);
        });

        // 添加"添加阈值"按钮
        const addButtonContainer = document.createElement('div');
        addButtonContainer.className = 'simulation-form-row';
        thresholdContainer.appendChild(addButtonContainer);

        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.className = 'simulation-add-threshold-button';
        addButton.textContent = '添加阈值';
        addButton.addEventListener('click', () => this.addThresholdRow(tableBody, 0, 100));
        addButtonContainer.appendChild(addButton);

        // 添加表单分组 - 高级设置
        const advancedGroup = this.createFormGroup('高级设置');
        form.appendChild(advancedGroup);

        // 起始筹码
        this.addNumberInput(advancedGroup, 'startingChips', '起始筹码', 1000, 1000000, 100000);

        // 提交按钮
        const submitButton = document.createElement('button');
        submitButton.type = 'submit';
        submitButton.className = 'simulation-submit-button';
        submitButton.textContent = '保存配置并开始模拟';
        form.appendChild(submitButton);

        return panel;
    }

    /**
     * 创建表单分组
     * @param {string} title - 分组标题
     * @returns {HTMLElement} 分组元素
     */
    createFormGroup(title) {
        const group = document.createElement('fieldset');
        group.className = 'simulation-form-group';

        const legend = document.createElement('legend');
        legend.textContent = title;
        group.appendChild(legend);

        return group;
    }

    /**
     * 添加数字输入
     * @param {HTMLElement} parent - 父元素
     * @param {string} id - 输入项ID
     * @param {string} label - 标签文本
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {number} value - 默认值
     */
    addNumberInput(parent, id, label, min, max, value) {
        const row = document.createElement('div');
        row.className = 'simulation-form-row';
        parent.appendChild(row);

        const labelElement = document.createElement('label');
        labelElement.htmlFor = id;
        labelElement.textContent = label;
        row.appendChild(labelElement);

        const input = document.createElement('input');
        input.type = 'number';
        input.id = id;
        input.name = id;
        input.min = min;
        input.max = max;
        input.value = value;
        row.appendChild(input);
    }

    /**
     * 添加范围输入
     * @param {HTMLElement} parent - 父元素
     * @param {string} id - 输入项ID
     * @param {string} label - 标签文本
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {number} step - 步长
     * @param {number} value - 默认值
     * @param {string} unit - 单位
     */
    addRangeInput(parent, id, label, min, max, step, value, unit = '') {
        const row = document.createElement('div');
        row.className = 'simulation-form-row';
        parent.appendChild(row);

        const labelElement = document.createElement('label');
        labelElement.htmlFor = id;
        labelElement.textContent = label;
        row.appendChild(labelElement);

        const rangeContainer = document.createElement('div');
        rangeContainer.className = 'simulation-range-container';
        row.appendChild(rangeContainer);

        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'simulation-range-value';
        valueDisplay.textContent = value + (unit || '');
        rangeContainer.appendChild(valueDisplay);

        const input = document.createElement('input');
        input.type = 'range';
        input.id = id;
        input.className = 'simulation-range-input';
        input.name = id;
        input.min = min;
        input.max = max;
        input.step = step;
        input.value = value;
        rangeContainer.appendChild(input);

        // 更新显示值的事件处理程序
        input.addEventListener('input', () => {
            valueDisplay.textContent = input.value + (unit || '');
        });
    }

    /**
     * 添加选择输入
     * @param {HTMLElement} parent - 父元素
     * @param {string} id - 输入项ID
     * @param {string} label - 标签文本
     * @param {Array<Object>} options - 选项数组，每个选项包含value和text属性
     * @param {string|number} selectedValue - 默认选中值
     */
    addSelectInput(parent, id, label, options, selectedValue) {
        const row = document.createElement('div');
        row.className = 'simulation-form-row';
        parent.appendChild(row);

        const labelElement = document.createElement('label');
        labelElement.htmlFor = id;
        labelElement.textContent = label;
        row.appendChild(labelElement);

        const select = document.createElement('select');
        select.id = id;
        select.name = id;
        row.appendChild(select);

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            if (option.value == selectedValue) {
                optionElement.selected = true;
            }
            select.appendChild(optionElement);
        });
    }

    /**
     * 添加开关输入
     * @param {HTMLElement} parent - 父元素
     * @param {string} id - 输入项ID
     * @param {string} label - 标签文本
     * @param {boolean} checked - 是否默认选中
     */
    addToggleInput(parent, id, label, checked) {
        const row = document.createElement('div');
        row.className = 'simulation-form-row';
        parent.appendChild(row);

        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'simulation-toggle-container';
        row.appendChild(toggleContainer);

        const input = document.createElement('input');
        input.type = 'checkbox';
        input.id = id;
        input.name = id;
        input.className = 'simulation-toggle-input';
        input.checked = checked;
        toggleContainer.appendChild(input);

        const toggleLabel = document.createElement('label');
        toggleLabel.htmlFor = id;
        toggleLabel.className = 'simulation-toggle-label';
        toggleContainer.appendChild(toggleLabel);

        const slider = document.createElement('span');
        slider.className = 'simulation-toggle-slider';
        toggleLabel.appendChild(slider);

        const text = document.createElement('span');
        text.className = 'simulation-toggle-text';
        text.textContent = label;
        toggleLabel.appendChild(text);
    }

    /**
     * 更新下注历史表格
     * @param {SimulationResult} result - 模拟结果
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     * @param {number} page - 当前页码，从1开始
     * @private
     */
    _updateBetHistoryTable(result, playerFilter = 'all', betFilter = 'all', resultFilter = 'all', actionFilter = 'all', page = 1) {
        const tbody = document.getElementById('simulation-bet-history-body');
        if (!tbody) return;

        // 清空表格内容
        tbody.innerHTML = '';

        // 筛选历史记录
        let filteredHistory = result.betHistory;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(bet => bet.playerIndex === playerIndex);
        }

        // 按下注金额筛选
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            filteredHistory = filteredHistory.filter(bet => bet.bet === betAmount);
        }

        // 按结果筛选
        if (resultFilter !== 'all') {
            filteredHistory = filteredHistory.filter(bet => bet.result === resultFilter);
        }

        // 按操作类型筛选
        if (actionFilter !== 'all') {
            switch (actionFilter) {
                case 'double':
                    filteredHistory = filteredHistory.filter(bet => bet.isDoubleDown === true);
                    break;
                case 'split':
                    // 修正分牌筛选：同一玩家同一局有多手牌时，筛选所有 isSplit 为 true 的手牌
                    const splitGrouped = {};
                    filteredHistory.forEach(bet => {
                        const key = `${bet.game}-${bet.playerIndex}`;
                        if (!splitGrouped[key]) splitGrouped[key] = [];
                        splitGrouped[key].push(bet);
                    });
                    filteredHistory = Object.values(splitGrouped).flatMap(records => {
                        const hasSplit = records.length > 1 && records.some(r => r.isSplit === true);
                        if (!hasSplit) return [];
                        return records.filter(r => r.isSplit === true);
                    });
                    break;
                case 'surrender':
                    filteredHistory = filteredHistory.filter(bet => bet.result === 'surrender');
                    break;
                case 'normal':
                    filteredHistory = filteredHistory.filter(bet =>
                        !bet.isDoubleDown && !bet.isSplit && bet.result !== 'surrender');
                    break;
            }
        }

        // 添加筛选后的下注记录，按游戏局数正序排列（从局号1开始）
        const sortedBets = filteredHistory.slice().sort((a, b) => a.game - b.game);

        // 分页设置
        const itemsPerPage = 100; // 每页显示100条记录
        const totalItems = sortedBets.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);

        // 确保页码在有效范围内
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));

        // 计算当前页的数据范围
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

        // 获取当前页的数据
        const currentPageData = sortedBets.slice(startIndex, endIndex);

        // 跟踪每个玩家的剩余筹码
        const playerChips = {};

        // 初始化每个玩家的筹码数
        if (result.initialChips) {
            // 计算当前页之前的所有筹码变化
            for (let i = 0; i < startIndex; i++) {
                const bet = sortedBets[i];
                if (!playerChips[bet.playerIndex]) {
                    playerChips[bet.playerIndex] = result.initialChips;
                }
                playerChips[bet.playerIndex] += bet.profit;
            }

            // 确保当前页所有玩家都有初始筹码
            currentPageData.forEach(bet => {
                if (!playerChips[bet.playerIndex]) {
                    playerChips[bet.playerIndex] = result.initialChips;
                }
            });
        }

        // 按游戏局号分组记录
        const gameGroups = {};
        currentPageData.forEach(bet => {
            if (!gameGroups[bet.game]) {
                gameGroups[bet.game] = [];
            }
            gameGroups[bet.game].push(bet);
        });

        // 处理每一局游戏
        Object.keys(gameGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(gameNumber => {
            const bets = gameGroups[gameNumber];

            // 检查是否为分牌情况
            const isSplitHand = bets.length > 1 && bets.some(bet => bet.handIndex > 0);

            // 对于每一局游戏，按手牌索引排序
            bets.sort((a, b) => a.handIndex - b.handIndex);

            // 处理每一手牌
            bets.forEach((bet, betIndex) => {
                const row = document.createElement('tr');

                // 如果是分牌情况，为行添加特殊样式
                if (isSplitHand) {
                    row.classList.add('split-hand-row');

                    // 第一手牌添加顶部边框
                    if (betIndex === 0) {
                        row.classList.add('first-split-hand');
                    }

                    // 最后一手牌添加底部边框
                    if (betIndex === bets.length - 1) {
                        row.classList.add('last-split-hand');
                    }
                }

                // 局号 - 简化为只显示局号
                const gameCell = document.createElement('td');
                gameCell.textContent = bet.game;
                gameCell.style.textAlign = 'center';
                row.appendChild(gameCell);

                // 玩家标识
                const playerIdentCell = document.createElement('td');

                // 创建玩家标识容器，使用flex布局
                const playerIdentContainer = document.createElement('div');
                playerIdentContainer.style.display = 'flex';
                playerIdentContainer.style.alignItems = 'center';
                playerIdentContainer.style.justifyContent = 'flex-start';
                playerIdentContainer.style.width = '100%';
                playerIdentContainer.style.flexWrap = 'nowrap'; // 防止内容换行

                // 添加玩家标识文本
                const playerIdentText = document.createElement('span');
                playerIdentText.textContent = `玩家${bet.playerIndex + 1}`;
                playerIdentText.style.marginRight = '4px';
                playerIdentContainer.appendChild(playerIdentText);

                // 如果是分牌情况，添加手牌标识
                if (isSplitHand) {
                    const handIndicator = document.createElement('span');
                    handIndicator.className = 'hand-indicator';
                    handIndicator.title = `分牌手牌 ${bet.handIndex + 1}`; // 添加悬停提示
                    handIndicator.textContent = `手牌${bet.handIndex + 1}`;  // 使用完整的"手牌1"、"手牌2"标识
                    handIndicator.style.fontSize = '0.85em'; // 增大字体
                    handIndicator.style.color = '#60a5fa';
                    handIndicator.style.fontWeight = 'bold';
                    handIndicator.style.padding = '2px 4px'; // 增加内边距
                    handIndicator.style.backgroundColor = 'rgba(96, 165, 250, 0.1)';
                    handIndicator.style.borderRadius = '3px';
                    handIndicator.style.whiteSpace = 'nowrap';  // 防止文本换行
                    handIndicator.style.display = 'inline-block';  // 确保标签作为块元素
                    handIndicator.style.flexShrink = '0';  // 防止标签被压缩
                    playerIdentContainer.appendChild(handIndicator);
                }

                playerIdentCell.appendChild(playerIdentContainer);
                row.appendChild(playerIdentCell);

                // 下注金额
                const betCell = document.createElement('td');
                betCell.textContent = bet.bet;
                row.appendChild(betCell);

                // 真数
                const trueCountCell = document.createElement('td');
                trueCountCell.textContent = bet.trueCount !== undefined ? bet.trueCount.toFixed(1) : '-';
                row.appendChild(trueCountCell);

                // 结果
                const resultCell = document.createElement('td');
                let resultText = {
                    'win': '赢',
                    'lose': '输',
                    'push': '和',
                    'blackjack': '黑杰克',
                    'bust': '爆牌',
                    'surrender': '投降'
                }[bet.result] || bet.result;

                // 添加加倍标识到结果文本中
                if (bet.isDoubleDown) {
                    resultText += '【加倍】';
                }

                // 设置结果文本
                resultCell.textContent = resultText;

                // 设置不同结果的颜色
                if (bet.result === 'win' || bet.result === 'blackjack') {
                    resultCell.style.color = '#34d399'; // 绿色
                } else if (bet.result === 'lose' || bet.result === 'bust' || bet.result === 'surrender') {
                    resultCell.style.color = '#f87171'; // 红色
                } else if (bet.result === 'push') {
                    resultCell.style.color = '#60a5fa'; // 蓝色
                }

                row.appendChild(resultCell);

                // 玩家手牌
                const playerHandCell = document.createElement('td');
                if (bet.playerCards && bet.playerCards.length > 0) {
                    // 显示玩家手牌，包括操作过程
                    let handHtml = '';

                    // 如果有多份手牌（分牌情况）但我们只显示当前手牌
                    if (Array.isArray(bet.playerCards[0]) && isSplitHand) {
                        // 只显示当前手牌索引的手牌
                        if (bet.handIndex < bet.playerCards.length) {
                            handHtml = this._formatCardSequence(bet.playerCards[bet.handIndex]);
                        } else {
                            handHtml = '-';
                        }
                    } else if (Array.isArray(bet.playerCards[0])) {
                        // 如果是旧格式的分牌记录，显示所有手牌
                        bet.playerCards.forEach((hand, idx) => {
                            if (idx > 0) handHtml += '<br>';
                            handHtml += `手牌${idx+1}: ${this._formatCardSequence(hand)}`;
                        });
                    } else {
                        handHtml = this._formatCardSequence(bet.playerCards);
                    }

                    playerHandCell.innerHTML = handHtml;
                } else {
                    playerHandCell.textContent = '-';
                }
                row.appendChild(playerHandCell);

                // 庄家手牌
                const dealerHandCell = document.createElement('td');
                if (bet.dealerCards && bet.dealerCards.length > 0) {
                    dealerHandCell.innerHTML = this._formatCardSequence(bet.dealerCards);
                } else {
                    dealerHandCell.textContent = '-';
                }
                row.appendChild(dealerHandCell);

                // 盈亏 - 添加颜色区分
                const profitCell = document.createElement('td');
                profitCell.textContent = bet.profit > 0 ? '+' + bet.profit : bet.profit;
                profitCell.style.color = bet.profit > 0 ? '#34d399' : bet.profit < 0 ? '#f87171' : '';
                row.appendChild(profitCell);

                // 更新玩家筹码
                if (playerChips[bet.playerIndex] !== undefined) {
                    playerChips[bet.playerIndex] += bet.profit;
                }

                // 剩余筹码
                const chipsCell = document.createElement('td');
                chipsCell.textContent = playerChips[bet.playerIndex] !== undefined ?
                    playerChips[bet.playerIndex].toLocaleString() : '-';
                row.appendChild(chipsCell);

                tbody.appendChild(row);
            });
        });

        // 更新分页控件
        this._updatePagination(result, playerFilter, betFilter, resultFilter, actionFilter, currentPage, totalPages, totalItems);

        // 更新统计信息
        this._updateBetHistoryStats(result, playerFilter, betFilter, resultFilter, actionFilter);

        // 更新玩家总数据
        this._updatePlayerTotalStats(result, playerFilter, betFilter, resultFilter, actionFilter);
    }

    /**
     * 更新分页控件
     * @param {SimulationResult} result - 模拟结果
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     * @param {number} currentPage - 当前页码
     * @param {number} totalPages - 总页数
     * @param {number} totalItems - 总记录数
     * @private
     */
    _updatePagination(result, playerFilter, betFilter, resultFilter, actionFilter, currentPage, totalPages, totalItems) {
        // 查找或创建分页容器
        let paginationContainer = document.getElementById('simulation-pagination');
        if (!paginationContainer) {
            paginationContainer = document.createElement('div');
            paginationContainer.id = 'simulation-pagination';
            paginationContainer.className = 'simulation-pagination';

            // 将分页容器添加到表格后面
            const historyTable = document.getElementById('simulation-bet-history-table');
            if (historyTable && historyTable.parentNode) {
                historyTable.parentNode.insertBefore(paginationContainer, historyTable.nextSibling);
            }
        }

        // 清空分页容器
        paginationContainer.innerHTML = '';

        // 如果没有数据或只有一页，不显示分页控件
        if (totalItems <= 0 || totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // 创建首页按钮
        const firstPageButton = document.createElement('button');
        firstPageButton.className = 'simulation-pagination-button';
        firstPageButton.textContent = '首页';
        firstPageButton.disabled = currentPage === 1;
        firstPageButton.addEventListener('click', () => {
            this._updateBetHistoryTable(result, playerFilter, betFilter, resultFilter, actionFilter, 1);
        });
        paginationContainer.appendChild(firstPageButton);

        // 创建上一页按钮
        const prevPageButton = document.createElement('button');
        prevPageButton.className = 'simulation-pagination-button';
        prevPageButton.textContent = '上一页';
        prevPageButton.disabled = currentPage === 1;
        prevPageButton.addEventListener('click', () => {
            this._updateBetHistoryTable(result, playerFilter, betFilter, resultFilter, actionFilter, currentPage - 1);
        });
        paginationContainer.appendChild(prevPageButton);

        // 创建页码信息
        const pageInfo = document.createElement('span');
        pageInfo.className = 'simulation-pagination-info';
        pageInfo.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
        paginationContainer.appendChild(pageInfo);

        // 创建下一页按钮
        const nextPageButton = document.createElement('button');
        nextPageButton.className = 'simulation-pagination-button';
        nextPageButton.textContent = '下一页';
        nextPageButton.disabled = currentPage === totalPages;
        nextPageButton.addEventListener('click', () => {
            this._updateBetHistoryTable(result, playerFilter, betFilter, resultFilter, actionFilter, currentPage + 1);
        });
        paginationContainer.appendChild(nextPageButton);

        // 创建尾页按钮
        const lastPageButton = document.createElement('button');
        lastPageButton.className = 'simulation-pagination-button';
        lastPageButton.textContent = '尾页';
        lastPageButton.disabled = currentPage === totalPages;
        lastPageButton.addEventListener('click', () => {
            this._updateBetHistoryTable(result, playerFilter, betFilter, resultFilter, actionFilter, totalPages);
        });
        paginationContainer.appendChild(lastPageButton);
    }

    /**
     * 格式化牌序列，显示操作过程
     * @param {Array} cards - 牌数组
     * @returns {string} 格式化后的牌序列HTML
     * @private
     */
    _formatCardSequence(cards) {
        if (!cards || !Array.isArray(cards) || cards.length === 0) return '-';

        // 将牌转换为带花色的表示
        const formattedCards = cards.map(card => {
            if (typeof card === 'string') {
                // 如果已经是字符串格式，直接返回
                return card;
            } else if (card && card.rank && card.suit) {
                // 如果是对象格式，转换为字符串
                const suitSymbol = {
                    'hearts': '♥',
                    'diamonds': '♦',
                    'clubs': '♣',
                    'spades': '♠',
                    'h': '♥',
                    'd': '♦',
                    'c': '♣',
                    's': '♠'
                }[card.suit.toLowerCase()] || card.suit;

                return `${card.rank}${suitSymbol}`;
            } else {
                return '?';
            }
        });

        // 如果只有一张或两张牌，直接显示
        if (formattedCards.length <= 2) {
            return formattedCards.join(' ');
        }

        // 显示操作过程：前两张牌 → 第三张牌 → 第四张牌...
        let result = `${formattedCards[0]} ${formattedCards[1]}`;
        for (let i = 2; i < formattedCards.length; i++) {
            result += ` → ${formattedCards[i]}`;
        }

        return result;
    }

    /**
     * 更新玩家总数据统计
     * @param {SimulationResult} result - 模拟结果
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     * @private
     */
    _updatePlayerTotalStats(result, playerFilter = 'all', betFilter = 'all', resultFilter = 'all', actionFilter = 'all') {
        // 获取玩家总数据统计区域
        const playerTotalStatsDiv = document.getElementById('player-total-stats');
        if (!playerTotalStatsDiv) return;

        // 清空区域内容
        playerTotalStatsDiv.innerHTML = '';

        // 创建总数据表格
        const totalStatsTable = document.createElement('table');
        totalStatsTable.className = 'simulation-total-stats-table';

        // 创建表头
        const totalStatsHeader = document.createElement('thead');
        const totalStatsHeaderRow = document.createElement('tr');
        ['总局数', '胜率', '赢局数', '输局数', '平局数', '黑杰克', '总盈亏', '平均下注'].forEach(text => {
            const th = document.createElement('th');
            th.textContent = text;
            totalStatsHeaderRow.appendChild(th);
        });
        totalStatsHeader.appendChild(totalStatsHeaderRow);
        totalStatsTable.appendChild(totalStatsHeader);

        // 创建表体
        const totalStatsBody = document.createElement('tbody');
        totalStatsBody.id = 'player-total-stats-body';
        totalStatsTable.appendChild(totalStatsBody);

        playerTotalStatsDiv.appendChild(totalStatsTable);

        // 筛选历史记录
        let filteredHistory = result.betHistory;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(bet => bet.playerIndex === playerIndex);
        }

        // 按下注金额筛选
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            filteredHistory = filteredHistory.filter(bet => bet.bet === betAmount);
        }

        // 按结果筛选
        if (resultFilter !== 'all') {
            filteredHistory = filteredHistory.filter(bet => bet.result === resultFilter);
        }

        // 按操作类型筛选
        if (actionFilter !== 'all') {
            switch (actionFilter) {
                case 'double':
                    filteredHistory = filteredHistory.filter(bet => bet.isDoubleDown === true);
                    break;
                case 'split':
                    filteredHistory = filteredHistory.filter(bet => bet.isSplit === true);
                    break;
                case 'surrender':
                    filteredHistory = filteredHistory.filter(bet => bet.result === 'surrender');
                    break;
                case 'normal':
                    filteredHistory = filteredHistory.filter(bet =>
                        !bet.isDoubleDown && !bet.isSplit && bet.result !== 'surrender');
                    break;
            }
        }

        // 如果没有记录，显示提示
        if (filteredHistory.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 8;
            cell.textContent = '没有符合条件的记录';
            cell.style.textAlign = 'center';
            row.appendChild(cell);
            totalStatsBody.appendChild(row);
            return;
        }

        // 计算统计数据
        const totalGames = filteredHistory.length;
        let totalProfit = 0;
        let winCount = 0;
        let loseCount = 0;
        let pushCount = 0;
        let blackjackCount = 0;
        let totalBet = 0;

        filteredHistory.forEach(bet => {
            totalProfit += bet.profit;
            totalBet += bet.bet;

            if (bet.result === 'win' || bet.result === 'blackjack') {
                winCount++;
                if (bet.result === 'blackjack') {
                    blackjackCount++;
                }
            } else if (bet.result === 'lose' || bet.result === 'bust' || bet.result === 'surrender') {
                loseCount++;
            } else if (bet.result === 'push') {
                pushCount++;
            }
        });

        // 计算胜率
        const winRate = totalGames > 0 ? (winCount / totalGames) * 100 : 0;
        const avgBet = totalGames > 0 ? Math.round(totalBet / totalGames) : 0;

        // 创建数据行
        const dataRow = document.createElement('tr');

        // 总局数
        const totalGamesCell = document.createElement('td');
        totalGamesCell.textContent = totalGames;
        dataRow.appendChild(totalGamesCell);

        // 胜率
        const winRateCell = document.createElement('td');
        winRateCell.textContent = winRate.toFixed(1) + '%';
        winRateCell.style.color = winRate > 50 ? '#34d399' : winRate < 40 ? '#f87171' : '';
        dataRow.appendChild(winRateCell);

        // 赢局数
        const winCountCell = document.createElement('td');
        winCountCell.textContent = winCount;
        winCountCell.style.color = '#34d399';
        dataRow.appendChild(winCountCell);

        // 输局数
        const loseCountCell = document.createElement('td');
        loseCountCell.textContent = loseCount;
        loseCountCell.style.color = '#f87171';
        dataRow.appendChild(loseCountCell);

        // 平局数
        const pushCountCell = document.createElement('td');
        pushCountCell.textContent = pushCount;
        dataRow.appendChild(pushCountCell);

        // 黑杰克
        const blackjackCountCell = document.createElement('td');
        blackjackCountCell.textContent = blackjackCount;
        blackjackCountCell.style.color = blackjackCount > 0 ? '#34d399' : '';
        dataRow.appendChild(blackjackCountCell);

        // 总盈亏
        const totalProfitCell = document.createElement('td');
        totalProfitCell.textContent = totalProfit > 0 ? '+' + totalProfit : totalProfit;
        totalProfitCell.style.color = totalProfit > 0 ? '#34d399' : (totalProfit < 0 ? '#f87171' : '');
        dataRow.appendChild(totalProfitCell);

        // 平均下注
        const avgBetCell = document.createElement('td');
        avgBetCell.textContent = avgBet;
        dataRow.appendChild(avgBetCell);

        totalStatsBody.appendChild(dataRow);
    }

    /**
     * 更新下注历史统计信息
     * @param {SimulationResult} result - 模拟结果
     * @param {string|number} playerFilter - 玩家筛选条件
     * @param {string|number} betFilter - 下注金额筛选条件
     * @param {string} resultFilter - 结果筛选条件
     * @param {string} actionFilter - 操作类型筛选条件
     * @private
     */
    _updateBetHistoryStats(result, playerFilter = 'all', betFilter = 'all', resultFilter = 'all', actionFilter = 'all') {
        const statsDiv = document.getElementById('history-stats');
        if (!statsDiv) return;

        // 清空统计区域
        statsDiv.innerHTML = '';

        // 筛选历史记录
        let filteredHistory = result.betHistory;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(bet => bet.playerIndex === playerIndex);
        }

        // 按下注金额筛选
        if (betFilter !== 'all') {
            const betAmount = parseInt(betFilter);
            filteredHistory = filteredHistory.filter(bet => bet.bet === betAmount);
        }

        // 按结果筛选
        if (resultFilter !== 'all') {
            filteredHistory = filteredHistory.filter(bet => bet.result === resultFilter);
        }

        // 按操作类型筛选
        if (actionFilter !== 'all') {
            switch (actionFilter) {
                case 'double':
                    filteredHistory = filteredHistory.filter(bet => bet.isDoubleDown === true);
                    break;
                case 'split':
                    filteredHistory = filteredHistory.filter(bet => bet.isSplit === true);
                    break;
                case 'surrender':
                    filteredHistory = filteredHistory.filter(bet => bet.result === 'surrender');
                    break;
                case 'normal':
                    filteredHistory = filteredHistory.filter(bet =>
                        !bet.isDoubleDown && !bet.isSplit && bet.result !== 'surrender');
                    break;
            }
        }

        // 创建统计标题
        const statsTitle = document.createElement('h4');
        statsTitle.textContent = '显示记录统计';
        statsTitle.style.margin = '10px 0';
        statsTitle.style.padding = '5px 10px';
        statsTitle.style.borderBottom = '1px solid #3e4c63';
        statsTitle.style.color = '#38bdf8';
        statsDiv.appendChild(statsTitle);

        // 如果没有记录，显示提示
        if (filteredHistory.length === 0) {
            const noDataMsg = document.createElement('div');
            noDataMsg.className = 'simulation-no-data';
            noDataMsg.style.height = '40px';
            noDataMsg.textContent = '没有符合条件的记录';
            statsDiv.appendChild(noDataMsg);
            return;
        }

        // 计算统计数据
        const totalGames = filteredHistory.length;
        let totalProfit = 0;
        let winCount = 0;
        let loseCount = 0;
        let pushCount = 0;
        let blackjackCount = 0;
        let totalBet = 0;

        filteredHistory.forEach(bet => {
            totalProfit += bet.profit;
            totalBet += bet.bet;

            if (bet.result === 'win' || bet.result === 'blackjack') {
                winCount++;
                if (bet.result === 'blackjack') {
                    blackjackCount++;
                }
            } else if (bet.result === 'lose' || bet.result === 'bust' || bet.result === 'surrender') {
                loseCount++;
            } else if (bet.result === 'push') {
                pushCount++;
            }
        });

        // 计算胜率
        const winRate = totalGames > 0 ? (winCount / totalGames) * 100 : 0;
        const avgBet = totalGames > 0 ? Math.round(totalBet / totalGames) : 0;

        // 创建统计表格
        const statsTable = document.createElement('table');
        statsTable.className = 'simulation-overall-stats-table';
        statsTable.style.tableLayout = 'fixed'; // 强制固定表格布局

        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        const headers = ['总局数', '胜率', '赢局数', '输局数', '平局数', '黑杰克', '总盈亏', '平均下注'];
        headers.forEach((header, index) => {
            const th = document.createElement('th');
            th.textContent = header;
            th.style.width = '12.5%'; // 强制每列宽度相等
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        statsTable.appendChild(thead);

        // 创建表身
        const tbody = document.createElement('tbody');
        const dataRow = document.createElement('tr');

        // 总局数
        const totalGamesCell = document.createElement('td');
        totalGamesCell.textContent = totalGames;
        totalGamesCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(totalGamesCell);

        // 胜率
        const winRateCell = document.createElement('td');
        winRateCell.textContent = winRate.toFixed(1) + '%';
        winRateCell.style.color = winRate > 50 ? '#34d399' : winRate < 40 ? '#f87171' : '';
        winRateCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(winRateCell);

        // 赢局数
        const winCountCell = document.createElement('td');
        winCountCell.textContent = winCount;
        winCountCell.className = 'simulation-history-stat-cell positive';
        winCountCell.style.color = '#34d399';
        winCountCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(winCountCell);

        // 输局数
        const loseCountCell = document.createElement('td');
        loseCountCell.textContent = loseCount;
        loseCountCell.className = 'simulation-history-stat-cell negative';
        loseCountCell.style.color = '#f87171';
        loseCountCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(loseCountCell);

        // 平局数
        const pushCountCell = document.createElement('td');
        pushCountCell.textContent = pushCount;
        pushCountCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(pushCountCell);

        // 黑杰克
        const blackjackCountCell = document.createElement('td');
        blackjackCountCell.textContent = blackjackCount;
        blackjackCountCell.style.color = blackjackCount > 0 ? '#34d399' : '';
        blackjackCountCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(blackjackCountCell);

        // 总盈亏
        const totalProfitCell = document.createElement('td');
        totalProfitCell.textContent = totalProfit > 0 ? '+' + totalProfit : totalProfit;
        totalProfitCell.className = 'simulation-history-stat-cell ' + (totalProfit > 0 ? 'positive' : 'negative');
        totalProfitCell.style.color = totalProfit > 0 ? '#34d399' : (totalProfit < 0 ? '#f87171' : '');
        totalProfitCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(totalProfitCell);

        // 平均下注
        const avgBetCell = document.createElement('td');
        avgBetCell.textContent = avgBet;
        avgBetCell.style.width = '12.5%'; // 强制每列宽度相等
        dataRow.appendChild(avgBetCell);

        tbody.appendChild(dataRow);
        statsTable.appendChild(tbody);

        statsDiv.appendChild(statsTable);
    }

    /**
     * 更新玩家统计信息
     * @param {SimulationResult} result - 模拟结果
     * @param {string|number} playerFilter - 玩家筛选条件，'all'表示所有玩家
     * @private
     */
    _updatePlayerStats(result, playerFilter = 'all') {
        const statsContentDiv = document.getElementById('player-stats-content');
        if (!statsContentDiv) return;

        // 清空统计内容
        statsContentDiv.innerHTML = '';

        // 如果没有历史记录，显示提示
        if (!result.betHistory || result.betHistory.length === 0) {
            const noDataMsg = document.createElement('div');
            noDataMsg.className = 'simulation-no-data';
            noDataMsg.textContent = '没有下注历史记录，无法生成玩家统计';
            statsContentDiv.appendChild(noDataMsg);
            return;
        }

        // 记录当前筛选条件
        console.log(`更新玩家统计，筛选条件: playerFilter=${playerFilter}`);

        // 筛选历史记录
        let filteredHistory = result.betHistory;

        // 按玩家筛选
        if (playerFilter !== 'all') {
            const playerIndex = parseInt(playerFilter);
            filteredHistory = filteredHistory.filter(bet => bet.playerIndex === playerIndex);
        }

        // 如果筛选后没有数据，显示提示
        if (filteredHistory.length === 0) {
            const noDataMsg = document.createElement('div');
            noDataMsg.className = 'simulation-no-data';
            noDataMsg.textContent = '没有符合条件的玩家数据';
            statsContentDiv.appendChild(noDataMsg);
            return;
        }

        // 计算统计数据
        const stats = {
            // 总体统计
            totalGames: filteredHistory.length,
            totalBetAmount: 0,
            totalWinAmount: 0,
            totalLossAmount: 0,

            // 游戏结果
            winCount: 0,
            loseCount: 0,
            pushCount: 0,
            blackjackCount: 0,

            // 游戏操作
            doubleCount: 0,
            doubleWinCount: 0,
            splitCount: 0,
            surrenderCount: 0,

            // 记录数据
            largestWin: 0,
            largestLoss: 0,
            longestWinStreak: 0,
            longestLoseStreak: 0,
            highestChips: result.initialChips,
            lowestChips: result.initialChips,
            averageBet: 0,
            winRate: 0,

            // 临时计算用
            currentWinStreak: 0,
            currentLoseStreak: 0,
            currentChips: result.initialChips
        };

        // 排序历史记录，按游戏局数排序
        const sortedHistory = filteredHistory.slice().sort((a, b) => a.game - b.game);

        // 这个变量已不再使用，因为我们现在按照分牌动作计算分牌次数

        // 计算统计数据
        sortedHistory.forEach(bet => {
            // 下注金额
            stats.totalBetAmount += bet.bet;

            // 处理结果
            switch (bet.result) {
                case 'win':
                    stats.winCount++;
                    stats.totalWinAmount += bet.profit;
                    stats.largestWin = Math.max(stats.largestWin, bet.profit);
                    stats.currentWinStreak++;
                    stats.currentLoseStreak = 0;
                    break;

                case 'blackjack':
                    stats.winCount++;
                    stats.blackjackCount++;
                    stats.totalWinAmount += bet.profit;
                    stats.largestWin = Math.max(stats.largestWin, bet.profit);
                    stats.currentWinStreak++;
                    stats.currentLoseStreak = 0;
                    break;

                case 'lose':
                case 'bust':
                    stats.loseCount++;
                    stats.totalLossAmount += Math.abs(bet.profit);
                    stats.largestLoss = Math.max(stats.largestLoss, Math.abs(bet.profit));
                    stats.currentLoseStreak++;
                    stats.currentWinStreak = 0;
                    break;

                case 'surrender':
                    stats.loseCount++;
                    stats.surrenderCount++;
                    stats.totalLossAmount += Math.abs(bet.profit);
                    stats.currentLoseStreak++;
                    stats.currentWinStreak = 0;
                    break;

                case 'push':
                    stats.pushCount++;
                    stats.currentWinStreak = 0;
                    stats.currentLoseStreak = 0;
                    break;
            }

            // 更新连胜/连败记录
            stats.longestWinStreak = Math.max(stats.longestWinStreak, stats.currentWinStreak);
            stats.longestLoseStreak = Math.max(stats.longestLoseStreak, stats.currentLoseStreak);

            // 更新筹码记录
            stats.currentChips += bet.profit;
            stats.highestChips = Math.max(stats.highestChips, stats.currentChips);
            stats.lowestChips = Math.min(stats.lowestChips, stats.currentChips);

            // 识别加倍操作
            if (bet.bet > bet.originalBet) {
                stats.doubleCount++;
                if (bet.result === 'win' || bet.result === 'blackjack') {
                    stats.doubleWinCount++;
                }
            }

            // 分牌操作次数直接从 result.playerStats.splits 获取
            // 不再通过遍历 betHistory 和使用 splitGameIds 计算
            // if (bet.isSplit) {
            //     // 只有当这个局号的分牌操作还没被计算过时才增加计数
            //     if (!splitGameIds.has(bet.game || bet.gameId)) {
            //         // stats.splitCount++; // 旧的按局数计算的方式
            //         splitGameIds.add(bet.game || bet.gameId);
            //     }
            // }
        });

        // 根据筛选条件获取分牌次数
        if (playerFilter === 'all') {
            // 全部玩家的分牌次数 - 使用更精确的计算方法
            if (result.betHistory && result.betHistory.length > 0) {
                // 获取所有玩家的分牌记录
                const allSplitRecords = result.betHistory.filter(bet =>
                    bet.isSplit || bet.actionType === '分牌' || bet.isSplitAction);

                // 按玩家和游戏局ID分组，确保每个玩家在每局游戏中的分牌操作只计算一次
                const splitsByPlayerAndGame = {};

                allSplitRecords.forEach(bet => {
                    const key = `${bet.playerIndex}-${bet.game || bet.gameId}`;
                    if (!splitsByPlayerAndGame[key]) {
                        splitsByPlayerAndGame[key] = true;
                    }
                });

                // 计算所有玩家的分牌次数总和
                stats.splitCount = Object.keys(splitsByPlayerAndGame).length;
                console.log(`通过玩家索引和游戏局ID计算所有玩家的分牌次数总和: ${stats.splitCount}`);
            } else {
                // 如果没有下注历史记录，使用result.playerStats.splits
                stats.splitCount = result.playerStats && typeof result.playerStats.splits === 'number'
                                ? result.playerStats.splits
                                : 0;
                console.log(`使用result.playerStats.splits作为所有玩家的分牌次数: ${stats.splitCount}`);
            }
        } else {
            // 特定玩家的分牌次数 - 从筛选后的历史记录中计算
            // 按照分牌动作计算，不是按照游戏局计算
            let playerSplitCount = 0;

            // 检查历史记录中是否有分牌操作

            filteredHistory.forEach(bet => {
                // 优先使用actionType字段判断分牌动作
                if (bet.actionType === '分牌') {
                    playerSplitCount++;
                    console.log(`计算分牌次数: 玩家${bet.playerIndex}执行分牌动作，游戏局ID: ${bet.game || bet.gameId}`);
                }
                // 兼容旧数据，如果没有actionType字段，则使用isSplitAction判断
                else if (bet.isSplitAction) {
                    playerSplitCount++;
                    console.log(`计算分牌次数: 玩家${bet.playerIndex}执行分牌动作(isSplitAction)，游戏局ID: ${bet.game || bet.gameId}`);
                }
                // 检查是否有isSplit标记，这可能表示这是一个分牌手牌
                else if (bet.isSplit) {
                    console.log(`检测到分牌手牌: 玩家${bet.playerIndex}，游戏局ID: ${bet.game || bet.gameId}，手牌索引: ${bet.handIndex}`);
                }
            });

            // 如果在历史记录中没有找到分牌动作，但是筛选的是单个玩家，并且全部玩家有分牌操作
            // 那么可能是这个玩家确实执行了分牌操作，但是在历史记录中没有正确标记
            if (playerSplitCount === 0 && playerFilter !== 'all') {
                // 首先检查这个玩家是否有分牌手牌（isSplit=true）
                const splitHands = filteredHistory.filter(bet => bet.isSplit);
                if (splitHands.length > 0) {
                    // 统计不同的游戏局ID
                    const uniqueGameIds = new Set(splitHands.map(bet => bet.game || bet.gameId));
                    playerSplitCount = uniqueGameIds.size;
                    console.log(`通过分牌手牌计算分牌次数: 玩家${playerFilter}有${splitHands.length}个分牌手牌，涉及${playerSplitCount}个不同的游戏局`);
                }

                // 如果仍然没有找到分牌次数，但全局有分牌操作，则使用全局分牌次数
                if (playerSplitCount === 0 && result.playerStats && typeof result.playerStats.splits === 'number' && result.playerStats.splits > 0) {
                    // 如果只有一个玩家，那么全部分牌次数就是这个玩家的分牌次数
                    if (result.playerCount === 1) {
                        playerSplitCount = result.playerStats.splits;
                        console.log(`使用全局分牌次数作为玩家${playerFilter}的分牌次数: ${playerSplitCount}`);
                    }
                    // 否则，尝试根据玩家索引和游戏局ID更精确地计算分牌次数
                    else if (result.betHistory) {
                        // 获取所有玩家的分牌记录
                        const allSplitRecords = result.betHistory.filter(bet =>
                            bet.isSplit || bet.actionType === '分牌' || bet.isSplitAction);

                        // 按玩家和游戏局ID分组，确保每个玩家在每局游戏中的分牌操作只计算一次
                        const splitsByPlayerAndGame = {};

                        allSplitRecords.forEach(bet => {
                            const key = `${bet.playerIndex}-${bet.game || bet.gameId}`;
                            if (!splitsByPlayerAndGame[key]) {
                                splitsByPlayerAndGame[key] = true;
                            }
                        });

                        // 计算当前筛选玩家的分牌次数
                        const currentPlayerFilter = parseInt(playerFilter);
                        const currentPlayerSplitKeys = Object.keys(splitsByPlayerAndGame).filter(key =>
                            key.startsWith(`${currentPlayerFilter}-`));

                        playerSplitCount = currentPlayerSplitKeys.length;
                        console.log(`通过玩家索引和游戏局ID计算分牌次数: 玩家${playerFilter}的分牌次数: ${playerSplitCount}`);
                    }
                }
            }

            stats.splitCount = playerSplitCount;
            console.log(`玩家${playerFilter}的分牌次数: ${playerSplitCount}`);
        }

        // 计算平均下注和胜率
        stats.averageBet = stats.totalGames > 0 ? Math.round(stats.totalBetAmount / stats.totalGames) : 0;
        stats.winRate = stats.totalGames > 0 ? (stats.winCount / stats.totalGames * 100).toFixed(2) : 0;

        // 新增计算：总盈亏 和 玩家优势
        const playerNetProfit = (stats.totalWinAmount || 0) - (stats.totalLossAmount || 0);
        const playerEdge = (stats.totalBetAmount && stats.totalBetAmount !== 0) ? (playerNetProfit / stats.totalBetAmount) * 100 : 0;

        // 创建统计容器 - 使用网格布局
        const statsContainer = document.createElement('div');
        statsContainer.className = 'player-stats-container';

        // 1. 游戏结果区域 - 左上
        const resultStatsSection = document.createElement('div');
        resultStatsSection.className = 'player-stats-section';

        const resultStatsTitle = document.createElement('h4');
        resultStatsTitle.className = 'player-stats-title';
        resultStatsTitle.textContent = '游戏结果';
        resultStatsSection.appendChild(resultStatsTitle);

        const resultStatsGrid = document.createElement('div');
        resultStatsGrid.className = 'player-stats-grid';

        // 添加游戏结果卡片
        this.addStatsCard(resultStatsGrid, '获胜次数', stats.winCount.toLocaleString(), 'positive');
        this.addStatsCard(resultStatsGrid, '失败次数', stats.loseCount.toLocaleString(), 'negative');
        this.addStatsCard(resultStatsGrid, '平局次数', stats.pushCount.toLocaleString());
        this.addStatsCard(resultStatsGrid, '黑杰克次数', stats.blackjackCount.toLocaleString(), stats.blackjackCount > 0 ? 'positive' : '');

        resultStatsSection.appendChild(resultStatsGrid);
        statsContainer.appendChild(resultStatsSection);

        // 2. 游戏操作区域 - 右上
        const operationStatsSection = document.createElement('div');
        operationStatsSection.className = 'player-stats-section';

        const operationStatsTitle = document.createElement('h4');
        operationStatsTitle.className = 'player-stats-title';
        operationStatsTitle.textContent = '游戏操作';
        operationStatsSection.appendChild(operationStatsTitle);

        const operationStatsGrid = document.createElement('div');
        operationStatsGrid.className = 'player-stats-grid';

        // 添加游戏操作卡片
        this.addStatsCard(operationStatsGrid, '加倍次数', stats.doubleCount.toLocaleString());
        this.addStatsCard(operationStatsGrid, '加倍获胜次数', stats.doubleWinCount.toLocaleString(), stats.doubleWinCount > 0 ? 'positive' : '');
        this.addStatsCard(operationStatsGrid, '分牌次数', stats.splitCount.toLocaleString());
        this.addStatsCard(operationStatsGrid, '投降次数', stats.surrenderCount.toLocaleString());

        operationStatsSection.appendChild(operationStatsGrid);
        statsContainer.appendChild(operationStatsSection);

        // 3. 总体统计区域 - 左下
        const totalStatsSection = document.createElement('div');
        totalStatsSection.className = 'player-stats-section';

        const totalStatsTitle = document.createElement('h4');
        totalStatsTitle.className = 'player-stats-title';
        totalStatsTitle.textContent = '总体统计';
        totalStatsSection.appendChild(totalStatsTitle);

        const totalStatsGrid = document.createElement('div');
        totalStatsGrid.className = 'player-stats-grid';

        // 修改总体统计网格为1x4布局
        totalStatsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';

        // 添加总体统计卡片
        this.addStatsCard(totalStatsGrid, '总游戏局数', stats.totalGames.toLocaleString());
        this.addStatsCard(totalStatsGrid, '总下注金额', stats.totalBetAmount.toLocaleString());
        // this.addStatsCard(totalStatsGrid, '总赢取金额', stats.totalWinAmount.toLocaleString(), 'positive'); // 移除
        // this.addStatsCard(totalStatsGrid, '总损失金额', stats.totalLossAmount.toLocaleString(), 'negative'); // 移除

        // 新增：总盈亏
        this.addStatsCard(totalStatsGrid, '总盈亏', playerNetProfit.toLocaleString(), playerNetProfit >= 0 ? 'positive' : 'negative');
        // 新增：玩家优势
        this.addStatsCard(totalStatsGrid, '玩家优势', playerEdge.toFixed(2) + '%', playerEdge >= 0 ? 'positive' : 'negative');

        totalStatsSection.appendChild(totalStatsGrid);
        statsContainer.appendChild(totalStatsSection);

        // 4. 记录数据区域 - 底部跨越两列
        const recordStatsSection = document.createElement('div');
        recordStatsSection.className = 'player-stats-section';

        const recordStatsTitle = document.createElement('h4');
        recordStatsTitle.className = 'player-stats-title';
        recordStatsTitle.textContent = '记录数据';
        recordStatsSection.appendChild(recordStatsTitle);

        const recordStatsGrid = document.createElement('div');
        recordStatsGrid.className = 'player-stats-grid';

        // 添加记录数据卡片 - 使用4列布局
        this.addStatsCard(recordStatsGrid, '单局最大赢取', stats.largestWin.toLocaleString(), 'positive');
        this.addStatsCard(recordStatsGrid, '单局最大损失', stats.largestLoss.toLocaleString(), 'negative');
        this.addStatsCard(recordStatsGrid, '最长连胜', stats.longestWinStreak.toLocaleString(), stats.longestWinStreak > 0 ? 'positive' : '');
        this.addStatsCard(recordStatsGrid, '最长连败', stats.longestLoseStreak.toLocaleString(), stats.longestLoseStreak > 0 ? 'negative' : '');
        this.addStatsCard(recordStatsGrid, '最高筹码', stats.highestChips.toLocaleString(), 'positive');
        this.addStatsCard(recordStatsGrid, '最低筹码', stats.lowestChips.toLocaleString(), stats.lowestChips < result.initialChips ? 'negative' : '');
        this.addStatsCard(recordStatsGrid, '平均下注', stats.averageBet.toLocaleString());
        this.addStatsCard(recordStatsGrid, '获胜胜率', stats.winRate + '%', parseFloat(stats.winRate) > 50 ? 'positive' : parseFloat(stats.winRate) < 40 ? 'negative' : '');

        recordStatsSection.appendChild(recordStatsGrid);
        statsContainer.appendChild(recordStatsSection);

        // 添加到页面
        statsContentDiv.appendChild(statsContainer);
    }

    /**
     * 添加统计卡片
     * @param {HTMLElement} container - 容器元素
     * @param {string} title - 标题
     * @param {string} value - 值
     * @param {string} valueClass - 值的CSS类
     */
    addStatsCard(container, title, value, valueClass = '') {
        const card = document.createElement('div');
        card.className = 'player-stats-card';

        const titleElement = document.createElement('div');
        titleElement.className = 'player-stats-card-title';
        titleElement.textContent = title;
        card.appendChild(titleElement);

        const valueElement = document.createElement('div');
        valueElement.className = `player-stats-card-value ${valueClass}`;
        valueElement.textContent = value;

        // 设置颜色样式
        if (valueClass === 'positive') {
            valueElement.style.color = '#34d399';
        } else if (valueClass === 'negative') {
            valueElement.style.color = '#f87171';
        }

        card.appendChild(valueElement);
        container.appendChild(card);
    }

    /**
     * 添加统计表格行
     * @param {HTMLElement} table - 表格元素
     * @param {string} label - 标签
     * @param {string} value - 值
     * @param {string} valueClass - 值的CSS类
     */
    addStatsRow(table, label, value, valueClass = '') {
        const row = document.createElement('tr');

        const labelCell = document.createElement('td');
        labelCell.className = 'simulation-table-label';
        labelCell.textContent = label;
        row.appendChild(labelCell);

        const valueCell = document.createElement('td');
        valueCell.className = `simulation-table-value ${valueClass}`;
        valueCell.textContent = value;
        if (valueClass === 'positive') {
            valueCell.style.color = '#34d399';
        } else if (valueClass === 'negative') {
            valueCell.style.color = '#f87171';
        }
        row.appendChild(valueCell);

        table.appendChild(row);
    }

    /**
     * 计算玩家优势
     * 玩家优势 = 玩家期望收益 / 总下注金额 * 100%
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 玩家优势百分比
     * @private
     */
    _calculatePlayerEdge(result) {
        if (!result || !result.playerStats) return 0;

        const totalBet = result.playerStats.totalBetAmount;
        if (totalBet <= 0) return 0;

        const netProfit = result.netProfit;
        return (netProfit / totalBet) * 100;
    }

    /**
     * 计算回报率(RTP - Return To Player)
     * RTP = (总赢取金额 / 总下注金额) * 100%
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 回报率百分比
     * @private
     */
    _calculateRTP(result) {
        if (!result || !result.playerStats) return 0;

        const totalBet = result.playerStats.totalBetAmount;
        if (totalBet <= 0) return 0;

        // 总赢取金额 = 总下注金额 + 净盈亏
        const totalReturn = totalBet + result.netProfit;
        return (totalReturn / totalBet) * 100;
    }

    /**
     * 计算投资回报率(ROI - Return On Investment)
     * ROI = (净盈亏 / 初始投资) * 100%
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 投资回报率百分比
     * @private
     */
    _calculateROI(result) {
        if (!result || result.initialTotalChips <= 0) return 0;

        return (result.netProfit / result.initialTotalChips) * 100;
    }

    /**
     * 计算每手牌平均盈亏
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 每手牌平均盈亏
     * @private
     */
    _calculateProfitPerHand(result) {
        if (!result || !result.playerStats || result.playerStats.handsPlayed <= 0) return 0;

        return result.netProfit / result.playerStats.handsPlayed;
    }

    /**
     * 计算盈亏标准差
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 盈亏标准差
     * @private
     */
    _calculateProfitStandardDeviation(result) {
        if (!result || !result.betHistory || result.betHistory.length <= 1) return 0;

        // 计算平均盈亏
        const profits = result.betHistory.map(bet => bet.profit);
        const mean = profits.reduce((sum, profit) => sum + profit, 0) / profits.length;

        // 计算方差
        const variance = profits.reduce((sum, profit) => {
            const diff = profit - mean;
            return sum + diff * diff;
        }, 0) / profits.length;

        // 返回标准差
        return Math.sqrt(variance);
    }

    /**
     * 计算盈亏方差
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 盈亏方差
     * @private
     */
    _calculateProfitVariance(result) {
        if (!result || !result.betHistory || result.betHistory.length <= 1) return 0;

        // 计算平均盈亏
        const profits = result.betHistory.map(bet => bet.profit);
        const mean = profits.reduce((sum, profit) => sum + profit, 0) / profits.length;

        // 计算方差
        return profits.reduce((sum, profit) => {
            const diff = profit - mean;
            return sum + diff * diff;
        }, 0) / profits.length;
    }

    /**
     * 计算盈亏变异系数(CV - Coefficient of Variation)
     * CV = 标准差 / 平均值
     * @param {SimulationResult} result - 模拟结果
     * @returns {number} 盈亏变异系数
     * @private
     */
    _calculateCoefficientOfVariation(result) {
        if (!result || !result.betHistory || result.betHistory.length <= 1) return 0;

        // 计算平均盈亏
        const profits = result.betHistory.map(bet => bet.profit);
        const mean = profits.reduce((sum, profit) => sum + profit, 0) / profits.length;

        // 如果平均值接近0，返回0以避免除以0
        if (Math.abs(mean) < 0.0001) return 0;

        // 计算标准差
        const variance = profits.reduce((sum, profit) => {
            const diff = profit - mean;
            return sum + diff * diff;
        }, 0) / profits.length;
        const stdDev = Math.sqrt(variance);

        // 返回变异系数
        return stdDev / Math.abs(mean);
    }

    /**
     * 计算玩家平均点数
     * @param {Array} betHistory - 下注历史记录
     * @returns {string} 玩家平均点数（保留2位小数）
     * @private
     */
    _calculatePlayerAveragePoints(betHistory) {
        if (!betHistory || betHistory.length === 0) return '0.00';

        let totalPoints = 0;
        let validHandsCount = 0;

        betHistory.forEach(bet => {
            // 只计算有玩家点数记录的手牌
            if (bet.playerPoints !== undefined && bet.playerPoints !== null) {
                totalPoints += bet.playerPoints;
                validHandsCount++;
            }
        });

        if (validHandsCount === 0) return '0.00';
        return (totalPoints / validHandsCount).toFixed(2);
    }

    /**
     * 创建盈亏分布图表
     * @param {HTMLElement} container - 图表容器
     * @param {Object} result - 模拟结果
     * @private
     */
    _createProfitDistributionChart(container, result) {
        if (!result.betHistory || result.betHistory.length === 0) {
            container.innerHTML = '<div class="simulation-chart-message">没有足够的下注历史数据来创建盈亏分布图表</div>';
            return;
        }

        // 清空容器
        container.innerHTML = '';

        // 创建canvas元素
        const canvas = document.createElement('canvas');
        canvas.id = 'profitDistributionChart';
        container.appendChild(canvas);

        // 计算每局盈亏
        const profitPerGame = [];
        let totalBet = 0;
        let totalWin = 0;
        let totalLoss = 0;

        // 从下注历史中提取盈亏数据
        for (const bet of result.betHistory) {
            let profit = 0;

            // 计算盈亏
            if (bet.profit !== undefined) {
                profit = bet.profit;
            } else if (bet.result === 'win') {
                profit = bet.bet;
            } else if (bet.result === 'lose') {
                profit = -bet.bet;
            } else if (bet.result === 'blackjack') {
                profit = bet.bet * 1.5;
            } else if (bet.result === 'push') {
                profit = 0;
            } else if (bet.result === 'surrender') {
                profit = -bet.bet / 2;
            }

            profitPerGame.push(profit);

            // 累计总下注和盈亏
            totalBet += bet.bet;
            if (profit > 0) {
                totalWin += profit;
            } else if (profit < 0) {
                totalLoss += Math.abs(profit);
            }
        }

        // 计算盈亏分布
        const profitDistribution = {};
        const profitRanges = [-500, -200, -100, -50, -20, -10, 0, 10, 20, 50, 100, 200, 500];

        // 初始化分布对象
        for (let i = 0; i < profitRanges.length - 1; i++) {
            const rangeLabel = `${profitRanges[i]} 到 ${profitRanges[i+1]}`;
            profitDistribution[rangeLabel] = 0;
        }

        // 统计每个范围内的盈亏次数
        for (const profit of profitPerGame) {
            for (let i = 0; i < profitRanges.length - 1; i++) {
                if (profit >= profitRanges[i] && profit < profitRanges[i+1]) {
                    const rangeLabel = `${profitRanges[i]} 到 ${profitRanges[i+1]}`;
                    profitDistribution[rangeLabel]++;
                    break;
                }
            }

            // 处理超出范围的值
            if (profit < profitRanges[0]) {
                const rangeLabel = `小于 ${profitRanges[0]}`;
                if (!profitDistribution[rangeLabel]) {
                    profitDistribution[rangeLabel] = 0;
                }
                profitDistribution[rangeLabel]++;
            } else if (profit >= profitRanges[profitRanges.length - 1]) {
                const rangeLabel = `大于等于 ${profitRanges[profitRanges.length - 1]}`;
                if (!profitDistribution[rangeLabel]) {
                    profitDistribution[rangeLabel] = 0;
                }
                profitDistribution[rangeLabel]++;
            }
        }

        // 准备图表数据
        const labels = Object.keys(profitDistribution);
        const data = Object.values(profitDistribution);

        // 为每个范围设置颜色
        const backgroundColors = labels.map(label => {
            if (label.includes('小于') || label.startsWith('-')) {
                return 'rgba(244, 67, 54, 0.7)'; // 红色，表示亏损
            } else if (label.includes('0 到')) {
                return 'rgba(255, 193, 7, 0.7)'; // 黄色，表示接近盈亏平衡
            } else {
                return 'rgba(76, 175, 80, 0.7)'; // 绿色，表示盈利
            }
        });

        // 创建图表
        const ctx = canvas.getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '盈亏分布',
                    data: data,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '盈亏分布图表',
                        color: '#e6e6e6',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleColor: '#e6e6e6',
                        bodyColor: '#e6e6e6',
                        borderColor: '#38bdf8',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = (value / profitPerGame.length * 100).toFixed(1);
                                return `${value} 局 (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa',
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#a1a1aa'
                        }
                    }
                }
            }
        });

        // 添加图表信息
        const chartInfo = document.createElement('div');
        chartInfo.className = 'simulation-chart-info';
        chartInfo.innerHTML = `
            <div>总局数: ${profitPerGame.length}</div>
            <div>总下注: ${totalBet.toLocaleString()}</div>
            <div>总盈利: ${totalWin.toLocaleString()}</div>
            <div>总亏损: ${totalLoss.toLocaleString()}</div>
            <div>净盈亏: ${(totalWin - totalLoss).toLocaleString()}</div>
        `;
        container.appendChild(chartInfo);
    }
}

// 创建全局单例实例
window.simulationUI = new SimulationUI();