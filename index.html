<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlackJack 21点 - 多人模式</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/test-mode-controls.css">
    <link rel="stylesheet" href="css/betting-ui.css">
    <link rel="stylesheet" href="css/chips-ui.css">
    <link rel="stylesheet" href="css/stats-history-ui.css">
    <link rel="stylesheet" href="css/card-counting.css">
    <link rel="stylesheet" href="css/betting-history.css">
    <link rel="stylesheet" href="css/player-stats.css">
    <link rel="stylesheet" href="css/simulation.css">
</head>
<body>
    <div class="game-container">
        <div class="stats-container">
            <div class="deck-info">
                <span>牌库数量: <span id="deck-count">8</span></span>
                <span>已发牌数: <span id="cards-used">0</span></span>
                <span>剩余牌数: <span id="cards-remaining">416</span></span>
                <span>洗牌阈值: <span id="shuffle-threshold">146</span></span>
                <span>算牌系统: <span id="counting-system">Hi-Lo</span></span>
                <span>流水数: <span id="running-count">0</span></span>
                <span>真数: <span id="true-count">0</span></span>
                <span>洗牌次数: <span id="shuffle-count">0</span></span>
            </div>
            <div class="total-score">
                <span>总盈亏: <span id="total-score">0</span></span>
            </div>
        </div>

        <div class="game-controls">
            <div class="control-group">
                <button id="add-player">添加玩家</button>
                <button id="remove-player">移除玩家</button>
                <select id="player-count">
                    <option value="1">1位玩家</option>
                    <option value="2">2位玩家</option>
                    <option value="3">3位玩家</option>
                    <option value="4">4位玩家</option>
                    <option value="5">5位玩家</option>
                    <option value="6">6位玩家</option>
                </select>
                <button id="new-game">开始新游戏</button>
                <button id="game-settings">游戏设置</button>
                <button id="pause-auto" class="auto-control-btn" style="display: none;">暂停自动</button>
                <label class="toggle inline-toggle" style="display: inline-flex; align-items: center; margin-left: 5px; vertical-align: middle;">
                    <input type="checkbox" id="allow-management">
                    <span class="toggle-slider"></span>
                    <span class="toggle-label">允许游戏中管理玩家</span>
                </label>
                <button id="betting-history-btn" class="inline-button" style="margin-left: 10px;">下注记录</button>
                <button id="player-stats-btn" class="inline-button" style="margin-left: 5px;">玩家统计</button>
                <button id="reset-stats-btn" class="inline-button" style="margin-left: 5px;">重置统计</button>
                <label class="toggle inline-toggle" style="display: inline-flex; align-items: center; margin-left: 10px; vertical-align: middle;">
                    <input type="checkbox" id="test-mode">
                    <span class="toggle-slider"></span>
                    <span class="toggle-label">测试模式</span>
                </label>
            </div>
        </div>

        <div id="game-settings-menu" class="game-settings-menu" style="display: none;">
            <div class="settings-header">
                <h3>游戏设置</h3>
                <span id="close-settings" class="close-button">&times;</span>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h4>牌库设置</h4>
                    <div class="settings-row">
                        <label for="deck-number">牌库数量：</label>
                        <select id="deck-number">
                            <option value="1">1副牌</option>
                            <option value="2">2副牌</option>
                            <option value="4">4副牌</option>
                            <option value="6">6副牌</option>
                            <option value="8" selected>8副牌</option>
                        </select>
                    </div>
                    <div class="settings-row">
                        <label for="penetration-rate-slider">渗透率：<span id="penetration-rate-value">65%</span></label>
                        <input type="range" id="penetration-rate-slider" min="10" max="90" step="5" value="65">
                    </div>
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="snail-shuffle">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">蜗牛洗牌</span>
                        </label>
                    </div>
                </div>

                <!-- 游戏规则部分已移至其他设置 -->

                <div class="settings-section">
                    <h4>筹码设置</h4>
                    <div class="settings-row">
                        <label for="min-bet">最小下注：</label>
                        <input type="number" id="min-bet" min="1" max="100" value="10" step="5">
                    </div>
                    <div class="settings-row">
                        <label for="max-bet">最大下注：</label>
                        <input type="number" id="max-bet" min="100" max="50000" value="500" step="100">
                    </div>
                    <div class="settings-row">
                        <label for="default-bet">默认下注：</label>
                        <input type="number" id="default-bet" min="10" max="500" value="50" step="10">
                    </div>

                    <!-- 玩家筹码管理部分 -->
                    <div class="settings-row">
                        <h5>筹码管理</h5>
                        <div class="player-selector">
                            <select id="chip-player-select">
                                <!-- 将由JS动态填充玩家列表 -->
                            </select>
                            <div style="display: flex; align-items: center; gap: 5px; white-space: nowrap;">
                                <label for="chip-amount">金额：</label>
                                <input type="number" id="chip-amount" min="1000" max="100000" value="10000" step="1000">
                            </div>
                        </div>
                        <div class="chip-management-buttons">
                            <button id="add-chips-single" class="chip-add-single">为选中玩家补充筹码</button>
                            <button id="add-chips-all" class="chip-add-all">为所有玩家补充筹码</button>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>自动模式与自动化设置</h4>
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="auto-mode">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">自动模式</span>
                        </label>
                    </div>
                    <div class="settings-row" id="auto-speed-settings">
                        <label for="auto-speed">速度：<span id="speed-value">600</span>ms</label>
                        <div class="speed-control">
                            <span id="decrease-speed" style="color:#38bdf8; cursor:pointer; margin:0 3px;">-</span>
                            <input type="range" id="auto-speed" min="300" max="1000" step="100" value="600">
                            <span id="increase-speed" style="color:#38bdf8; cursor:pointer; margin:0 3px;">+</span>
                        </div>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                自动模式支持后台运行，切换标签页游戏将继续执行
                            </span>
                        </div>
                    </div>

                    <!-- 庄家发牌速度设置 -->
                    <div class="settings-row" id="dealer-speed-settings">
                        <label for="dealer-speed">庄家发牌速度：<span id="dealer-speed-value">500</span>ms</label>
                        <div class="speed-control">
                            <span id="decrease-dealer-speed" style="color:#38bdf8; cursor:pointer; margin:0 3px;">-</span>
                            <input type="range" id="dealer-speed" min="500" max="2000" step="100" value="500">
                            <span id="increase-dealer-speed" style="color:#38bdf8; cursor:pointer; margin:0 3px;">+</span>
                        </div>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                调节庄家每张牌发牌的时间间隔，数值越大发牌越慢
                            </span>
                        </div>
                    </div>

                    <!-- 自动下注设置 -->
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="auto-betting">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">自动下注</span>
                        </label>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                注意：如果启用了计牌系统的基于真数的自动下注，将优先使用计牌系统的下注策略
                            </span>
                        </div>
                    </div>
                    <div class="settings-row" id="auto-bet-settings" style="display: none;">
                        <label for="auto-bet-amount">自动下注金额：</label>
                        <input type="number" id="auto-bet-amount" min="10" max="500" value="100" step="10">
                        <button id="set-auto-bet" class="small-button">应用</button>
                    </div>

                    <!-- 自动补充筹码设置 -->
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="auto-refill-chips">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">自动补充筹码</span>
                        </label>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                当玩家筹码低于阈值时自动补充，避免自动模式因筹码不足而中断
                            </span>
                        </div>
                    </div>
                    <div class="settings-row" id="auto-refill-settings" style="display: none;">
                        <div class="settings-row">
                            <label for="auto-refill-threshold">筹码阈值：</label>
                            <input type="number" id="auto-refill-threshold" min="100" max="10000" value="1000" step="100">
                        </div>
                        <div class="settings-row">
                            <label for="auto-refill-amount">补充金额：</label>
                            <input type="number" id="auto-refill-amount" min="1000" max="100000" value="10000" step="1000">
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>游戏规则与其他设置</h4>
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="dealer-stand-soft17" checked>
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">庄家软17点停牌</span>
                        </label>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                开启：庄家在软17点及以上都停牌；关闭：庄家只有在硬17点及以上才停牌，所有软牌都继续要牌
                            </span>
                        </div>
                    </div>
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="strategy-hint-switch" checked>
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">策略建议</span>
                        </label>
                    </div>
                    <div class="settings-row">
                        <label class="toggle">
                            <input type="checkbox" id="reset-stats" checked>
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">开始新游戏时重置统计</span>
                        </label>
                        <div class="info-tip">
                            <span style="font-size: 0.8em; color: #60a5fa; display: block; margin-top: 5px;">
                                打开此选项后，每次点击"开始新游戏"按钮都会自动重置所有统计数据和游戏历史
                            </span>
                        </div>
                    </div>
                </div>

                <div class="settings-buttons">
                    <button id="save-settings">保存设置</button>
                    <button id="cancel-settings">取消</button>
                </div>
            </div>
        </div>

        <div id="test-mode-controls" class="test-mode-controls" style="display: none;">
            <div class="test-control-header">
                <h3>测试模式</h3>
                <span id="test-mode-close" class="test-mode-close">&times;</span>
            </div>
            <div class="test-control-group">
                <div class="card-selector">
                    <select id="test-player-select">
                        <option value="dealer">庄家</option>
                        <option value="0">玩家1</option>
                        <option value="1">玩家2</option>
                        <option value="2">玩家3</option>
                        <option value="3">玩家4</option>
                    </select>
                    <select id="test-hand-select">
                        <option value="0">手牌1</option>
                        <option value="1">手牌2</option>
                        <option value="2">手牌3</option>
                        <option value="3">手牌4</option>
                    </select>
                    <select id="test-card-suit">
                        <option value="hearts">红心</option>
                        <option value="diamonds">方块</option>
                        <option value="clubs">梅花</option>
                        <option value="spades">黑桃</option>
                    </select>
                    <select id="test-card-rank">
                        <option value="A">A</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="J">J</option>
                        <option value="Q">Q</option>
                        <option value="K">K</option>
                    </select>
                    <button id="test-add-card">添加</button>
                    <button id="test-clear-hand">清空</button>
                </div>
                <div class="test-control-tip">
                    <p>使用说明：选择玩家/庄家和手牌，然后选择要添加的牌花色和点数，点击"添加"按钮将牌添加到选定手牌中。可通过拖拽窗口顶部移动位置。</p>
                </div>
            </div>
        </div>

        <div class="result-banner" id="result-banner"></div>

        <div class="game-area">
            <div class="dealer-section">
                <div class="dealer-hand">
                    <h2>庄家手牌</h2>
                    <div id="dealer-cards" class="cards"></div>
                    <span class="points-display" id="dealer-points">0</span>
                </div>
            </div>

            <div id="players-container">
                <!-- 玩家手牌区域将由JavaScript动态生成 -->
            </div>

            <div class="bottom-controls">
                <div class="controls action-buttons">
                    <button id="hit" class="hit">要牌</button>
                    <button id="stand" class="stand">停牌</button>
                    <button id="double" class="double">加倍</button>
                    <button id="split" class="split">分牌</button>
                    <button id="surrender" class="surrender">投降</button>
                </div>

                <div class="strategy-container">
                    <div id="strategy-hint" class="strategy-hint">
                        <div id="strategy-hint-text"></div>
                    </div>
                </div>

                <div class="history-controls">
                    <button id="prev-hand">上一局</button>
                    <button id="next-hand">下一局</button>
                    <button id="current-hand">当前局</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/logger.js"></script>
    <script src="js/performance-utils.js"></script>
    <script src="js/resource-loader.js"></script>
    <script src="js/background-runner.js"></script>
    <script src="js/card-counting.js"></script>
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/autoStrategy.js"></script>
    <script src="js/autoStrategyValidator.js"></script>
    <script src="js/game.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/betting-history.js"></script>
    <script src="js/player-stats.js"></script>
    <script src="js/card-counting-ui.js"></script>
    <!-- 直接加载模拟系统相关脚本 -->
    <script src="js/simulation-engine.js"></script>
    <script src="js/chart-utils.js"></script>
    <script src="js/simulation-ui.js"></script>
    <script src="js/simulation-import.js"></script>
    <script>
        // Chart.js加载器
        function loadChartJS(callback) {
            // 尝试不同的CDN源
            const sources = [
                "https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js",
                "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js",
                "https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js",
                "https://unpkg.com/chart.js@3.9.1/dist/chart.min.js",
                "https://fastly.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"
            ];

            function trySource(index) {
                if (index >= sources.length) {
                    console.error("无法加载Chart.js，所有源都失败");
                    // 通知用户
                    alert("加载图表库失败，模拟功能的图表展示将不可用。但其他功能不受影响。");
                    if (callback) callback(false);
                    return;
                }

                console.log(`尝试从 ${sources[index]} 加载Chart.js...`);

                const script = document.createElement('script');
                script.src = sources[index];
                script.onload = function() {
                    console.log("成功从 " + sources[index] + " 加载Chart.js");
                    if (callback) callback(true);
                };
                script.onerror = function() {
                    console.warn("从 " + sources[index] + " 加载Chart.js失败，尝试下一个源");
                    trySource(index + 1);
                };

                // 设置超时处理
                const timeout = setTimeout(function() {
                    console.warn("从 " + sources[index] + " 加载Chart.js超时，尝试下一个源");
                    script.onerror = null; // 防止可能的回调重复
                    trySource(index + 1);
                }, 5000); // 5秒超时

                script.onload = function() {
                    clearTimeout(timeout);
                    console.log("成功从 " + sources[index] + " 加载Chart.js");
                    if (callback) callback(true);
                };

                document.head.appendChild(script);
            }

            // 检查是否已加载
            if (typeof Chart !== 'undefined') {
                console.log("Chart.js已经加载，无需重新加载");
                if (callback) callback(true);
                return;
            }

            // 开始尝试加载
            trySource(0);
        }

        // 初始化加载
        loadChartJS(function(success) {
            if (success) {
                console.log("Chart.js加载成功，模拟功能的图表功能可用");
            } else {
                console.warn("Chart.js加载失败，模拟功能的图表展示将不可用，但其他功能不受影响");
            }
        });
    </script>
    <script>
        // 添加模拟按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 创建快速模拟按钮
            const controlGroup = document.querySelector('.control-group');
            if (controlGroup) {
                const simulationButton = document.createElement('button');
                simulationButton.id = 'simulation-button';
                simulationButton.className = 'inline-button';
                simulationButton.style.marginLeft = '5px';
                simulationButton.textContent = '快速模拟';
                simulationButton.addEventListener('click', function() {
                    if (window.simulationUI) {
                        try {
                            // 在打开模拟界面前先简单检查游戏组件
                            console.log('检查游戏组件状态...');
                            const hasGameClass = typeof window.Game === 'function';
                            const hasCountingSystemClass = typeof window.CardCountingSystem === 'function';
                            const hasAutoStrategyClass = typeof window.AutoStrategy === 'function';

                            if (!hasGameClass || !hasCountingSystemClass || !hasAutoStrategyClass) {
                                console.error('缺少必要游戏组件类:');
                                console.error('Game类:', hasGameClass);
                                console.error('CardCountingSystem类:', hasCountingSystemClass);
                                console.error('AutoStrategy类:', hasAutoStrategyClass);

                                // 尝试先加载各个组件
                                if (!hasGameClass && window.Game === undefined) {
                                    console.log('尝试从game.js手动加载Game类...');
                                }
                                if (!hasCountingSystemClass && window.CardCountingSystem === undefined) {
                                    console.log('尝试从card-counting.js手动加载CardCountingSystem类...');
                                }
                                if (!hasAutoStrategyClass && window.AutoStrategy === undefined) {
                                    console.log('尝试从autoStrategy.js手动加载AutoStrategy类...');
                                }

                                alert('无法启动模拟功能：缺少必要的游戏组件。请刷新页面并确保先开始一局游戏。');
                                return;
                            }

                            // 直接尝试显示UI，让SimulationUI自己处理组件检查和创建
                            window.simulationUI.toggle();
                        } catch (error) {
                            console.error('打开模拟界面时出错:', error);
                            // 提供更详细的错误信息
                            let errorMsg = '打开模拟界面时出错，请刷新页面后重试。\n';
                            errorMsg += '错误详情: ' + error.message + '\n';
                            errorMsg += '如果问题持续，请先开始一局普通游戏，然后再尝试使用模拟功能。';
                            alert(errorMsg);
                        }
                    } else {
                        console.error('找不到模拟UI组件');
                        alert('模拟UI组件未加载，请刷新页面后重试。');
                    }
                });

                // 在适当位置添加按钮
                const resetStatsBtn = document.getElementById('reset-stats-btn');
                if (resetStatsBtn) {
                    controlGroup.insertBefore(simulationButton, resetStatsBtn.nextSibling);
                } else {
                    controlGroup.appendChild(simulationButton);
                }
            }

            // 在页面加载完成后，尝试初始化游戏实例
            setTimeout(function() {
                console.log('检查游戏实例...');
                try {
                    // 检查类是否正确定义
                    const hasGameClass = typeof Game === 'function';
                    const hasCountingSystemClass = typeof CardCountingSystem === 'function';
                    const hasAutoStrategyClass = typeof AutoStrategy === 'function';

                    console.log('游戏组件类状态:');
                    console.log('Game类:', hasGameClass ? '已定义' : '未定义');
                    console.log('CardCountingSystem类:', hasCountingSystemClass ? '已定义' : '未定义');
                    console.log('AutoStrategy类:', hasAutoStrategyClass ? '已定义' : '未定义');

                    // 检查并创建游戏实例
                    if (!window.game && hasGameClass) {
                        console.log('创建默认游戏实例...');
                        window.game = new Game();
                        window.game.init();
                    }

                    // 检查并创建算牌系统实例
                    if (!window.cardCountingSystem && hasCountingSystemClass) {
                        console.log('创建算牌系统实例...');
                        window.cardCountingSystem = new CardCountingSystem();
                    }

                    // 检查并创建自动策略实例
                    if (!window.autoStrategy && hasAutoStrategyClass) {
                        console.log('创建自动策略实例...');
                        window.autoStrategy = new AutoStrategy();
                    }

                    // 检查实例是否正确创建
                    const hasGameInstance = typeof window.game !== 'undefined' && window.game !== null;
                    const hasCountingSystemInstance = typeof window.cardCountingSystem !== 'undefined' && window.cardCountingSystem !== null;
                    const hasAutoStrategyInstance = typeof window.autoStrategy !== 'undefined' && window.autoStrategy !== null;

                    console.log('游戏组件实例状态:');
                    console.log('Game实例:', hasGameInstance ? '已创建' : '未创建');
                    console.log('CardCountingSystem实例:', hasCountingSystemInstance ? '已创建' : '未创建');
                    console.log('AutoStrategy实例:', hasAutoStrategyInstance ? '已创建' : '未创建');

                    if (hasGameInstance && hasCountingSystemInstance && hasAutoStrategyInstance) {
                        console.log('游戏组件初始化完成!');
                    } else {
                        console.warn('部分游戏组件未能初始化，模拟功能可能无法正常工作');
                    }
                } catch (error) {
                    console.error('初始化游戏实例时出错:', error);
                }
            }, 1000); // 增加延迟时间以确保所有脚本加载
        });
    </script>
</body>
</html>