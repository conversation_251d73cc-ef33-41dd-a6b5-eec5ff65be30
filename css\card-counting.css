/* 算牌系统UI样式 */

/* 算牌系统按钮 */
#card-counting-btn {
    margin: 5px;
}

/* 算牌系统面板 */
.card-counting-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    max-width: 90%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    overflow: hidden;
}

/* 面板标题 */
.card-counting-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #2980b9; /* 更深的蓝色 */
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.card-counting-panel .panel-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
}

.card-counting-panel .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

/* 面板内容 */
.card-counting-panel .panel-content {
    padding: 15px;
    /* 移除最大高度和滚动条，使面板自适应内容高度 */
}

/* 设置区域 */
.card-counting-panel .setting-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.card-counting-panel .setting-section:last-child {
    border-bottom: none;
}

.card-counting-panel h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #2980b9; /* 蓝色标题 */
    font-weight: bold;
    border-bottom: 2px solid #3498db; /* 添加下划线 */
    padding-bottom: 8px;
}

/* 算牌系统选择 */
#counting-system-select {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background-color: #fff;
    border: 2px solid #3498db;
    border-radius: 4px;
    margin-bottom: 15px;
    cursor: pointer;
    appearance: none; /* 移除默认样式 */
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg fill="%233498db" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
}

#counting-system-select:focus {
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
}

#counting-system-select option {
    font-weight: bold;
    color: #333;
    background-color: #fff;
    padding: 10px;
}

/* 开关容器 */
.switch-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px;
    background-color: #e8f4ff; /* 浅蓝色背景 */
    border-radius: 4px;
    border: 1px solid #a8d4ff; /* 蓝色边框 */
}

/* 开关标签样式 */
.switch-container label {
    font-weight: bold;
    color: #0066cc; /* 深蓝色文字 */
}

/* 阈值设置 */
.thresholds-container {
    margin-top: 15px;
}

.threshold-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #e0e0e0; /* 更深的背景色 */
    border-radius: 4px;
    border: 1px solid #ccc; /* 添加边框 */
    flex-wrap: nowrap; /* 防止换行 */
    height: 50px; /* 固定高度，确保足够容纳删除按钮 */
    box-sizing: border-box; /* 确保padding不会增加总高度 */
}

.threshold-row label {
    margin-right: 5px;
    font-size: 14px;
    color: #333; /* 添加更深的文字颜色 */
    white-space: nowrap; /* 防止文字换行 */
}

.threshold-row input {
    width: 70px;
    padding: 5px;
    border: 1px solid #3498db; /* 蓝色边框 */
    border-radius: 3px;
    margin-right: 10px;
    background-color: #fff; /* 白色背景 */
    color: #333; /* 深色文字 */
    font-weight: bold; /* 加粗文字 */
}

.delete-threshold-btn {
    background-color: #9b59b6; /* 紫色背景 */
    border: none;
    color: white; /* 白色文字 */
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 36px; /* 增加宽度 */
    height: 36px; /* 增加高度，与宽度相同，形成正方形 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    border-radius: 4px;
    padding: 0; /* 移除内边距 */
    line-height: 1;
    min-width: 36px; /* 确保最小宽度 */
    max-width: 36px; /* 确保最大宽度 */
    flex: 0 0 36px; /* 防止flex布局改变尺寸 */
}

.delete-threshold-btn:hover {
    background-color: #8e44ad; /* 鼠标悬停时背景色加深 */
}

/* 添加阈值按钮 */
#add-threshold-btn {
    margin-top: 10px;
    padding: 8px 15px;
    font-size: 14px;
    background-color: #3498db; /* 蓝色背景 */
    color: white; /* 白色文字 */
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

#add-threshold-btn:hover {
    background-color: #2980b9; /* 鼠标悬停时背景色加深 */
}

/* 复选框样式 */
input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
    accent-color: #3498db; /* 设置选中颜色 */
}

/* 保存按钮 */
#save-counting-settings {
    display: block;
    width: 100%;
    padding: 12px;
    margin-top: 20px;
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    transition: all 0.3s ease; /* 添加过渡效果 */
}

#save-counting-settings:hover {
    background-color: #27ae60;
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15); /* 鼠标悬停时阴影加深 */
    transform: translateY(-2px); /* 鼠标悬停时按钮微微上浮 */
}

/* 算牌系统信息显示 */
.counting-info {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 14px;
}

.counting-info span {
    margin-right: 15px;
}

.counting-info .system-name {
    font-weight: bold;
    color: #3498db;
}

.counting-info .running-count {
    color: #f39c12;
}

.counting-info .true-count {
    color: #e74c3c;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .card-counting-panel {
        width: 95%;
    }

    /* 保持阈值行不换行 */
    .threshold-row {
        flex-wrap: nowrap;
    }

    /* 减小输入框宽度 */
    .threshold-row input {
        width: 55px;
        margin-right: 5px;
    }

    /* 减小标签字体大小 */
    .threshold-row label {
        font-size: 12px;
        margin-right: 2px;
    }
}
