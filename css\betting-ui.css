/* 下注界面样式 */
.betting-ui {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    max-width: 90vw;
    background-color: #1a2436;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    color: #ffffff;
    border: 2px solid #38bdf8;
}

.bet-control-panel {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.bet-control-panel h3 {
    font-size: 24px;
    margin: 0 0 15px 0;
    color: #38bdf8;
    text-align: center;
}

.betting-player-info {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: rgba(30, 41, 59, 0.7);
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.player-name {
    font-weight: bold;
    color: #60a5fa;
}

.player-chips {
    color: #8b5cf6;
    font-weight: bold;
}

.bet-amount-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 10px 0;
}

.decrease-bet-btn, .increase-bet-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #38bdf8;
    color: #ffffff;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.decrease-bet-btn:hover, .increase-bet-btn:hover {
    background-color: #60a5fa;
    transform: scale(1.05);
}

.decrease-bet-btn:active, .increase-bet-btn:active {
    transform: scale(0.95);
}

.bet-amount {
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    min-width: 80px;
    text-align: center;
    background-color: rgba(30, 41, 59, 0.7);
    padding: 5px 15px;
    border-radius: 5px;
}

.preset-bets-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin: 10px 0;
}

.preset-bet-btn {
    padding: 8px 15px;
    border-radius: 5px;
    background-color: #1e293b;
    color: #ffffff;
    border: 1px solid #38bdf8;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: bold;
}

.preset-bet-btn:hover {
    background-color: #38bdf8;
    color: #0c1525;
}

.bet-action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    width: 100%;
    justify-content: center;
}

.place-bet-btn, .clear-bet-btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.place-bet-btn {
    background-color: #8b5cf6;
    color: #ffffff;
}

.clear-bet-btn {
    background-color: #1e293b;
    color: #ffffff;
    border: 1px solid #8b5cf6;
}

.place-bet-btn:hover {
    background-color: #6366f1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
}

.clear-bet-btn:hover {
    background-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-2px);
}

.place-bet-btn:active, .clear-bet-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* 手牌下注显示样式 */
.hand-header {
    display: flex;
    justify-content: flex-start; /* 改为左对齐 */
    align-items: center;
    padding: 6px 12px; /* 增加内边距 */
    background-color: rgba(30, 41, 59, 0.7);
    border-radius: 5px 5px 0 0;
    margin-bottom: 5px;
    gap: 12px; /* 增加间距 */
    min-height: 30px; /* 确保高度足够容纳更大的标识 */
}

.hand-title {
    color: #60a5fa;
    font-weight: bold;
    font-size: 15px; /* 增大字体大小 */
    order: 1; /* 设置顺序，确保手牌标题在左侧 */
}

.bet-display {
    color: #8b5cf6;
    font-size: 15px; /* 增大字体大小 */
    display: flex;
    align-items: center;
    gap: 6px; /* 增加间距 */
    order: 2; /* 设置顺序，确保下注显示在手牌标题右侧 */
}

.bet-label {
    color: #e2e8f0;
}

.bet-value {
    font-weight: bold;
    color: #ffffff;
    background-color: rgba(139, 92, 246, 0.3);
    padding: 3px 8px; /* 增加内边距 */
    border-radius: 4px; /* 稍微增大圆角 */
    display: flex;
    align-items: center;
    gap: 5px;
    min-height: 22px; /* 确保高度足够容纳更大的标识 */
}

/* 加倍和分牌标识样式 */
.bet-badge {
    display: inline-block;
    font-size: 12px; /* 增大字体大小，从10px改为12px */
    font-weight: bold;
    padding: 2px 5px; /* 稍微增加内边距，使标识更加醒目 */
    border-radius: 3px;
    color: #ffffff;
    margin-left: 5px; /* 稍微增加左边距 */
}

.double-badge {
    background-color: #f59e0b; /* 橙色，与加倍按钮颜色一致 */
}

.split-badge {
    background-color: #7c3aed; /* 紫色，与分牌按钮颜色一致 */
}

.surrender-badge {
    background-color: #3b82f6; /* 蓝色，与投降按钮颜色一致 */
}

/* 响应式设计 */
@media (max-width: 768px) {
    .betting-ui {
        width: 320px;
    }

    .bet-amount {
        font-size: 22px;
    }

    .decrease-bet-btn, .increase-bet-btn {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .preset-bet-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}