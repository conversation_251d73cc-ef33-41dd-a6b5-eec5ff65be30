/**
 * 21点算牌系统UI界面
 * 提供算牌算法选择和自动下注策略设置
 */

// 创建日志记录器
const cardCountingUILogger = window.Logger ? window.Logger.getLogger('CardCountingUI') : {
    debug: () => {},
    info: () => {},
    warn: console.warn,
    error: console.error
};

/**
 * 算牌系统UI管理类
 */
class CardCountingUI {
    constructor() {
        // 初始化UI
        this.createUI();

        // 绑定事件
        this.bindEvents();

        cardCountingUILogger.info('算牌系统UI已初始化');
    }

    /**
     * 创建算牌系统UI界面
     */
    createUI() {
        // 创建算牌系统设置按钮
        this.createCardCountingButton();

        // 创建算牌系统设置面板
        this.createCardCountingPanel();
    }

    /**
     * 创建算牌系统设置按钮
     */
    createCardCountingButton() {
        // 检查按钮是否已存在
        if (document.getElementById('card-counting-btn')) {
            return;
        }

        // 创建按钮
        const button = document.createElement('button');
        button.id = 'card-counting-btn';
        button.className = 'btn btn-info';
        button.textContent = '算牌系统';

        // 添加到游戏控制按钮栏
        const controlGroup = document.querySelector('.game-controls .control-group');
        if (controlGroup) {
            // 在游戏设置按钮后面插入计牌系统按钮
            const gameSettingsBtn = document.getElementById('game-settings');
            if (gameSettingsBtn) {
                controlGroup.insertBefore(button, gameSettingsBtn.nextSibling);
            } else {
                // 如果找不到游戏设置按钮，直接添加到控制组末尾
                controlGroup.appendChild(button);
            }
        } else {
            // 如果找不到控制组，添加到页面底部
            document.body.appendChild(button);
        }
    }

    /**
     * 创建算牌系统设置面板
     */
    createCardCountingPanel() {
        // 检查面板是否已存在
        if (document.getElementById('card-counting-panel')) {
            return;
        }

        // 创建面板
        const panel = document.createElement('div');
        panel.id = 'card-counting-panel';
        panel.className = 'card-counting-panel';
        panel.style.display = 'none';

        // 创建面板标题
        const header = document.createElement('div');
        header.className = 'panel-header';

        const title = document.createElement('h3');
        title.textContent = '算牌系统设置';
        header.appendChild(title);

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-button';
        closeButton.textContent = '×';
        header.appendChild(closeButton);

        panel.appendChild(header);

        // 创建面板内容
        const content = document.createElement('div');
        content.className = 'panel-content';

        // 算牌系统选择
        const systemSection = document.createElement('div');
        systemSection.className = 'setting-section';

        const systemLabel = document.createElement('h4');
        systemLabel.textContent = '算牌算法';
        systemSection.appendChild(systemLabel);

        const systemSelect = document.createElement('select');
        systemSelect.id = 'counting-system-select';

        // 添加算牌系统选项
        const systems = [
            { value: 'hi-lo', text: 'Hi-Lo' },
            { value: 'omega-ii', text: 'Omega II' },
            { value: 'halves', text: 'Halves' }
        ];

        systems.forEach(system => {
            const option = document.createElement('option');
            option.value = system.value;
            option.textContent = system.text;
            systemSelect.appendChild(option);
        });

        systemSection.appendChild(systemSelect);
        content.appendChild(systemSection);

        // 自动下注设置
        const betSection = document.createElement('div');
        betSection.className = 'setting-section';

        const betLabel = document.createElement('h4');
        betLabel.textContent = '基于真数的自动下注';
        betSection.appendChild(betLabel);

        // 启用自动下注开关
        const enableBetDiv = document.createElement('div');
        enableBetDiv.className = 'switch-container';

        const enableBetLabel = document.createElement('label');
        enableBetLabel.textContent = '启用自动下注';
        enableBetLabel.htmlFor = 'enable-auto-bet';
        enableBetLabel.style.fontWeight = 'bold'; // 加粗文字
        enableBetLabel.style.color = '#0066cc'; // 深蓝色文字
        enableBetDiv.appendChild(enableBetLabel);

        const enableBetSwitch = document.createElement('input');
        enableBetSwitch.type = 'checkbox';
        enableBetSwitch.id = 'enable-auto-bet';
        enableBetDiv.appendChild(enableBetSwitch);

        betSection.appendChild(enableBetDiv);

        // 添加自动模式开关
        const autoModeDiv = document.createElement('div');
        autoModeDiv.className = 'switch-container';
        autoModeDiv.style.marginTop = '15px';

        const autoModeLabel = document.createElement('label');
        autoModeLabel.textContent = '启用自动模式';
        autoModeLabel.htmlFor = 'counting-auto-mode';
        autoModeLabel.style.fontWeight = 'bold';
        autoModeLabel.style.color = '#0066cc';
        autoModeDiv.appendChild(autoModeLabel);

        const autoModeSwitch = document.createElement('input');
        autoModeSwitch.type = 'checkbox';
        autoModeSwitch.id = 'counting-auto-mode';
        autoModeDiv.appendChild(autoModeSwitch);

        betSection.appendChild(autoModeDiv);

        // 添加自动模式速度控制
        const autoSpeedDiv = document.createElement('div');
        autoSpeedDiv.className = 'speed-settings';
        autoSpeedDiv.id = 'counting-auto-speed-settings';
        autoSpeedDiv.style.display = 'none'; // 默认隐藏
        autoSpeedDiv.style.marginTop = '10px';
        autoSpeedDiv.style.padding = '10px';
        autoSpeedDiv.style.backgroundColor = '#d0e8ff'; // 更深的背景色，提高对比度
        autoSpeedDiv.style.borderRadius = '4px';

        const speedLabel = document.createElement('label');
        speedLabel.htmlFor = 'counting-auto-speed';
        speedLabel.innerHTML = '<span style="color:#333; font-weight:bold;">速度：</span><span id="counting-speed-value" style="color:#333; font-weight:bold;">600</span><span style="color:#333; font-weight:bold;">ms</span>';
        speedLabel.style.display = 'block';
        speedLabel.style.marginBottom = '5px';
        autoSpeedDiv.appendChild(speedLabel);

        const speedControlDiv = document.createElement('div');
        speedControlDiv.className = 'speed-control';
        speedControlDiv.style.display = 'flex';
        speedControlDiv.style.alignItems = 'center';

        const decreaseBtn = document.createElement('span');
        decreaseBtn.id = 'counting-decrease-speed';
        decreaseBtn.textContent = '-';
        decreaseBtn.style.color = '#38bdf8';
        decreaseBtn.style.cursor = 'pointer';
        decreaseBtn.style.margin = '0 3px';
        decreaseBtn.style.fontSize = '18px';
        speedControlDiv.appendChild(decreaseBtn);

        const speedSlider = document.createElement('input');
        speedSlider.type = 'range';
        speedSlider.id = 'counting-auto-speed';
        speedSlider.min = '300';
        speedSlider.max = '1000';
        speedSlider.step = '100';
        speedSlider.value = '600';
        speedSlider.style.flex = '1';
        speedControlDiv.appendChild(speedSlider);

        const increaseBtn = document.createElement('span');
        increaseBtn.id = 'counting-increase-speed';
        increaseBtn.textContent = '+';
        increaseBtn.style.color = '#38bdf8';
        increaseBtn.style.cursor = 'pointer';
        increaseBtn.style.margin = '0 3px';
        increaseBtn.style.fontSize = '18px';
        speedControlDiv.appendChild(increaseBtn);

        autoSpeedDiv.appendChild(speedControlDiv);

        const infoTip = document.createElement('div');
        infoTip.className = 'info-tip';
        infoTip.style.marginTop = '5px';

        const infoText = document.createElement('span');
        infoText.style.fontSize = '0.8em';
        infoText.style.color = '#0066cc'; // 更深的蓝色，提高对比度
        infoText.style.display = 'block';
        infoText.style.fontWeight = 'bold'; // 加粗文字
        infoText.textContent = '自动模式支持后台运行，切换标签页游戏将继续执行';

        infoTip.appendChild(infoText);
        autoSpeedDiv.appendChild(infoTip);

        betSection.appendChild(autoSpeedDiv);

        // 自动补充筹码设置
        const autoRefillDiv = document.createElement('div');
        autoRefillDiv.className = 'setting-section';
        autoRefillDiv.style.marginTop = '15px';
        autoRefillDiv.style.padding = '10px';
        autoRefillDiv.style.backgroundColor = '#d0e8ff';
        autoRefillDiv.style.borderRadius = '4px';

        const autoRefillLabel = document.createElement('h4');
        autoRefillLabel.textContent = '自动补充筹码';
        autoRefillLabel.style.color = '#333';
        autoRefillLabel.style.marginTop = '0';
        autoRefillDiv.appendChild(autoRefillLabel);

        // 启用自动补充筹码开关
        const enableRefillDiv = document.createElement('div');
        enableRefillDiv.className = 'switch-container';

        const enableRefillLabel = document.createElement('label');
        enableRefillLabel.textContent = '启用自动补充筹码';
        enableRefillLabel.htmlFor = 'counting-auto-refill';
        enableRefillLabel.style.fontWeight = 'bold';
        enableRefillLabel.style.color = '#0066cc';
        enableRefillDiv.appendChild(enableRefillLabel);

        const enableRefillSwitch = document.createElement('input');
        enableRefillSwitch.type = 'checkbox';
        enableRefillSwitch.id = 'counting-auto-refill';
        enableRefillDiv.appendChild(enableRefillSwitch);

        autoRefillDiv.appendChild(enableRefillDiv);

        // 筹码阈值设置
        const thresholdDiv = document.createElement('div');
        thresholdDiv.className = 'settings-row';
        thresholdDiv.style.marginTop = '10px';

        const thresholdLabel = document.createElement('label');
        thresholdLabel.textContent = '筹码阈值：';
        thresholdLabel.htmlFor = 'counting-refill-threshold';
        thresholdLabel.style.color = '#333';
        thresholdDiv.appendChild(thresholdLabel);

        const thresholdInput = document.createElement('input');
        thresholdInput.type = 'number';
        thresholdInput.id = 'counting-refill-threshold';
        thresholdInput.min = '100';
        thresholdInput.max = '10000';
        thresholdInput.value = '1000';
        thresholdInput.step = '100';
        thresholdInput.style.width = '100px';
        thresholdDiv.appendChild(thresholdInput);

        autoRefillDiv.appendChild(thresholdDiv);

        // 补充金额设置
        const amountDiv = document.createElement('div');
        amountDiv.className = 'settings-row';
        amountDiv.style.marginTop = '10px';

        const amountLabel = document.createElement('label');
        amountLabel.textContent = '补充金额：';
        amountLabel.htmlFor = 'counting-refill-amount';
        amountLabel.style.color = '#333';
        amountDiv.appendChild(amountLabel);

        const amountInput = document.createElement('input');
        amountInput.type = 'number';
        amountInput.id = 'counting-refill-amount';
        amountInput.min = '1000';
        amountInput.max = '100000';
        amountInput.value = '10000';
        amountInput.step = '1000';
        amountInput.style.width = '100px';
        amountDiv.appendChild(amountInput);

        autoRefillDiv.appendChild(amountDiv);

        betSection.appendChild(autoRefillDiv);

        // 自动下注阈值设置
        const thresholdsDiv = document.createElement('div');
        thresholdsDiv.className = 'thresholds-container';
        thresholdsDiv.id = 'auto-bet-thresholds';
        thresholdsDiv.style.marginTop = '15px';

        // 添加默认阈值
        const defaultThresholds = [
            { trueCount: -999, bet: 100 },
            { trueCount: 1, bet: 200 },
            { trueCount: 2, bet: 300 },
            { trueCount: 3, bet: 400 },
            { trueCount: 4, bet: 500 },
            { trueCount: 5, bet: 600 }
        ];

        defaultThresholds.forEach((threshold, index) => {
            const thresholdRow = this.createThresholdRow(index, threshold.trueCount, threshold.bet);
            thresholdsDiv.appendChild(thresholdRow);
        });

        // 添加新阈值按钮
        const addThresholdBtn = document.createElement('button');
        addThresholdBtn.id = 'add-threshold-btn';
        addThresholdBtn.className = 'btn btn-sm btn-primary';
        addThresholdBtn.textContent = '添加阈值';
        thresholdsDiv.appendChild(addThresholdBtn);

        betSection.appendChild(thresholdsDiv);
        content.appendChild(betSection);

        // 保存按钮
        const saveBtn = document.createElement('button');
        saveBtn.id = 'save-counting-settings';
        saveBtn.className = 'btn btn-success';
        saveBtn.textContent = '保存设置';
        content.appendChild(saveBtn);

        panel.appendChild(content);

        // 添加到文档
        document.body.appendChild(panel);
    }

    /**
     * 创建阈值设置行
     * @param {number} index - 阈值索引
     * @param {number} trueCount - 真数阈值
     * @param {number} bet - 下注金额
     * @returns {HTMLElement} 阈值设置行元素
     */
    createThresholdRow(index, trueCount, bet) {
        const row = document.createElement('div');
        row.className = 'threshold-row';
        row.dataset.index = index;

        // 真数输入
        const trueCountLabel = document.createElement('label');
        trueCountLabel.textContent = '真数 ≥';
        trueCountLabel.style.color = '#333'; // 确保文字颜色可见
        row.appendChild(trueCountLabel);

        const trueCountInput = document.createElement('input');
        trueCountInput.type = 'number';
        trueCountInput.className = 'true-count-input';
        trueCountInput.value = trueCount;
        trueCountInput.step = '0.5';
        row.appendChild(trueCountInput);

        // 下注金额输入
        const betLabel = document.createElement('label');
        betLabel.textContent = '下注金额:';
        betLabel.style.color = '#333'; // 确保文字颜色可见
        row.appendChild(betLabel);

        const betInput = document.createElement('input');
        betInput.type = 'number';
        betInput.className = 'bet-amount-input';
        betInput.value = bet;
        betInput.min = '1';
        row.appendChild(betInput);

        // 删除按钮
        if (index > 0) { // 第一个阈值不能删除
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-threshold-btn';
            deleteBtn.textContent = '×';
            deleteBtn.title = '删除此阈值';
            // 确保按钮是正方形
            deleteBtn.style.width = '36px';
            deleteBtn.style.height = '36px';
            deleteBtn.style.minWidth = '36px';
            deleteBtn.style.maxWidth = '36px';
            deleteBtn.style.flex = '0 0 36px';
            // 使用flex布局居中显示
            deleteBtn.style.display = 'flex';
            deleteBtn.style.justifyContent = 'center';
            deleteBtn.style.alignItems = 'center';
            row.appendChild(deleteBtn);
        }

        return row;
    }

    /**
     * 绑定事件处理函数
     */
    bindEvents() {
        // 算牌系统按钮点击事件
        const countingBtn = document.getElementById('card-counting-btn');
        if (countingBtn) {
            countingBtn.addEventListener('click', () => {
                this.showPanel();
            });
        }

        // 关闭按钮点击事件
        const closeBtn = document.querySelector('#card-counting-panel .close-button');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hidePanel();
            });
        }

        // 保存按钮点击事件
        const saveBtn = document.getElementById('save-counting-settings');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // 添加阈值按钮点击事件
        const addThresholdBtn = document.getElementById('add-threshold-btn');
        if (addThresholdBtn) {
            addThresholdBtn.addEventListener('click', () => {
                this.addThreshold();
            });
        }

        // 为阈值容器添加事件委托，处理删除按钮点击
        const thresholdsContainer = document.getElementById('auto-bet-thresholds');
        if (thresholdsContainer) {
            thresholdsContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('delete-threshold-btn')) {
                    const row = e.target.closest('.threshold-row');
                    if (row) {
                        row.remove();
                    }
                }
            });
        }

        // 自动模式开关事件
        const autoModeSwitch = document.getElementById('counting-auto-mode');
        if (autoModeSwitch) {
            autoModeSwitch.addEventListener('change', (e) => {
                // 同步更新游戏设置菜单中的自动模式开关
                const gameAutoModeSwitch = document.getElementById('auto-mode');
                if (gameAutoModeSwitch) {
                    gameAutoModeSwitch.checked = e.target.checked;
                    // 触发原始自动模式开关的change事件
                    const event = new Event('change');
                    gameAutoModeSwitch.dispatchEvent(event);
                }

                // 显示或隐藏速度控制
                const speedSettings = document.getElementById('counting-auto-speed-settings');
                if (speedSettings) {
                    speedSettings.style.display = e.target.checked ? 'block' : 'none';
                }
            });
        }

        // 自动模式速度滑块事件
        const speedSlider = document.getElementById('counting-auto-speed');
        const speedValue = document.getElementById('counting-speed-value');
        if (speedSlider && speedValue) {
            speedSlider.addEventListener('input', (e) => {
                // 更新显示的速度值
                speedValue.textContent = e.target.value;

                // 同步更新游戏设置菜单中的速度滑块
                const gameSpeedSlider = document.getElementById('auto-speed');
                const gameSpeedValue = document.getElementById('speed-value');
                if (gameSpeedSlider && gameSpeedValue) {
                    gameSpeedSlider.value = e.target.value;
                    gameSpeedValue.textContent = e.target.value;

                    // 更新游戏自动延迟
                    if (window.game) {
                        window.game.setAutoDelay(parseInt(e.target.value));
                    }
                }
            });
        }

        // 减速按钮事件
        const decreaseBtn = document.getElementById('counting-decrease-speed');
        if (decreaseBtn && speedSlider && speedValue) {
            decreaseBtn.addEventListener('click', () => {
                // 获取当前速度值
                let currentSpeed = parseInt(speedSlider.value);
                // 确保不低于最小值
                currentSpeed = Math.max(parseInt(speedSlider.min), currentSpeed - 100);
                // 更新滑动条和显示值
                speedSlider.value = currentSpeed;
                speedValue.textContent = currentSpeed;

                // 同步更新游戏设置菜单中的速度
                const gameSpeedSlider = document.getElementById('auto-speed');
                const gameSpeedValue = document.getElementById('speed-value');
                if (gameSpeedSlider && gameSpeedValue) {
                    gameSpeedSlider.value = currentSpeed;
                    gameSpeedValue.textContent = currentSpeed;
                }

                // 更新游戏自动延迟
                if (window.game) {
                    window.game.setAutoDelay(currentSpeed);
                }
            });
        }

        // 加速按钮事件
        const increaseBtn = document.getElementById('counting-increase-speed');
        if (increaseBtn && speedSlider && speedValue) {
            increaseBtn.addEventListener('click', () => {
                // 获取当前速度值
                let currentSpeed = parseInt(speedSlider.value);
                // 确保不超过最大值
                currentSpeed = Math.min(parseInt(speedSlider.max), currentSpeed + 100);
                // 更新滑动条和显示值
                speedSlider.value = currentSpeed;
                speedValue.textContent = currentSpeed;

                // 同步更新游戏设置菜单中的速度
                const gameSpeedSlider = document.getElementById('auto-speed');
                const gameSpeedValue = document.getElementById('speed-value');
                if (gameSpeedSlider && gameSpeedValue) {
                    gameSpeedSlider.value = currentSpeed;
                    gameSpeedValue.textContent = currentSpeed;
                }

                // 更新游戏自动延迟
                if (window.game) {
                    window.game.setAutoDelay(currentSpeed);
                }
            });
        }
    }

    /**
     * 显示算牌系统设置面板
     */
    showPanel() {
        const panel = document.getElementById('card-counting-panel');
        if (panel) {
            // 更新当前选择的算牌系统
            if (window.cardCountingSystem) {
                const systemSelect = document.getElementById('counting-system-select');
                if (systemSelect) {
                    systemSelect.value = window.cardCountingSystem.getCurrentSystem();
                }

                // 更新自动下注开关状态
                const enableAutoBet = document.getElementById('enable-auto-bet');
                if (enableAutoBet) {
                    enableAutoBet.checked = window.cardCountingSystem.autoBetStrategy.enabled;
                }

                // 更新阈值设置
                this.updateThresholds(window.cardCountingSystem.autoBetStrategy.thresholds);
            }

            // 同步自动模式状态
            const autoModeSwitch = document.getElementById('counting-auto-mode');
            const autoSpeedSettings = document.getElementById('counting-auto-speed-settings');
            const gameAutoModeSwitch = document.getElementById('auto-mode');

            if (autoModeSwitch && autoSpeedSettings && gameAutoModeSwitch) {
                // 同步自动模式开关状态
                autoModeSwitch.checked = gameAutoModeSwitch.checked;

                // 显示或隐藏速度设置
                autoSpeedSettings.style.display = autoModeSwitch.checked ? 'block' : 'none';
            }

            // 同步速度滑块值
            const speedSlider = document.getElementById('counting-auto-speed');
            const speedValue = document.getElementById('counting-speed-value');
            const gameSpeedSlider = document.getElementById('auto-speed');

            if (speedSlider && speedValue && gameSpeedSlider) {
                speedSlider.value = gameSpeedSlider.value;
                speedValue.textContent = gameSpeedSlider.value;
                // 确保速度值文字颜色正确
                speedValue.style.color = '#333';
                speedValue.style.fontWeight = 'bold';
            }

            // 同步自动补充筹码设置
            if (window.game && window.game.autoRefillChips) {
                const enableAutoRefill = document.getElementById('counting-auto-refill');
                const refillThresholdInput = document.getElementById('counting-refill-threshold');
                const refillAmountInput = document.getElementById('counting-refill-amount');

                if (enableAutoRefill) {
                    enableAutoRefill.checked = window.game.autoRefillChips.enabled;
                }

                if (refillThresholdInput) {
                    refillThresholdInput.value = window.game.autoRefillChips.threshold;
                }

                if (refillAmountInput) {
                    refillAmountInput.value = window.game.autoRefillChips.amount;
                }
            }

            panel.style.display = 'block';
        }
    }

    /**
     * 隐藏算牌系统设置面板
     */
    hidePanel() {
        const panel = document.getElementById('card-counting-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 更新阈值设置
     * @param {Array} thresholds - 阈值数组
     */
    updateThresholds(thresholds) {
        const container = document.getElementById('auto-bet-thresholds');
        if (!container) return;

        // 清除现有阈值行（除了添加按钮）
        const addBtn = document.getElementById('add-threshold-btn');
        container.innerHTML = '';

        // 添加新的阈值行
        thresholds.forEach((threshold, index) => {
            const row = this.createThresholdRow(index, threshold.trueCount, threshold.bet);
            container.appendChild(row);
        });

        // 重新添加添加按钮
        container.appendChild(addBtn);
    }

    /**
     * 添加新的阈值设置行
     */
    addThreshold() {
        const container = document.getElementById('auto-bet-thresholds');
        if (!container) return;

        // 获取当前行数
        const rows = container.querySelectorAll('.threshold-row');
        const index = rows.length;

        // 创建新行
        const newRow = this.createThresholdRow(index, 0, 100);

        // 插入到添加按钮之前
        const addBtn = document.getElementById('add-threshold-btn');
        container.insertBefore(newRow, addBtn);
    }

    /**
     * 保存算牌系统设置
     */
    saveSettings() {
        if (!window.cardCountingSystem) {
            cardCountingUILogger.error('算牌系统未初始化');
            return;
        }

        // 获取选择的算牌系统
        const systemSelect = document.getElementById('counting-system-select');
        if (systemSelect) {
            window.cardCountingSystem.setCountingSystem(systemSelect.value);
        }

        // 获取自动下注设置
        const enableAutoBet = document.getElementById('enable-auto-bet');
        const enabled = enableAutoBet ? enableAutoBet.checked : false;

        // 获取阈值设置
        const thresholds = [];
        const thresholdRows = document.querySelectorAll('.threshold-row');
        thresholdRows.forEach(row => {
            const trueCountInput = row.querySelector('.true-count-input');
            const betInput = row.querySelector('.bet-amount-input');

            if (trueCountInput && betInput) {
                const trueCount = parseFloat(trueCountInput.value);
                const bet = parseInt(betInput.value);

                if (!isNaN(trueCount) && !isNaN(bet) && bet > 0) {
                    thresholds.push({ trueCount, bet });
                }
            }
        });

        // 获取自动补充筹码设置
        const enableAutoRefill = document.getElementById('counting-auto-refill');
        const autoRefillEnabled = enableAutoRefill ? enableAutoRefill.checked : false;

        const refillThresholdInput = document.getElementById('counting-refill-threshold');
        const refillAmountInput = document.getElementById('counting-refill-amount');

        let refillThreshold = 1000;
        let refillAmount = 10000;

        if (refillThresholdInput && !isNaN(parseInt(refillThresholdInput.value))) {
            refillThreshold = parseInt(refillThresholdInput.value);
        }

        if (refillAmountInput && !isNaN(parseInt(refillAmountInput.value))) {
            refillAmount = parseInt(refillAmountInput.value);
        }

        // 保存自动补充筹码设置到游戏对象
        if (window.game) {
            window.game.setAutoRefillChips(autoRefillEnabled, refillThreshold, refillAmount);
            cardCountingUILogger.info(`算牌系统设置自动补充筹码: 启用=${autoRefillEnabled}, 阈值=${refillThreshold}, 金额=${refillAmount}`);
        }

        // 设置自动下注策略
        window.cardCountingSystem.setAutoBetStrategy(enabled, thresholds);

        // 立即更新牌库统计显示，确保算牌系统名称同步更新
        if (window.game) {
            cardCountingUILogger.info('立即更新牌库统计显示');
            // 直接调用updateDeckInfo方法更新牌库统计
            window.game.updateDeckInfo();
            // 或者调用updateUI方法更新整个UI
            window.game.updateUI();
        }

        // 如果启用了自动下注，并且当前是下注阶段，立即触发自动下注
        if (enabled && window.game && window.game.gameState === 'betting') {
            cardCountingUILogger.info('当前处于下注阶段，立即触发自动下注');
            setTimeout(() => {
                window.game.autoPlaceBets();
            }, 500);
        }

        // 隐藏面板
        this.hidePanel();

        // 显示保存成功提示
        alert('算牌系统设置已保存');
    }
}

// 创建全局实例
window.cardCountingUI = new CardCountingUI();
