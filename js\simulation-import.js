/**
 * 模拟系统数据导入模块
 * 提供导入之前导出的模拟配置和结果的功能
 */

/**
 * 导入处理类
 * 处理模拟数据的导入和应用
 */
class SimulationImport {
    /**
     * 构造函数
     * @param {SimulationUI} simulationUI - 模拟UI实例
     */
    constructor(simulationUI) {
        this.simulationUI = simulationUI;
        this.fileInput = null;
    }

    /**
     * 初始化导入功能
     */
    init() {
        // 创建隐藏的文件输入元素
        this.fileInput = document.createElement('input');
        this.fileInput.type = 'file';
        this.fileInput.accept = '.json,application/json';
        this.fileInput.style.display = 'none';
        document.body.appendChild(this.fileInput);

        // 添加文件选择事件监听
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    /**
     * 显示文件选择对话框
     */
    showFileDialog() {
        if (this.fileInput) {
            this.fileInput.click();
        }
    }

    /**
     * 处理文件选择事件
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 检查文件类型
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            this.simulationUI.showError('请选择有效的JSON文件');
            this.fileInput.value = ''; // 清空文件输入
            return;
        }

        // 读取文件内容
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                this.processImportedData(data);
            } catch (error) {
                console.error('解析导入文件失败', error);
                this.simulationUI.showError(`解析导入文件失败: ${error.message}`);
            }
            this.fileInput.value = ''; // 清空文件输入
        };

        reader.onerror = (error) => {
            console.error('读取文件失败', error);
            this.simulationUI.showError('读取文件失败');
            this.fileInput.value = ''; // 清空文件输入
        };

        reader.readAsText(file);
    }

    /**
     * 处理导入的数据
     * @param {Object} data - 导入的JSON数据
     */
    processImportedData(data) {
        // 验证数据格式
        if (!data.simulationConfig) {
            this.simulationUI.showError('导入的文件不包含有效的模拟配置');
            return;
        }

        try {
            // 应用导入的配置
            this.applyImportedConfig(data.simulationConfig);

            // 如果有结果数据，也导入
            if (data.simulationResult) {
                this.applyImportedResult(data.simulationResult);
            }

            // 显示成功消息
            this.simulationUI.showMessage('成功导入模拟数据');

            // 更新UI
            this.updateUI();
        } catch (error) {
            console.error('应用导入数据失败', error);
            this.simulationUI.showError(`应用导入数据失败: ${error.message}`);
        }
    }

    /**
     * 应用导入的配置
     * @param {Object} config - 导入的配置对象
     */
    applyImportedConfig(config) {
        // 复制基本配置属性
        const targetConfig = this.simulationUI.config;

        // 基础游戏设置
        targetConfig.numberOfGames = config.numberOfGames || 10000;
        targetConfig.numberOfDecks = config.numberOfDecks || 8;
        targetConfig.penetrationRate = config.penetrationRate || 0.65;
        targetConfig.dealerStandSoft17 = config.dealerStandSoft17 !== undefined ? config.dealerStandSoft17 : true;
        targetConfig.playerCount = config.playerCount || 1;
        targetConfig.startingChips = config.startingChips || 100000;
        targetConfig.batchSize = config.batchSize || 1000;

        // 算牌系统
        targetConfig.countingSystem = config.countingSystem || 'hi-lo';

        // 下注策略
        if (config.bettingStrategy) {
            targetConfig.bettingStrategy.enabled = config.bettingStrategy.enabled !== undefined ?
                config.bettingStrategy.enabled : true;
            targetConfig.bettingStrategy.useCountingBasedBetting = config.bettingStrategy.useCountingBasedBetting !== undefined ?
                config.bettingStrategy.useCountingBasedBetting : true;
            targetConfig.bettingStrategy.fixedBet = config.bettingStrategy.fixedBet || 100;

            // 复制阈值设置
            if (Array.isArray(config.bettingStrategy.thresholds)) {
                targetConfig.bettingStrategy.thresholds = [...config.bettingStrategy.thresholds];
            }
        }

        // 玩家策略
        if (config.playerStrategy) {
            targetConfig.playerStrategy = { ...config.playerStrategy };
        }

        console.log('已应用导入的配置', targetConfig);
    }

    /**
     * 应用导入的结果
     * @param {Object} result - 导入的结果对象
     */
    applyImportedResult(result) {
        try {
            // 如果引擎不存在，初始化它
            if (!this.simulationUI.engine) {
                this.simulationUI.initializeEngine();
            }

            // 创建结果对象，确保所有属性都有默认值
            this.simulationUI.engine.result = {
                totalGames: result.totalGames || 0,
                completedGames: result.completedGames || 0,
                totalPlayers: result.totalPlayers || 1,
                initialChips: result.initialChips || 0,
                initialTotalChips: result.initialTotalChips || 0,  // 确保这个属性存在
                finalChips: result.finalChips || 0,
                netProfit: result.netProfit || 0,
                profitPerGame: result.profitPerGame || 0,
                winCount: result.winCount || 0,
                loseCount: result.loseCount || 0,
                pushCount: result.pushCount || 0,
                blackjackCount: result.blackjackCount || 0,
                winRate: result.winRate || 0,
                blackjackRate: result.blackjackRate || 0,
                doubleCount: result.doubleCount || 0,
                doubleWinCount: result.doubleWinCount || 0,  // 确保这个属性存在
                splitCount: result.splitCount || 0,
                surrenderCount: result.surrenderCount || 0,
                doubleSuccessRate: result.doubleSuccessRate || 0,
                maxDrawdown: result.maxDrawdown || 0,
                simulationTime: result.simulationTime || 0,
                gamesPerSecond: result.gamesPerSecond || 0,
                betHistory: Array.isArray(result.betHistory) ? result.betHistory : [],
                chipHistory: Array.isArray(result.chipHistory) ? result.chipHistory : [],
                chipsCurve: Array.isArray(result.chipsCurve) ? result.chipsCurve : [],  // 确保这个属性存在
                trueCountDistribution: result.trueCountDistribution || {},
                timestamp: result.timestamp || new Date().toISOString(),
                playerStats: result.playerStats || {}  // 确保这个属性存在
            };

            // 确保playerStats对象包含必要的属性
            if (!this.simulationUI.engine.result.playerStats) {
                this.simulationUI.engine.result.playerStats = {};
            }

            // 确保所有必要的属性都存在
            this.ensureResultProperties(this.simulationUI.engine.result);

            console.log('已应用导入的结果', this.simulationUI.engine.result);
        } catch (error) {
            console.error('应用导入结果时出错:', error);
            throw new Error(`应用导入结果时出错: ${error.message}`);
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        try {
            // 更新配置表单
            this.simulationUI.updateConfigForm();

            // 更新真数阈值表格
            this.simulationUI.updateThresholdTable();

            // 如果有结果，更新结果显示
            if (this.simulationUI.engine && this.simulationUI.engine.result) {
                // 确保结果对象中的所有必要属性都存在
                this.ensureResultProperties(this.simulationUI.engine.result);

                // 显示结果面板
                if (this.simulationUI.elements && this.simulationUI.elements.resultsPanel) {
                    this.simulationUI.elements.resultsPanel.style.display = 'block';
                }

                // 更新结果显示
                if (typeof this.simulationUI.displayResults === 'function') {
                    this.simulationUI.displayResults(this.simulationUI.engine.result);
                } else {
                    console.error('displayResults方法不存在');
                }
            }
        } catch (error) {
            console.error('更新UI时出错:', error);
            this.simulationUI.showError(`更新UI时出错: ${error.message}`);
        }
    }

    /**
     * 确保结果对象中包含所有必要的属性
     * @param {Object} result - 结果对象
     */
    ensureResultProperties(result) {
        // 确保基本属性存在
        const defaultProperties = {
            totalGames: 0,
            completedGames: 0,
            totalPlayers: 1,
            initialChips: 0,
            initialTotalChips: 0,
            finalChips: 0,
            netProfit: 0,
            profitPerGame: 0,
            winCount: 0,
            loseCount: 0,
            pushCount: 0,
            blackjackCount: 0,
            winRate: 0,
            blackjackRate: 0,
            doubleCount: 0,
            splitCount: 0,
            surrenderCount: 0,
            doubleSuccessRate: 0,
            maxDrawdown: 0,
            simulationTime: 0,
            gamesPerSecond: 0
        };

        // 为缺失的属性设置默认值
        for (const [key, value] of Object.entries(defaultProperties)) {
            if (result[key] === undefined || result[key] === null) {
                result[key] = value;
            }
        }

        // 确保数组属性存在
        if (!Array.isArray(result.betHistory)) {
            result.betHistory = [];
        }

        if (!Array.isArray(result.chipHistory)) {
            result.chipHistory = [];
        }

        // 确保对象属性存在
        if (!result.playerStats) {
            result.playerStats = {};
        }

        if (!result.trueCountDistribution) {
            result.trueCountDistribution = {};
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SimulationImport
    };
}
