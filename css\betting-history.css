/* 历史记录面板样式 */
.history-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1200px; /* 增加最大宽度 */
    max-height: 90vh;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 面板标题 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(90deg, #2563eb, #3b82f6);
    color: white;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

/* 关闭按钮 */
.close-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.close-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 内容区域 */
.history-content {
    padding: 20px;
    overflow-y: auto; /* 只在内容区域显示垂直滚动条 */
    overflow-x: hidden; /* 防止水平滚动条 */
    max-height: calc(90vh - 60px);
    flex: 1; /* 让内容区域占据剩余空间 */
}

/* 筛选器容器 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 筛选项 */
.filter-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-item label {
    color: #e2e8f0;
    font-weight: 500;
    white-space: nowrap;
}

.filter-item select {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(30, 41, 59, 0.8);
    color: white;
    font-size: 14px;
    min-width: 150px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
}

.filter-item select:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(30, 41, 59, 0.9);
}

.filter-item select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 重置筛选按钮 */
.reset-filter-btn {
    padding: 8px 15px;
    background: linear-gradient(135deg, #475569, #334155);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: auto;
}

.reset-filter-btn:hover {
    background: linear-gradient(135deg, #334155, #1e293b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 历史记录表格 */
.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    color: #e2e8f0;
    table-layout: fixed; /* 固定表格布局 */
    font-size: 1rem;
}

.history-table thead {
    background: rgba(30, 41, 59, 0.8);
}

.history-table th {
    padding: 12px 15px;
    text-align: center; /* 居中对齐 */
    font-weight: 600;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    overflow: visible;
    height: 42px; /* 增加表头高度 */
}

.history-table td {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center; /* 居中对齐 */
    white-space: normal; /* 允许文本换行 */
    overflow: visible; /* 确保内容不被裁剪 */
    height: auto; /* 自动调整行高 */
    min-height: 36px; /* 最小行高 */
    box-sizing: border-box;
}

/* 设置各列的宽度 */
.history-table th:nth-child(1), /* 局数 */
.history-table td:nth-child(1) {
    width: 5%;
    min-width: 50px;
}

.history-table th:nth-child(2), /* 玩家标识 */
.history-table td:nth-child(2) {
    width: 10%;
    min-width: 90px;
}

.history-table th:nth-child(3), /* 下注金额 */
.history-table td:nth-child(3) {
    width: 8%;
    min-width: 70px;
}

.history-table th:nth-child(4), /* 结果 */
.history-table td:nth-child(4) {
    width: 10%;
    min-width: 80px;
}

.history-table th:nth-child(5), /* 玩家手牌 */
.history-table td:nth-child(5) {
    width: 15%;
    min-width: 120px;
}

.history-table th:nth-child(6), /* 庄家手牌 */
.history-table td:nth-child(6) {
    width: 15%;
    min-width: 120px;
}

.history-table th:nth-child(7), /* 盈亏 */
.history-table td:nth-child(7) {
    width: 8%;
    min-width: 60px;
}

.history-table th:nth-child(8), /* 时间 */
.history-table td:nth-child(8) {
    width: 12%;
    min-width: 100px;
}

.history-table tbody tr {
    transition: all 0.3s ease;
}

.history-table tbody tr:hover {
    background: rgba(30, 41, 59, 0.5);
}

/* 分牌记录样式 */
.split-hand-row {
    background-color: rgba(59, 130, 246, 0.05) !important;
    border-left: 3px solid #3b82f6;
}

.first-split-hand {
    border-top: 2px solid #3b82f6;
}

.last-split-hand {
    border-bottom: 2px solid #3b82f6;
}

/* 手牌标识样式 */
.hand-indicator {
    display: inline-block;
    font-size: 0.85em;
    color: #60a5fa;
    font-weight: bold;
    padding: 2px 4px;
    background-color: rgba(96, 165, 250, 0.1);
    border-radius: 3px;
    white-space: nowrap;
    margin-left: 4px;
}

/* 操作类型标记样式 */
.action-type {
    display: inline-block;
    font-size: 0.85em;
    font-weight: bold;
    color: #f8fafc;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-shadow: none;
}

/* 不同操作类型的颜色 */
.action-type.double {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); /* 加倍按钮颜色 */
}

.action-type.split {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%); /* 分牌按钮颜色 */
}

.action-type.surrender {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); /* 投降按钮颜色 */
}

.action-type.normal {
    background: linear-gradient(135deg, #6366f1, #4f46e5); /* 普通操作使用游戏控制按钮颜色 */
}

/* 结果颜色 */
.result-win {
    color: #10b981;
    font-weight: 600;
}

.result-lose {
    color: #ef4444;
    font-weight: 600;
}

.result-push {
    color: #f59e0b;
    font-weight: 600;
}

.profit-positive {
    color: #10b981;
    font-weight: 600;
}

.profit-negative {
    color: #ef4444;
    font-weight: 600;
}

/* 统计信息 */
.history-stats {
    margin-top: 20px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
    color: #e2e8f0;
}

.stats-table th {
    padding: 10px;
    text-align: center;
    font-weight: 600;
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.stats-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.win-count {
    color: #10b981;
    font-weight: 600;
}

.lose-count {
    color: #ef4444;
    font-weight: 600;
}

.blackjack-count {
    color: #8b5cf6;
    font-weight: 600;
}

/* 玩家总数据区域 */
.player-total-stats {
    margin-top: 20px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.total-stats-table {
    width: 100%;
    border-collapse: collapse;
    color: #e2e8f0;
}

.total-stats-table th {
    padding: 10px;
    text-align: center;
    font-weight: 600;
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.total-stats-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 分页控件 */
.history-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    gap: 10px;
}

.pagination-button {
    padding: 8px 15px;
    background: linear-gradient(135deg, #475569, #334155);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #334155, #1e293b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: #a1a1aa;
    font-size: 0.9rem;
    min-width: 180px;
    text-align: center;
}

/* 无数据提示 */
.no-data-message {
    text-align: center;
    padding: 20px;
    color: #a1a1aa;
    font-style: italic;
}

/* 扑克牌花色颜色 */
.history-table td span.hearts,
.history-table td span.diamonds,
.history-table td span.clubs,
.history-table td span.spades {
    display: inline-block;
    margin: 0 1px;
}

.history-table td span.hearts,
.history-table td span.diamonds {
    color: #ef4444;
}

.history-table td span.clubs,
.history-table td span.spades {
    color: #ffffff;
    font-weight: bold;
}

/* 清除历史按钮 */
.clear-history-button {
    display: block;
    margin: 20px auto 0;
    padding: 10px 20px;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.clear-history-button:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}
