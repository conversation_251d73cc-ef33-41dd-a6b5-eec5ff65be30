/* 21点快速模拟系统样式 */

.simulation-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 98%;
    max-width: 2200px; /* 进一步增加最大宽度 */
    height: 98vh;
    max-height: 98vh;
    background-color: #192231;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: #e6e6e6;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1.05rem; /* 增加字体大小 */
}

.simulation-header {
    padding: 10px 15px;
    background-color: #121a26;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #2c3747;
}

.simulation-header h2 {
    margin: 0;
    color: #38bdf8;
    font-size: 1.5rem;
}

.simulation-close-button {
    background: none;
    border: none;
    color: #e6e6e6;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.simulation-close-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.simulation-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding: 15px;
    gap: 15px;
}

.simulation-panel {
    background-color: #1e293b;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(98vh - 100px);
}

.simulation-config-panel {
    flex: 0 0 600px;
    min-width: 600px;
}

.simulation-control-panel {
    flex: 0 0 250px;
}

.simulation-results-panel {
    flex: 1;
    min-width: 800px; /* 确保结果面板有足够的最小宽度 */
}

.simulation-panel h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #38bdf8;
    font-size: 1.2rem;
    border-bottom: 1px solid #38bdf8;
    padding-bottom: 5px;
}

/* 表单样式 */
.simulation-form-group {
    border: 1px solid #2c3747;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
    background-color: rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

.simulation-form-group legend {
    color: #38bdf8;
    padding: 0 5px;
    font-weight: bold;
}

.simulation-form-row {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 100%;
    box-sizing: border-box;
}

.simulation-form-row label {
    margin-right: 15px;
    flex: 0 0 45%;
    font-size: 0.95rem;
    line-height: 1.4;
    display: flex;
    align-items: center;
    min-height: 32px;
    white-space: normal;
}

.simulation-form-row input[type="number"],
.simulation-form-row select {
    background-color: #293547;
    border: 1px solid #3e4c63;
    color: #e6e6e6;
    border-radius: 4px;
    padding: 5px 8px;
    flex: 0 0 53%;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
}

.simulation-form-row input[type="number"]:focus,
.simulation-form-row select:focus {
    outline: none;
    border-color: #38bdf8;
}

/* 范围滑块 */
.simulation-range-container {
    display: flex;
    flex-direction: column;
    flex: 0 0 53%;
    min-width: 0;
}

.simulation-range-value {
    margin-bottom: 5px;
    text-align: center;
    color: #38bdf8;
}

.simulation-range-input {
    width: 100%;
    -webkit-appearance: none;
    height: 6px;
    background: #293547;
    border-radius: 3px;
    outline: none;
}

.simulation-range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #38bdf8;
    border-radius: 50%;
    cursor: pointer;
}

.simulation-range-input::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #38bdf8;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* 开关切换样式 */
.simulation-toggle-container {
    display: flex;
    width: 100%;
    align-items: center;
}

.simulation-toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.simulation-toggle-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
}

.simulation-toggle-slider {
    position: relative;
    display: inline-block;
    min-width: 40px;
    flex: 0 0 40px;
    height: 20px;
    background-color: #293547;
    border-radius: 10px;
    margin-right: 15px;
    transition: background-color 0.3s;
}

.simulation-toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: #e6e6e6;
    border-radius: 50%;
    transition: transform 0.3s;
}

.simulation-toggle-input:checked + .simulation-toggle-label .simulation-toggle-slider {
    background-color: #38bdf8;
}

.simulation-toggle-input:checked + .simulation-toggle-label .simulation-toggle-slider:before {
    transform: translateX(20px);
}

.simulation-toggle-text {
    flex: 1;
    font-size: 0.95rem;
    white-space: normal;
    line-height: 1.4;
}

/* 下注策略预设 */
.simulation-preset-buttons {
    display: flex;
    gap: 8px;
    width: 100%;
    flex-wrap: nowrap;
}

.simulation-preset-button {
    flex: 1;
    background-color: #2c5282;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 0.95rem;
    white-space: nowrap;
    min-width: 75px;
    text-align: center;
}

.simulation-preset-button:hover {
    background-color: #3182ce;
}

/* 阈值表格样式 */
.simulation-threshold-container {
    margin: 15px 0;
    width: 100%;
    overflow-x: auto;
}

.simulation-threshold-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
    table-layout: fixed;
}

.simulation-threshold-table th,
.simulation-threshold-table td {
    border: 1px solid #2c3747;
    padding: 6px;
    text-align: center;
    font-size: 0.9rem;
}

.simulation-threshold-table th {
    background-color: #293547;
    color: #38bdf8;
    font-weight: bold;
}

.simulation-threshold-input {
    width: 100%;
    max-width: 100px;
    background-color: #293547;
    border: 1px solid #3e4c63;
    color: #e6e6e6;
    border-radius: 4px;
    padding: 6px;
    text-align: center;
    box-sizing: border-box;
}

.simulation-threshold-input:focus {
    border-color: #38bdf8;
    outline: none;
}

.simulation-delete-threshold-button {
    background-color: #9b2c2c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 0.85rem;
}

.simulation-delete-threshold-button:hover {
    background-color: #c53030;
}

.simulation-add-threshold-button {
    background-color: #2c5282;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    margin-top: 5px;
    width: 100%;
    font-size: 0.95rem;
}

.simulation-add-threshold-button:hover {
    background-color: #3182ce;
}

/* 提交按钮 */
.simulation-submit-button {
    width: 100%;
    background-color: #38bdf8;
    color: #192231;
    border: none;
    border-radius: 4px;
    padding: 10px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 15px;
}

.simulation-submit-button:hover {
    background-color: #0ea5e9;
}

/* 控制面板样式 */
.simulation-button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.simulation-control-button {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
}

.simulation-start-button {
    background-color: #059669;
}

.simulation-start-button:hover {
    background-color: #10b981;
}

.simulation-pause-button {
    background-color: #9f580a;
}

.simulation-pause-button:hover {
    background-color: #d97706;
}

.simulation-stop-button {
    background-color: #9b2c2c;
}

.simulation-stop-button:hover {
    background-color: #c53030;
}

.simulation-control-button:disabled {
    background-color: #4b5563;
    cursor: not-allowed;
}

/* 进度条 */
.simulation-progress-section {
    margin-bottom: 15px;
}

.simulation-progress-container {
    height: 20px;
    background-color: #293547;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
}

.simulation-progress-bar {
    height: 100%;
    background-color: #38bdf8;
    width: 0;
    transition: width 0.3s;
}

.simulation-status-text {
    text-align: center;
    font-size: 0.9rem;
    color: #d1d5db;
}

/* 性能指标 */
.simulation-performance-section {
    margin-top: 20px;
}

.simulation-metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.simulation-metric-label {
    color: #a1a1aa;
}

.simulation-metric-value {
    color: #38bdf8;
    font-weight: bold;
}

/* 结果面板样式 */
.simulation-tab-nav {
    display: flex;
    border-bottom: 1px solid #2c3747;
    margin-bottom: 15px;
    overflow-x: auto;
}

.simulation-tab-button {
    background: none;
    border: none;
    padding: 8px 15px;
    color: #a1a1aa;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
}

.simulation-tab-button.active {
    color: #38bdf8;
    border-bottom: 2px solid #38bdf8;
}

.simulation-tab-content {
    display: none;
    padding: 10px 0;
    width: 100%;
    box-sizing: border-box;
}

/* 详细数据样式 */
.simulation-details-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* 分布表格样式 */
.simulation-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed; /* 固定表格布局 */
}

.simulation-data-table th,
.simulation-data-table td {
    border: 1px solid #2c3747;
    padding: 8px;
    text-align: center;
}

.simulation-data-table th {
    background-color: #192231;
    color: #38bdf8;
    font-weight: bold;
}

/* 设置分布表格的列宽 */
.simulation-data-table th:nth-child(1),
.simulation-data-table td:nth-child(1) {
    width: 33%; /* 第一列：键/真数值/下注金额等 */
}

.simulation-data-table th:nth-child(2),
.simulation-data-table td:nth-child(2) {
    width: 33%; /* 第二列：值/出现次数/下注次数等 */
}

.simulation-data-table th:nth-child(3),
.simulation-data-table td:nth-child(3) {
    width: 34%; /* 第三列：百分比 */
}

.simulation-detail-section {
    background-color: #1a2234;
    border-radius: 6px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.simulation-detail-section h4 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #38bdf8;
    font-size: 1.1rem;
    border-bottom: 1px solid #2c3747;
    padding-bottom: 5px;
}

.simulation-detail-table {
    width: 100%;
    border-collapse: collapse;
}

.simulation-detail-table tr {
    border-bottom: 1px solid rgba(44, 55, 71, 0.3);
}

.simulation-detail-table tr:last-child {
    border-bottom: none;
}

.simulation-detail-label {
    padding: 8px 5px;
    color: #a1a1aa;
    font-size: 0.9rem;
    text-align: left;
}

.simulation-detail-value {
    padding: 8px 5px;
    color: #e6e6e6;
    font-weight: bold;
    text-align: right;
}

.simulation-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: #1a2234;
    border-radius: 6px;
    overflow: hidden;
}

.simulation-data-table thead {
    background-color: #293547;
}

.simulation-data-table th {
    padding: 10px;
    text-align: left;
    color: #38bdf8;
    font-weight: bold;
    border-bottom: 2px solid #2c3747;
}

.simulation-data-table td {
    padding: 8px 10px;
    border-bottom: 1px solid rgba(44, 55, 71, 0.3);
}

.simulation-data-table tr:last-child td {
    border-bottom: none;
}

.simulation-data-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.1);
}

/* 概览卡片 */
.simulation-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.simulation-overview-card {
    background-color: #293547;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.simulation-card-title {
    color: #a1a1aa;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

/* 卡片值容器 */
.simulation-card-value-container {
    display: flex;
    justify-content: center;
    align-items: flex-end; /* 底部对齐 */
    margin-bottom: 5px;
    width: 100%;
    height: 2.2rem; /* 固定高度 */
}

/* 卡片值 */
.simulation-card-value {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1.8rem;
}

.simulation-card-value.positive {
    color: #34d399;
}

.simulation-card-value.negative {
    color: #f87171;
}

/* 内联单位 */
.simulation-card-unit-inline {
    color: #a1a1aa;
    font-size: 1rem;
    margin-left: 2px;
    font-weight: normal;
    line-height: 1.4rem; /* 调整行高 */
    position: relative;
    bottom: 0.1rem; /* 微调位置 */
}

/* 保留旧的单位样式以兼容性 */
.simulation-card-unit {
    display: none;
}

/* 概览表格容器 */
.simulation-stats-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
}

/* 概览表格 */
.simulation-overview-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed; /* 固定表格布局 */
    background-color: #1e293b; /* 添加背景色 */
    border-radius: 8px; /* 添加圆角 */
    overflow: hidden; /* 确保圆角生效 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.simulation-overview-table tr:nth-child(odd) {
    background-color: rgba(0, 0, 0, 0.1);
}

.simulation-overview-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.05);
}

.simulation-overview-table tr:hover {
    background-color: rgba(56, 189, 248, 0.1); /* 鼠标悬停效果 */
}

.simulation-overview-table td {
    padding: 10px 15px; /* 增加内边距 */
    border-bottom: 1px solid #2c3747;
    height: 36px; /* 增加行高 */
    box-sizing: border-box;
    vertical-align: middle; /* 垂直居中 */
}

.simulation-overview-table tr:last-child td {
    border-bottom: none; /* 最后一行不显示底部边框 */
}

.simulation-table-label {
    color: #a1a1aa;
    width: 50%; /* 标签列宽度 */
    text-align: left;
    font-size: 1rem; /* 增大字体 */
}

.simulation-table-value {
    font-weight: bold;
    text-align: left; /* 改为左对齐 */
    width: 50%; /* 值列宽度 */
    color: #e6e6e6; /* 更亮的颜色 */
    font-size: 1rem; /* 增大字体 */
}

/* 添加正负值的颜色样式 */
.simulation-table-value.positive {
    color: #34d399; /* 绿色，表示正值 */
}

.simulation-table-value.negative {
    color: #f87171; /* 红色，表示负值 */
}

/* 图表容器 */
.simulation-chart-container {
    background-color: #293547;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    height: 350px;
    position: relative;
}

.simulation-chart-title {
    margin-top: 0;
    margin-bottom: 10px;
    color: #a1a1aa;
    font-size: 1rem;
}

/* 图表控制面板 */
.simulation-chart-control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e2536;
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.simulation-chart-type-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.simulation-chart-type-selector span {
    color: #a1a1aa;
    font-size: 0.95rem;
}

.simulation-chart-type-select {
    background-color: #293547;
    border: 1px solid #3e4c63;
    color: #e6e6e6;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 0.95rem;
}

.simulation-chart-type-select:focus {
    outline: none;
    border-color: #38bdf8;
}

.simulation-chart-options {
    display: flex;
    gap: 15px;
}

.simulation-chart-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.simulation-chart-option input[type="checkbox"] {
    cursor: pointer;
}

.simulation-chart-option label {
    color: #a1a1aa;
    font-size: 0.95rem;
    cursor: pointer;
}

/* 数据表格 */
.simulation-data-table {
    width: 100%;
    border-collapse: collapse;
}

.simulation-data-table th,
.simulation-data-table td {
    padding: 8px;
    border: 1px solid #2c3747;
    text-align: center;
}

.simulation-data-table th {
    background-color: #293547;
    color: #a1a1aa;
}

.simulation-data-table td.positive {
    color: #34d399;
}

.simulation-data-table td.negative {
    color: #f87171;
}

/* 导出按钮 */
.simulation-export-button {
    background-color: #2c5282;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
    margin-top: 10px;
}

.simulation-export-button:hover {
    background-color: #3182ce;
}

/* 错误消息 */
.simulation-error-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #9b2c2c;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 2000;
    max-width: 80%;
}

.simulation-error-message {
    text-align: center;
}

/* 提示消息 */
.simulation-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(25, 34, 49, 0.9);
    color: #e6e6e6;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    z-index: 2000;
    font-size: 0.95rem;
    transition: opacity 0.5s ease;
    border-left: 4px solid #38bdf8;
    max-width: 400px;
    text-align: center;
}

/* 禁用面板 */
.simulation-panel.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* 移动端适配 */
@media (max-width: 960px) {
    .simulation-content {
        flex-direction: column;
    }

    .simulation-config-panel,
    .simulation-control-panel {
        flex: 0 0 auto;
        width: 100%;
        min-width: 0;
    }
}

/* 游戏历史表格样式 */
#simulationBetHistoryTable {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 1rem; /* 增大字体 */
    table-layout: fixed;
    min-width: 1000px; /* 确保表格有足够的最小宽度 */
}

#simulationBetHistoryTable th {
    background-color: #293547;
    color: #a1a1aa;
    padding: 8px 10px; /* 增加内边距 */
    text-align: center;
    border: 1px solid #2c3747;
    position: sticky;
    top: 0;
    z-index: 1;
    white-space: nowrap;
    height: 42px; /* 增加表头高度 */
    box-sizing: border-box;
    font-weight: bold; /* 加粗表头 */
    font-size: 1rem; /* 增大字体 */
    overflow: visible; /* 确保内容不被裁剪 */
}

#simulationBetHistoryTable td {
    padding: 8px 10px; /* 增加内边距 */
    text-align: center;
    border: 1px solid #2c3747;
    white-space: normal; /* 允许文本换行 */
    overflow: visible; /* 修改为visible，确保内容不被裁剪 */
    height: auto; /* 自动调整行高 */
    min-height: 36px; /* 最小行高 */
    box-sizing: border-box;
}

#simulationBetHistoryTable tr:nth-child(odd) {
    background-color: rgba(0, 0, 0, 0.1);
}

#simulationBetHistoryTable tr:hover {
    background-color: rgba(56, 189, 248, 0.1);
}

#simulationBetHistoryTable .positive {
    color: #34d399;
}

#simulationBetHistoryTable .negative {
    color: #f87171;
}

/* 设置各列的宽度 */
#simulationBetHistoryTable th:nth-child(1), /* 局号 */
#simulationBetHistoryTable td:nth-child(1) {
    width: 4%;
    min-width: 50px;
}

#simulationBetHistoryTable th:nth-child(2), /* 玩家标识 */
#simulationBetHistoryTable td:nth-child(2) {
    width: 7%;
    min-width: 90px;
}

#simulationBetHistoryTable th:nth-child(3), /* 下注金额 */
#simulationBetHistoryTable td:nth-child(3) {
    width: 6%;
    min-width: 70px;
}

#simulationBetHistoryTable th:nth-child(4), /* 真数 */
#simulationBetHistoryTable td:nth-child(4) {
    width: 4%;
    min-width: 50px;
}

#simulationBetHistoryTable th:nth-child(5), /* 结果 */
#simulationBetHistoryTable td:nth-child(5) {
    width: 8%;
    min-width: 80px;
}

#simulationBetHistoryTable th:nth-child(6), /* 玩家手牌 */
#simulationBetHistoryTable td:nth-child(6) {
    width: 22%;
    min-width: 200px;
}

#simulationBetHistoryTable th:nth-child(7), /* 庄家手牌 */
#simulationBetHistoryTable td:nth-child(7) {
    width: 22%;
    min-width: 200px;
}

#simulationBetHistoryTable th:nth-child(8), /* 盈亏 */
#simulationBetHistoryTable td:nth-child(8) {
    width: 5%;
    min-width: 60px;
}

#simulationBetHistoryTable th:nth-child(9), /* 剩余筹码 */
#simulationBetHistoryTable td:nth-child(9) {
    width: 6%;
    min-width: 80px;
}

/* 玩家统计表格样式 */
#simulationPlayerStatsTableBody td:first-child {
    text-align: left;
    color: #a1a1aa;
}

#simulationPlayerStatsTableBody td:last-child {
    text-align: right;
    font-weight: bold;
}

/* 表格容器滚动样式 */
.simulation-tab-content {
    overflow-x: auto; /* 只保留水平滚动 */
    padding-right: 5px;
    width: 100%;
    box-sizing: border-box;
}

.simulation-tab-content::-webkit-scrollbar {
    width: 8px;
    height: 8px; /* 添加水平滚动条高度 */
}

.simulation-tab-content::-webkit-scrollbar-track {
    background: #293547;
    border-radius: 4px;
}

.simulation-tab-content::-webkit-scrollbar-thumb {
    background: #3e4c63;
    border-radius: 4px;
}

.simulation-tab-content::-webkit-scrollbar-thumb:hover {
    background: #38bdf8;
}

/* 手牌样式 */
.card-suit-spades, .card-suit-clubs {
    color: #e6e6e6;
}

.card-suit-hearts, .card-suit-diamonds {
    color: #f87171;
}

/* 玩家统计卡片样式 */
.simulation-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.simulation-stats-category {
    background-color: #293547;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.simulation-stats-category-title {
    margin-top: 0;
    margin-bottom: 15px;
    color: #38bdf8;
    font-size: 1rem;
    border-bottom: 1px solid #3e4c63;
    padding-bottom: 5px;
}

.simulation-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.simulation-stat-item {
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.simulation-stat-item:hover {
    background-color: rgba(56, 189, 248, 0.1);
}

.simulation-stat-label {
    color: #a1a1aa;
    font-size: 0.85rem;
    margin-bottom: 4px;
}

.simulation-stat-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.simulation-stat-value.positive {
    color: #34d399;
}

.simulation-stat-value.negative {
    color: #f87171;
}

/* 下注历史表格增强样式 */
#simulationbetHistoryTab {
    padding: 0 5px;
}

#simulationbetHistoryTab h4 {
    margin: 10px 0 15px 0;
    color: #38bdf8;
}

/* 为表格添加响应式布局 */
@media (max-width: 1200px) {
    .simulation-stats-cards {
        grid-template-columns: 1fr;
    }
}

/* 移动设备优化 */
@media (max-width: 768px) {
    .simulation-stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 历史记录控制栏样式 - 优化布局 */
.simulation-history-control-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #1e2536;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 分页信息显示 */
.simulation-pagination-info {
    color: #a1a1aa;
    font-size: 0.9rem;
    min-width: 180px;
}

/* 分页控件 */
.simulation-pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
}

/* 分页按钮 */
.simulation-pagination-button {
    background-color: #2563eb;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: center;
    transition: background-color 0.2s, transform 0.1s;
}

.simulation-pagination-button:hover:not(:disabled) {
    background-color: #1d4ed8;
    transform: translateY(-1px);
}

.simulation-pagination-button:disabled {
    background-color: #475569;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 页码输入容器 */
.simulation-page-number-container {
    display: flex;
    align-items: center;
    margin: 0 10px;
    white-space: nowrap;
}

/* 页码输入框 */
.simulation-page-number-input {
    width: 50px;
    text-align: center;
    background-color: #293547;
    border: 1px solid #3e4c63;
    color: #e6e6e6;
    border-radius: 4px;
    padding: 5px;
    margin-right: 5px;
}

/* 导出历史记录按钮 */
.simulation-export-history-button {
    background-color: #5a42ea;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.9rem;
    min-width: 105px;
    margin-left: 15px;
    transition: background-color 0.2s, transform 0.1s;
    text-align: center;
}

.simulation-export-history-button:hover:not(:disabled) {
    background-color: #4535b7;
    transform: translateY(-1px);
}

.simulation-export-result-button, .simulation-import-result-button {
    background-color: #2c5282;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
    display: inline-block;
    width: fit-content;
    font-size: 0.9rem;
    transition: background-color 0.2s, transform 0.1s;
}

.simulation-export-result-button:hover {
    background-color: #3182ce;
    transform: translateY(-1px);
}

.simulation-import-result-button:hover {
    background-color: #38a169;
    transform: translateY(-1px);
}

.simulation-export-result-button:disabled, .simulation-import-result-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #6b7280;
}

.simulation-result-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* 玩家总数据统计表格样式 */
.simulation-player-overall-stats {
    margin-bottom: 20px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #1e2536;
}

.simulation-overall-stats-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    font-size: 0.95rem;
}

.simulation-overall-stats-table th {
    background-color: #192231;
    color: #a1a1aa;
    padding: 6px 8px;
    text-align: center;
    font-weight: bold; /* 加粗表头 */
    border-bottom: 1px solid #2c3747;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 42px; /* 增加表头高度 */
    box-sizing: border-box;
    font-size: 1rem; /* 增大字体 */
}

.simulation-overall-stats-table td {
    padding: 6px 8px;
    text-align: center;
    font-weight: bold;
    border-right: 1px solid #2c3747;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 32px; /* 固定行高 */
    box-sizing: border-box;
}

.simulation-overall-stats-table td:last-child {
    border-right: none;
}

/* 设置各列的宽度 */
.simulation-overall-stats-table th:nth-child(1), /* 总局数 */
.simulation-overall-stats-table td:nth-child(1) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(2), /* 胜率 */
.simulation-overall-stats-table td:nth-child(2) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(3), /* 赢局数 */
.simulation-overall-stats-table td:nth-child(3) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(4), /* 输局数 */
.simulation-overall-stats-table td:nth-child(4) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(5), /* 平局数 */
.simulation-overall-stats-table td:nth-child(5) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(6), /* 黑杰克 */
.simulation-overall-stats-table td:nth-child(6) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(7), /* 总盈亏 */
.simulation-overall-stats-table td:nth-child(7) {
    width: 12.5%;
}

.simulation-overall-stats-table th:nth-child(8), /* 平均下注 */
.simulation-overall-stats-table td:nth-child(8) {
    width: 12.5%;
}

.simulation-history-stat-cell.positive {
    color: #34d399;
}

.simulation-history-stat-cell.negative {
    color: #f87171;
}

/* 筛选功能样式 */
.simulation-filter-container {
    background-color: #1e2536;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
}

.simulation-filter-title {
    font-size: 1rem;
    color: #38bdf8;
    margin-bottom: 12px;
    border-bottom: 1px solid #38bdf8;
    padding-bottom: 5px;
}

.simulation-filter-form {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-end; /* 改为底部对齐 */
    gap: 12px;
    width: 100%;
    box-sizing: border-box;
}

.simulation-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
    min-width: 0;
}

.simulation-filter-group label {
    color: #a1a1aa;
    font-size: 0.9rem;
}

.simulation-filter-select,
.simulation-filter-number {
    background-color: #293547;
    border: 1px solid #3e4c63;
    color: #e6e6e6;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 0.9rem;
    height: 32px; /* 统一高度 */
    box-sizing: border-box;
}

.simulation-filter-select:focus,
.simulation-filter-number:focus {
    outline: none;
    border-color: #38bdf8;
}

.simulation-filter-range {
    display: flex;
    align-items: center;
    gap: 5px;
}

.simulation-filter-range-text {
    color: #a1a1aa;
    font-size: 0.9rem;
}

.simulation-filter-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    flex: 0 0 auto;
}

.simulation-filter-button {
    background-color: #2563eb;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.simulation-filter-button:hover {
    background-color: #1d4ed8;
}

.simulation-reset-filter-button {
    background-color: #4b5563;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
    height: 32px; /* 与筛选框高度一致 */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.simulation-reset-filter-button:hover {
    background-color: #374151;
}

/* 玩家总数据统计 */
.simulation-player-total-stats {
    margin-bottom: 20px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #1e2536;
}

.simulation-total-stats-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    font-size: 0.95rem;
    margin-bottom: 20px;
}

.simulation-total-stats-table th {
    background-color: #192231;
    color: #a1a1aa;
    padding: 6px 8px;
    text-align: center;
    font-weight: bold; /* 加粗表头 */
    border-bottom: 1px solid #2c3747;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 42px; /* 增加表头高度 */
    box-sizing: border-box;
    font-size: 1rem; /* 增大字体 */
}

.simulation-total-stats-table td {
    padding: 6px 8px;
    text-align: center;
    font-weight: bold;
    border-right: 1px solid #2c3747;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 32px; /* 固定行高 */
    box-sizing: border-box;
}

.simulation-total-stats-table td:last-child {
    border-right: none;
}

/* 设置总数据统计表格各列的宽度 */
.simulation-total-stats-table th:nth-child(1), /* 总局数 */
.simulation-total-stats-table td:nth-child(1) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(2), /* 胜率 */
.simulation-total-stats-table td:nth-child(2) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(3), /* 赢局数 */
.simulation-total-stats-table td:nth-child(3) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(4), /* 输局数 */
.simulation-total-stats-table td:nth-child(4) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(5), /* 平局数 */
.simulation-total-stats-table td:nth-child(5) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(6), /* 黑杰克 */
.simulation-total-stats-table td:nth-child(6) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(7), /* 总盈亏 */
.simulation-total-stats-table td:nth-child(7) {
    width: 12.5%;
}

.simulation-total-stats-table th:nth-child(8), /* 平均下注 */
.simulation-total-stats-table td:nth-child(8) {
    width: 12.5%;
}

/* 下注历史表格 */
.simulation-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed; /* 固定表格布局 */
    font-size: 1rem; /* 增大字体 */
}

.simulation-history-table th,
.simulation-history-table td {
    padding: 8px 10px; /* 增加内边距 */
    border: 1px solid #2c3747;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 36px; /* 增加行高 */
    box-sizing: border-box;
}

/* 设置筛选后表格各列的宽度，与默认表格保持一致 */
.simulation-history-table th:nth-child(1), /* 局号 */
.simulation-history-table td:nth-child(1) {
    width: 7%;
}

.simulation-history-table th:nth-child(2), /* 下注金额 */
.simulation-history-table td:nth-child(2) {
    width: 10%;
    min-width: 100px;
}

.simulation-history-table th:nth-child(3), /* 真数 */
.simulation-history-table td:nth-child(3) {
    width: 7%;
}

.simulation-history-table th:nth-child(4), /* 结果 */
.simulation-history-table td:nth-child(4) {
    width: 12%;
}

.simulation-history-table th:nth-child(5), /* 玩家手牌 */
.simulation-history-table td:nth-child(5) {
    width: 23%;
}

.simulation-history-table th:nth-child(6), /* 庄家手牌 */
.simulation-history-table td:nth-child(6) {
    width: 23%;
}

.simulation-history-table th:nth-child(7), /* 盈亏 */
.simulation-history-table td:nth-child(7) {
    width: 8%;
}

.simulation-history-table th:nth-child(8), /* 剩余筹码 */
.simulation-history-table td:nth-child(8) {
    width: 10%;
    min-width: 100px;
}

.simulation-history-table th {
    background-color: #293547;
    color: #a1a1aa;
    position: sticky;
    top: 0;
    z-index: 10;
    height: 42px; /* 增加表头高度 */
    font-weight: bold; /* 加粗表头 */
    font-size: 1rem; /* 增大字体 */
}

.simulation-history-table tr:nth-child(odd):not(.split-hand-row) {
    background-color: rgba(0, 0, 0, 0.1);
}

.simulation-history-table tr:hover {
    background-color: rgba(56, 189, 248, 0.1);
}

.simulation-history-table td.win {
    color: #34d399;
}

.simulation-history-table td.lose {
    color: #f87171;
}

/* 分牌记录样式 */
.split-hand-row {
    background-color: rgba(59, 130, 246, 0.05) !important;
    border-left: 3px solid #3b82f6;
}

.first-split-hand {
    border-top: 2px solid #3b82f6;
}

.last-split-hand {
    border-bottom: 2px solid #3b82f6;
}

/* 手牌标识样式 */
.hand-indicator {
    display: inline-block;
    font-size: 0.8em;
    color: #60a5fa;
    margin-left: 5px;
    font-weight: bold;
}

/* 图表内容 */
.simulation-chart-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #a1a1aa;
    font-style: italic;
}

.simulation-chart-info {
    margin-top: 10px;
    color: #a1a1aa;
    font-size: 0.9rem;
    text-align: center;
}

/* 无数据提示 */
.simulation-no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: #a1a1aa;
    font-style: italic;
    background-color: #293547;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* 分页控制 */
.simulation-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
}

.simulation-pagination-button {
    background-color: #2c3747;
    color: #e6e6e6;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.simulation-pagination-button:hover {
    background-color: #3e4c63;
}

.simulation-pagination-button:disabled {
    background-color: #1e2536;
    color: #4b5563;
    cursor: not-allowed;
}

.simulation-pagination-info {
    color: #a1a1aa;
    font-size: 0.9rem;
}

/* 新的玩家统计样式 - 优化布局 */
.player-stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.player-stats-section {
    background-color: #1e2536;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.player-stats-title {
    margin-top: 0;
    margin-bottom: 10px;
    color: #38bdf8;
    font-size: 1rem;
    border-bottom: 1px solid #38bdf8;
    padding-bottom: 4px;
}

.player-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

/* 让总体统计和记录数据区域占据两列 */
.player-stats-section:nth-child(3),
.player-stats-section:nth-child(4) {
    grid-column: span 2;
}

.player-stats-section:nth-child(3) .player-stats-grid,
.player-stats-section:nth-child(4) .player-stats-grid {
    grid-template-columns: repeat(4, 1fr);
}

.player-stats-card {
    background-color: #293547;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 80px;
}

.player-stats-card:hover {
    background-color: #354761;
}

.player-stats-card-title {
    color: #a1a1aa;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.player-stats-card-value {
    font-size: 1.4rem;
    font-weight: bold;
}

.player-stats-card-value.positive {
    color: #34d399;
}

.player-stats-card-value.negative {
    color: #f87171;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .player-stats-container {
        grid-template-columns: 1fr;
    }

    .player-stats-section:nth-child(3),
    .player-stats-section:nth-child(4) {
        grid-column: span 1;
    }

    .player-stats-section:nth-child(3) .player-stats-grid,
    .player-stats-section:nth-child(4) .player-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .player-stats-grid {
        grid-template-columns: 1fr;
    }

    .player-stats-section:nth-child(4) .player-stats-grid,
    .player-stats-section:nth-child(3) .player-stats-grid {
        grid-template-columns: 1fr;
    }

    .player-stats-card {
        height: 70px;
    }

    .player-stats-card-value {
        font-size: 1.2rem;
    }
}