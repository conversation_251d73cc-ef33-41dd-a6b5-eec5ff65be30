/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 15px; /* 减小全局内边距 */
    min-height: 100vh;
    font-family: 'Poppins', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0c1525 0%, #1a2436 100%); /* 深化背景色 */
    position: relative;
    overflow-x: hidden;
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-size: 16px; /* 恢复基础字体大小 */
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 15%, rgba(56, 189, 248, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(99, 102, 241, 0.05) 0%, transparent 40%),
        linear-gradient(30deg, rgba(139, 92, 246, 0.02) 0%, transparent 40%),
        repeating-linear-gradient(45deg, rgba(255,255,255,0.005) 0px, rgba(255,255,255,0.005) 1px, transparent 1px, transparent 12px);
    z-index: 0;
    pointer-events: none;
}

/* 游戏容器 */
.game-container {
    position: relative;
    z-index: 1;
    max-width: 1800px; /* 从1600px增加到1800px，提供更多空间 */
    width: 99%; /* 略微增加占用宽度 */
    margin: 5px auto; /* 减少上下外边距 */
    padding: 10px; /* 减少内边距 */
    background: rgba(13, 20, 35, 0.85);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.3),
        0 8px 20px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    border: 1px solid rgba(255, 255, 255, 0.08);
    overflow: hidden;
}

.game-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 30% 30%, rgba(56, 189, 248, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    z-index: -1;
    animation: subtlePulse 15s ease-in-out infinite alternate;
}

/* 新增：游戏区域分隔线 */
.game-area::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(56, 189, 248, 0.1) 20%,
        rgba(56, 189, 248, 0.2) 50%,
        rgba(56, 189, 248, 0.1) 80%,
        transparent 100%);
}

.game-area::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(139, 92, 246, 0.1) 20%,
        rgba(139, 92, 246, 0.2) 50%,
        rgba(139, 92, 246, 0.1) 80%,
        transparent 100%);
}

/* 统计区域样式 */
.stats-container {
    background: rgba(30, 41, 59, 0.9);
    border-radius: 12px; /* 减小圆角 */
    padding: 8px 12px; /* 进一步减小内边距 */
    margin-bottom: 10px; /* 减少与游戏控制区域的间距 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    backdrop-filter: blur(10px);
}

.deck-info {
    display: flex;
    gap: 15px; /* 减小间距 */
    flex-wrap: nowrap;
    align-items: center;
}

.deck-info span {
    background: rgba(15, 23, 42, 0.9); /* 增强背景对比度 */
    padding: 8px 15px; /* 减小内边距 */
    border-radius: 10px;
    font-size: 0.95em; /* 稍微减小字体 */
    color: #e2e8f0;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
    white-space: nowrap;
    border: 1px solid rgba(255, 255, 255, 0.08);
    min-width: max-content;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.deck-info span:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.deck-info span span {
    background: none;
    padding: 0;
    border: none;
    color: #38bdf8;
    font-weight: bold;
    margin-left: 5px;
    text-shadow: 0 0 8px rgba(56, 189, 248, 0.4);
    box-shadow: none;
}

.deck-info span span:hover {
    transform: none;
}

/* 总盈亏样式 */
.total-score {
    background: linear-gradient(45deg, rgba(56, 189, 248, 0.2), rgba(129, 140, 248, 0.2));
    padding: 8px 15px; /* 减小内边距 */
    border-radius: 10px;
    font-size: 0.95em; /* 稍微减小字体 */
    font-weight: bold;
    color: #e2e8f0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow:
        0 0 20px rgba(99, 102, 241, 0.1),
        0 4px 10px rgba(0, 0, 0, 0.15);
    min-width: max-content;
    transition: all 0.3s ease;
}

.total-score:hover {
    transform: translateY(-2px);
    box-shadow:
        0 0 25px rgba(99, 102, 241, 0.15),
        0 6px 16px rgba(0, 0, 0, 0.2);
}

.total-score span {
    font-size: 1.1em;
    margin-left: 8px;
}

.total-score span span {
    color: #38bdf8;
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
}

/* 添加正负值的颜色样式 */
.total-score span span.positive {
    color: #10b981; /* 绿色 */
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.total-score span span.negative {
    color: #ef4444; /* 红色 */
    text-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* 游戏区域框架 */
.game-area {
    display: flex;
    flex-direction: column;
    gap: 8px; /* 减少间距 */
    padding: 10px; /* 减少内边距 */
    background: rgba(18, 25, 40, 0.8);
    border-radius: 18px;
    margin-top: 10px; /* 减少与游戏控制区域的间距 */
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    position: relative;
}

/* 庄家区域 */
.dealer-section {
    position: relative;
    padding: 5px 12px 2px; /* 进一步减少内边距 */
    background: linear-gradient(to bottom, rgba(8, 12, 25, 0.95), rgba(15, 25, 40, 0.95));
    border-radius: 18px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(56, 189, 248, 0.1) inset;
    border: 1px solid rgba(56, 189, 248, 0.15);
    transition: all 0.3s ease;
    overflow: visible;
    margin-top: 5px; /* 进一步减少顶部外边距 */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.dealer-section:hover {
    box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(56, 189, 248, 0.2) inset;
    transform: translateY(-3px);
}

.dealer-section::before {
    content: "庄家";
    position: absolute;
    top: -12px; /* 进一步减小top值，让标签更贴近容器 */
    left: 20px;
    background: linear-gradient(135deg, #0ea5e9, #3b82f6);
    padding: 2px 12px; /*.减少内边距 */
    border-radius: 10px; /* 减小圆角 */
    font-size: 12px; /* 减小字体 */
    color: white;
    font-weight: 600;
    box-shadow:
        0 4px 10px rgba(14, 165, 233, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    letter-spacing: 1px;
    z-index: 100;
}

.dealer-hand {
    padding: 2px; /* 进一步减少内边距 */
    margin: 1px 0; /* 进一步减少外边距 */
    border-radius: 15px;
    background: rgba(8, 12, 24, 0.8);
    border: 1px solid rgba(139, 92, 246, 0.15);
    position: relative;
    min-height: 160px; /* 进一步减小容器高度 */
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
}

.dealer-hand .cards {
    display: flex;
    flex-direction: column; /* 保持纵向排列 */
    justify-content: center;
    align-items: flex-start; /* 左对齐，便于向右偏移卡牌 */
    min-height: 140px; /* 进一步减小高度 */
    padding: 3px 8px; /* 进一步减少内边距 */
    width: 200px; /* 保持宽度不变 */
}

.dealer-hand:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 30px rgba(56, 189, 248, 0.1),
        0 0 0 1px rgba(56, 189, 248, 0.1) inset;
    border: 1px solid rgba(56, 189, 248, 0.2);
}

.dealer-hand::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(56, 189, 248, 0.08) 0%, transparent 60%);
    z-index: 0;
    pointer-events: none;
}

.dealer-hand h2 {
    color: #f8fafc;
    font-size: 1em; /* 减小字体大小 */
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 10px rgba(56, 189, 248, 0.3);
    margin-bottom: 2px; /* 进一步减少底部间距 */
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
    padding-bottom: 1px; /* 进一步减少底部内边距 */
    align-self: flex-start; /* 确保标题靠左对齐 */
    position: absolute; /* 绝对定位，不影响卡牌居中 */
    top: 3px; /* 减少顶部距离 */
    left: 8px; /* 减少左侧距离 */
}

.dealer-hand h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #38bdf8, transparent);
}

/* 专门针对庄家点数显示 */
.dealer-hand .points-display {
    background-color: rgba(8, 12, 25, 0.95); /* 原始背景色 */
    color: #38bdf8; /* 原始文字颜色 */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.15),
        0 0 10px rgba(56, 189, 248, 0.15); /* 原始阴影 */
    border: 1px solid rgba(56, 189, 248, 0.3); /* 原始边框 */
    text-shadow: 0 0 6px rgba(56, 189, 248, 0.6); /* 原始文字阴影 */
    text-align: center;

    /* 新的位置调整 */
    position: relative; /* 修改为相对定位 */
    top: auto; /* 覆盖默认的顶部位置 */
    right: auto; /* 覆盖默认的右侧位置 */
    margin-top: 15px; /* 增加与卡牌区域的间距 */
    font-size: 1.1rem; /* 略微增大字体 */
    padding: 5px 10px; /* 调整内边距 */
}

/* 庄家点数显示位置 */
.dealer-hand .points-display {
    top: auto; /* 覆盖默认的顶部位置 */
    right: auto; /* 覆盖默认的右侧位置 */
    bottom: 10px; /* 放在底部 */
    position: relative; /* 修改为相对定位 */
    margin-top: 10px; /* 与卡牌区域保持一定间距 */
}

/* 玩家区域容器 */
#players-container {
    display: flex;
    flex-wrap: nowrap; /* 修改为nowrap，避免玩家折行显示 */
    justify-content: center;
    gap: 8px; /* 进一步减小间距 */
    padding: 25px 8px 15px; /* 减小内边距 */
    background: linear-gradient(to bottom, rgba(8, 12, 25, 0.9), rgba(15, 25, 40, 0.9)); /* 更深的背景色 */
    border-radius: 18px;
    border: 1px solid rgba(139, 92, 246, 0.15);
    position: relative;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(139, 92, 246, 0.1) inset;
    overflow: visible; /* 改回visible，移除水平滚动 */
    margin-top: 10px; /* 减少顶部外边距 */
    max-width: 100%; /* 确保不超出父容器 */
}

#players-container::before {
    content: "玩家区域";
    position: absolute;
    top: -15px; /* 减小top值，让标签贴近容器 */
    left: 20px;
    background: linear-gradient(135deg, #8b5cf6, #6366f1);
    padding: 3px 15px;
    border-radius: 12px;
    font-size: 13px;
    color: white;
    font-weight: 600;
    box-shadow:
        0 4px 10px rgba(139, 92, 246, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    letter-spacing: 1px;
    z-index: 100; /* 增加z-index进一步确保在最上层 */
}

#players-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.08) 0%, transparent 60%);
    z-index: 0;
    pointer-events: none;
}

/* 玩家区域 */
.player-section {
    flex: 1;
    min-width: 270px; /* 从260px增加到270px */
    max-width: 310px; /* 从300px增加到310px */
    margin: 3px; /* 减少外边距 */
    background: linear-gradient(to bottom, rgba(12, 20, 45, 0.95), rgba(25, 35, 65, 0.95));
    border: 1px solid rgba(139, 92, 246, 0.2);
    padding: 8px; /* 减少内边距 */
    border-radius: 15px;
    position: relative;
    z-index: 1;
    transition: all 0.15s ease-out; /* 减少过渡时间，使用ease-out效果更流畅 */
}

.player-section h2 {
    color: #ffffff;
    font-size: 0.9em; /* 进一步减小字体大小 */
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0 0 8px 0; /* 减少底部外边距 */
    padding: 4px 8px; /* 减少内边距 */
    letter-spacing: 1px;
    position: relative;
    display: block;
    text-align: center;
    z-index: 10;
    background-color: rgba(11, 18, 35, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.player-section h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 10%;
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.6), rgba(255,255,255,0));
}

.player-section h3 {
    font-size: 1.1rem;
    margin: 0 0 5px;
    padding: 5px 0;
    color: #e2e8f0;
    text-align: center;
    position: relative;
    z-index: 2;
}

.player-section h4 {
    font-size: 0.95rem;
    margin: 5px 0;
    padding: 3px 0;
    color: #cbd5e1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.player-section:hover {
    transform: translateY(-5px);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(139, 92, 246, 0.1) inset;
}

/* 活跃玩家指示器 */
.player-section.active {
    transform: translateY(-5px); /* 减小位移距离，使动画更快 */
    box-shadow:
        0 15px 30px rgba(139, 92, 246, 0.2),
        0 0 0 1px rgba(139, 92, 246, 0.1) inset,
        0 0 20px rgba(139, 92, 246, 0.2);
    border: 1px solid rgba(139, 92, 246, 0.25);
    background: linear-gradient(145deg, rgba(15, 23, 42, 0.85), rgba(30, 41, 59, 0.85)),
                radial-gradient(circle at center, rgba(139, 92, 246, 0.2) 0%, transparent 80%);
    will-change: transform, box-shadow; /* 提示浏览器优化这些属性的变化 */
}

/* 当前操作指示器 */
.current-player-indicator {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(220, 38, 38, 0.9); /* 改为红色背景 */
    color: white;
    font-size: 0.9rem; /* 增大字体 */
    font-weight: bold;
    padding: 5px 12px; /* 增加内边距 */
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.6); /* 增加红色阴影 */
    z-index: 20;
    white-space: nowrap;
    animation: pulseRed 1.5s infinite; /* 添加脉冲动画 */
    border: 1px solid rgba(255, 255, 255, 0.3); /* 添加边框 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7); /* 增加文字阴影 */
}

/* 添加红色脉冲动画 */
@keyframes pulseRed {
    0% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.05); box-shadow: 0 3px 15px rgba(239, 68, 68, 0.8); }
    100% { transform: translateX(-50%) scale(1); }
}

/* 修改活跃玩家伪元素，防止遮挡玩家名称 */
.player-section.active::before {
    content: "▶";
    position: absolute;
    left: -27px;
    top: 50%;
    transform: translateY(-50%);
    color: #8b5cf6;
    font-size: 22px;
    text-shadow: 0 0 15px rgba(139, 92, 246, 0.7);
    animation: pulse 1.5s infinite;
    z-index: 1; /* 降低z-index，防止遮挡 */
}

.player-section.active::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px; /* 匹配玩家区域的border-radius */
    border: 2px solid rgba(139, 92, 246, 0.4);
    animation: glow 2s infinite;
    pointer-events: none;
    z-index: 1; /* 降低z-index，防止遮挡 */
}

/* 玩家手牌区域 */
.player-hand {
    padding: 2px; /* 进一步减少内边距 */
    margin: 1px 0; /* 进一步减少外边距 */
    border-radius: 15px;
    background: rgba(8, 12, 24, 0.8);
    border: 1px solid rgba(139, 92, 246, 0.15);
    position: relative;
    min-height: 160px; /* 进一步减小容器高度 */
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
}

.player-hand .cards {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row; /* 改为横向排列 */
    justify-content: flex-start; /* 改为左对齐 */
    align-items: center;
    min-height: 160px; /* 减小容器高度，与庄家和玩家区域一致 */
    position: relative;
    margin: 0;
    padding: 0;
    transition: all 0.3s ease;
    width: 100%;
}

.player-hand:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 30px rgba(139, 92, 246, 0.1),
        0 0 0 1px rgba(139, 92, 246, 0.1) inset;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.player-hand::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.08) 0%, transparent 60%);
    z-index: 0;
    pointer-events: none;
}

.player-hand h3 {
    color: #f8fafc;
    font-size: 1em; /* 减小字体 */
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 10px rgba(139, 92, 246, 0.3);
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.25), rgba(139, 92, 246, 0.05));
    padding: 8px 15px; /* 减小内边距 */
    border-radius: 10px;
    margin: -15px 0 15px -10px; /* 调整边距 */
    display: inline-block;
    position: relative;
    border: 1px solid rgba(139, 92, 246, 0.2);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
}

.player-hand h3::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, transparent);
}

/* 点数显示样式 */
.points-display {
    display: inline-block;
    padding: 4px 8px;
    background: rgba(139, 92, 246, 0.9);
    border-radius: 10px;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    position: absolute;
    right: 10px;
    top: 10px; /* 放在顶部右侧 */
    z-index: 10;
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    min-width: 36px;
    text-align: center;
}

/* 特殊样式：点数分数表示法 (如 6/16) */
.points-display.fraction {
    min-width: 60px; /* 确保分数有足够空间 */
    letter-spacing: 1px; /* 增加字母间距 */
}

/* 点数分数形式的特定样式 */
.points-display.fraction .small-value {
    font-size: 0.9em;
    color: #4ade80;
}

.points-display.fraction .big-value {
    font-size: 1.05em;
    color: #38bdf8;
    font-weight: 700;
}

/* 特殊宽度点数样式 */
.points-display.wide-points {
    min-width: 60px; /* 双位数或特殊值点数 */
    padding-left: 15px;
    padding-right: 15px;
}

.points-display.single-points {
    min-width: 35px; /* 单位数点数 */
}

/* 当前操作指示器增强 */
.current-player-indicator {
    animation-duration: 1.5s; /* 加快动画速度 */
    will-change: transform, opacity; /* 提示浏览器优化这些属性的变化 */
}

/* 背景流光效果，放在单独的伪元素中 */
.hand-container.active .cards::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 79, 216, 0.15) 0%,
        rgba(255, 79, 216, 0) 50%,
        rgba(255, 79, 216, 0.15) 100%);
    z-index: 0;
    animation: activeHandGlow 3s infinite;
    pointer-events: none;
}

/* 手牌容器样式 */
.hand-container {
    padding: 10px; /* 进一步减小内边距 */
    margin: 5px 0; /* 进一步减小外边距 */
    background-color: rgba(14, 20, 35, 0.8); /* 更深的背景色增强对比度 */
    border-radius: 14px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.15s ease-out; /* 减少过渡时间，使用ease-out效果更流畅 */
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.hand-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
}

.hand-container.active {
    background: linear-gradient(to right, rgba(255, 79, 216, 0.25), rgba(30, 41, 59, 0.5), rgba(30, 41, 59, 0.5));
    border: 2px solid rgba(255, 79, 216, 0.5);
    box-shadow:
        0 8px 20px rgba(255, 79, 216, 0.25),
        0 0 20px rgba(255, 79, 216, 0.2);
    transform: translateY(-3px) scale(1.01); /* 减小变换效果，使动画更快 */
    position: relative;
    will-change: transform, box-shadow; /* 提示浏览器优化这些属性的变化 */
}

.hand-container.active::before {
    content: ''; /* 移除文字内容 */
    position: absolute;
    top: -6px;
    left: 50%;
    width: 60%;
    height: 4px;
    transform: translateX(-50%);
    background: linear-gradient(to right, #ff4fd8, #be00ff);
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(255, 79, 216, 0.7); /* 保留发光效果 */
    z-index: 5;
    animation: labelPulse 2s infinite;
}

@keyframes labelPulse {
    0%, 100% { opacity: 0.8; transform: translateX(-50%) scale(1); }
    50% { opacity: 1; transform: translateX(-50%) scale(1.05); }
}

/* 添加左侧醒目的活动指示器 */
.hand-container.active::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 70%;
    background: linear-gradient(to bottom, #ff4fd8, #be00ff);
    border-radius: 4px;
    box-shadow: 0 0 15px rgba(255, 79, 216, 0.7);
    animation: pulseIndicator 1.5s ease-in-out infinite;
}

@keyframes pulseIndicator {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes activeHandGlow {
    0% { opacity: 0.3; background-position: -100% 0; }
    100% { opacity: 0.7; background-position: 200% 0; }
}

/* 可分牌手牌容器样式 */
.hand-container.can-split {
    border: 1px solid rgba(139, 92, 246, 0.4);
    box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.15),
        0 0 30px rgba(139, 92, 246, 0.1);
    animation: containerGlow 1.5s infinite;
}

/* 可分牌提示标识 - 放在分牌按钮上方 */
.split-indicator {
    position: absolute;
    top: -12px; /* 进一步减少顶部距离 */
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #8b5cf6, #6366f1);
    color: white;
    font-size: 0.75rem; /* 进一步减小字体 */
    font-weight: bold;
    padding: 1px 5px; /* 进一步减少内边距 */
    border-radius: 5px; /* 进一步减小圆角 */
    box-shadow: 0 3px 10px rgba(139, 92, 246, 0.5);
    z-index: 20;
    animation: splitPulse 1.5s infinite;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

/* 添加箭头指向分牌按钮 */
.split-indicator::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #8b5cf6;
}

@keyframes splitPulse {
    0% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.05); box-shadow: 0 4px 15px rgba(139, 92, 246, 0.7); }
    100% { transform: translateX(-50%) scale(1); }
}

@keyframes containerGlow {
    0%, 100% { box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15), 0 0 30px rgba(139, 92, 246, 0.1); }
    50% { box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3), 0 0 30px rgba(139, 92, 246, 0.2); }
}

/* 卡牌容器通用样式 */
.cards {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column; /* 恢复为纵向排列 */
    justify-content: center;
    align-items: flex-start; /* 左对齐，便于向右偏移卡牌 */
    min-height: 160px; /* 减小容器高度，与庄家和玩家区域一致 */
    position: relative;
    margin: 0;
    padding: 0;
    transition: all 0.3s ease;
    width: 200px; /* 恢复原始宽度 */
}

/* 确保第一张卡牌没有负margin */
.cards .card:first-child {
    margin-top: 0;
    margin-left: 0;
}

/* 卡牌样式 - 简化 */
.card {
    width: 100px;  /* 保持现有宽度 */
    height: 140px; /* 保持现有高度 */
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    margin-top: -125px; /* 恢复纵向重叠效果 */
    margin-left: 0;
    z-index: 1;
    background: transparent;
    border: none;
}

/* 第一张卡牌的margin调整 */
.cards .card:first-child {
    margin-top: 0;
}

/* 卡牌图片样式 - 简化 */
.card-img {
    width: 100%;
    height: 100%;
    object-fit: fill; /* 确保图片完全填充容器而不变形 */
    display: block;
}

/* 卡牌悬停效果 */
.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

/* 统一所有卡牌容器的堆叠效果 */
/* 卡牌横向叠加距离调整 */
.cards .card:nth-child(2) { margin-left: 15px; z-index: 2; }
.cards .card:nth-child(3) { margin-left: 30px; z-index: 3; }
.cards .card:nth-child(4) { margin-left: 45px; z-index: 4; }
.cards .card:nth-child(5) { margin-left: 60px; z-index: 5; }
.cards .card:nth-child(6) { margin-left: 75px; z-index: 6; }

/* 显式指定所有玩家的卡牌偏移 */
.player-section .hand-container .cards .card:nth-child(2) { margin-left: 15px; z-index: 2; }
.player-section .hand-container .cards .card:nth-child(3) { margin-left: 30px; z-index: 3; }
.player-section .hand-container .cards .card:nth-child(4) { margin-left: 45px; z-index: 4; }
.player-section .hand-container .cards .card:nth-child(5) { margin-left: 60px; z-index: 5; }
.player-section .hand-container .cards .card:nth-child(6) { margin-left: 75px; z-index: 6; }

/* 庄家手牌的卡牌样式 */
.dealer-hand .cards .card:nth-child(2) { margin-left: 15px; z-index: 2; }
.dealer-hand .cards .card:nth-child(3) { margin-left: 30px; z-index: 3; }
.dealer-hand .cards .card:nth-child(4) { margin-left: 45px; z-index: 4; }
.dealer-hand .cards .card:nth-child(5) { margin-left: 60px; z-index: 5; }
.dealer-hand .cards .card:nth-child(6) { margin-left: 75px; z-index: 6; }

/* 响应式调整 */
@media (max-width: 1400px) {
    .cards .card:nth-child(2) { margin-left: 12px; }
    .cards .card:nth-child(3) { margin-left: 24px; }
    .cards .card:nth-child(4) { margin-left: 36px; }
    .cards .card:nth-child(5) { margin-left: 48px; }
    .cards .card:nth-child(6) { margin-left: 60px; }

    .dealer-hand .cards .card:nth-child(2) { margin-left: 12px; }
    .dealer-hand .cards .card:nth-child(3) { margin-left: 24px; }
    .dealer-hand .cards .card:nth-child(4) { margin-left: 36px; }
    .dealer-hand .cards .card:nth-child(5) { margin-left: 48px; }
    .dealer-hand .cards .card:nth-child(6) { margin-left: 60px; }
}

@media (max-width: 768px) {
    .cards .card:nth-child(2) { margin-left: 8px; }
    .cards .card:nth-child(3) { margin-left: 16px; }
    .cards .card:nth-child(4) { margin-left: 24px; }
    .cards .card:nth-child(5) { margin-left: 32px; }
    .cards .card:nth-child(6) { margin-left: 40px; }

    .dealer-hand .cards .card:nth-child(2) { margin-left: 8px; }
    .dealer-hand .cards .card:nth-child(3) { margin-left: 16px; }
    .dealer-hand .cards .card:nth-child(4) { margin-left: 24px; }
    .dealer-hand .cards .card:nth-child(5) { margin-left: 32px; }
    .dealer-hand .cards .card:nth-child(6) { margin-left: 40px; }
}

/* 添加中央花纹 */
.card.hidden::before {
    display: none;
}

/* 边框设计 */
.card.hidden::after {
    display: none;
}

/* 移除之前的动画 */
@keyframes cardBackAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 调整卡牌背面pattern元素样式 */
.card-pattern {
    width: 100%;
    height: 100%;
    position: relative;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath fill='white' d='M0,0 L50,0 L50,50 L0,50 z M50,50 L100,50 L100,100 L50,100 z'/%3E%3C/svg%3E");
    background-size: 12px 12px;
    background-repeat: repeat;
}

.card-pattern::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 70%;
    height: 70%;
    transform: translate(-50%, -50%);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='160' height='160' viewBox='0 0 160 160' fill='none'%3E%3Cpath d='M80 10C41.34 10 10 41.34 10 80C10 118.66 41.34 150 80 150C118.66 150 150 118.66 150 80C150 41.34 118.66 10 80 10ZM80 30C107.6 30 130 52.4 130 80C130 107.6 107.6 130 80 130C52.4 130 30 107.6 30 80C30 52.4 52.4 30 80 30Z' fill='white'/%3E%3Cpath d='M85 40C60 40 55 60 55 80C55 100 65 120 85 120C105 120 110 100 110 80C110 60 100 40 85 40Z' fill='%23102372'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.85;
}

.card-pattern::after {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: 2px solid #102372;
    box-shadow: 0 0 0 1px white;
    border-radius: 4px;
    pointer-events: none;
}

/* 修改隐藏卡牌样式，使其适配扑克牌背面 */
.card.hidden {
    background-color: transparent; /* 移除背景色 */
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.15); /* 保持与普通卡牌相同的阴影 */
    border: none; /* 移除边框 */
    position: relative;
    overflow: hidden;
}

/* 移除不需要的伪元素，因为我们使用pattern元素替代 */
.card.hidden::before,
.card.hidden::after {
    display: none;
}

.card-inner {
    width: 100%;
    height: 100%;
    padding: 2px; /* 减小内边距，为内容留出更多空间 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    color: #000;
    box-sizing: border-box;
}

.card-inner.red {
    color: #e53e3e; /* 鲜明的红色 */
}

.rank {
    font-size: 1.1rem;
    font-weight: bold;
    position: absolute;
    line-height: 1;
    z-index: 2;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.8); /* 添加细微文字阴影增强可读性 */
}

.rank.top {
    top: 2px;
    left: 3px;
}

.rank.bottom {
    bottom: 2px;
    right: 3px;
    transform: rotate(180deg);
}

.suit {
    font-size: 1.8rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    line-height: 1;
    z-index: 2;
}
/* 媒体查询下的卡牌内部元素调整 */
@media (max-width: 1400px) {
    .rank {
        font-size: 1rem;
    }
    .suit {
        font-size: 1.6rem;
    }
}

@media (max-width: 768px) {
    .rank {
        font-size: 0.9rem;
    }
    .suit {
        font-size: 1.4rem;
    }
}

/* 发牌动画 */
@keyframes dealCard {
    from {
        transform: translateY(-150px) translateX(50px) rotate(-10deg) scale(0.5);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotate(0) scale(1);
        opacity: 1;
    }
}

.card.dealing {
    animation: dealCard 0.6s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

/* 游戏控制区域 */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 5px; /* 减少间距 */
    margin: 8px auto; /* 减少上下外边距 */
    position: relative;
    background: rgba(13, 20, 35, 0.7); /* 添加背景色 */
    border-radius: 10px; /* 减少圆角 */
    padding: 8px 12px; /* 减少内边距 */
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(56, 189, 248, 0.1) inset; /* 添加阴影和内发光效果 */
    border: 1px solid rgba(56, 189, 248, 0.15); /* 添加边框 */
}

/* 状态区域标题 */
.game-controls::before {
    content: '游戏控制';
    position: absolute;
    top: -12px;
    left: 20px;
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    padding: 2px 12px;
    border-radius: 10px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    box-shadow:
        0 4px 10px rgba(99, 102, 241, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    letter-spacing: 1px;
    z-index: 100;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 5px; /* 减少间距 */
    flex-wrap: wrap;
    justify-content: center;
    padding: 5px; /* 减少内边距 */
    background: rgba(15, 23, 42, 0.5); /* 添加轻微背景色 */
    border-radius: 8px; /* 减少圆角 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    border: 1px solid rgba(255, 255, 255, 0.05); /* 添加边框 */
}

/* 游戏设置菜单 */
.game-settings-menu {
    position: fixed; /* 改为固定定位，不受页面其他元素影响 */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1000px; /* 增加最大宽度，使布局更宽松 */
    max-height: 90vh; /* 限制最大高度，确保在小屏幕上也能完整显示 */
    background: rgba(15, 23, 42, 0.95);
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 102, 241, 0.2);
    overflow: hidden;
    display: flex; /* 使用弹性布局 */
    flex-direction: column; /* 垂直排列 */
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 确保垂直居中 */
    height: 60px; /* 固定高度 */
    padding: 0 20px; /* 水平内边距 */
    background: linear-gradient(90deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95)); /* 深蓝色背景 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0; /* 防止头部被压缩 */
    border-radius: 15px 15px 0 0; /* 添加圆角 */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* 添加阴影 */
    position: relative; /* 添加相对定位 */
}

.settings-header h3 {
    color: #ffffff;
    font-size: 1.3rem; /* 调整字体大小 */
    font-weight: 600; /* 调整字体粗细 */
    margin: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* 调整文字阴影 */
    letter-spacing: 1px; /* 调整字母间距 */
    display: inline-block;
}

.close-button {
    font-size: 1.5rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1; /* 确保垂直居中 */
    display: flex; /* 使用弹性布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    width: 30px; /* 固定宽度 */
    height: 30px; /* 固定高度 */
}

.close-button:hover {
    color: #38bdf8;
    transform: scale(1.1);
}

.settings-content {
    padding: 25px 20px 70px 20px; /* 增加顶部内边距 */
    max-height: 80vh; /* 减小最大高度，确保在小屏幕上也能完整显示 */
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr; /* 创建2列布局 */
    grid-gap: 25px 20px; /* 保持行间距 */
    flex: 1; /* 使内容区域占据剩余空间 */
    margin-top: 5px; /* 添加顶部外边距 */
}

/* 自定义滚动条样式 */
.settings-content::-webkit-scrollbar {
    width: 8px;
}

.settings-content::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
}

.settings-content::-webkit-scrollbar-thumb {
    background: rgba(56, 189, 248, 0.3);
    border-radius: 10px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
    background: rgba(56, 189, 248, 0.5);
}

/* 设置菜单底部的按钮区域需要占据整行 */
.settings-buttons {
    grid-column: 1 / span 2; /* 使按钮区域跨越两列 */
    display: flex;
    justify-content: center; /* 居中显示按钮 */
    gap: 20px; /* 增加按钮间距 */
    margin-top: 20px; /* 增加顶部边距 */
    position: sticky; /* 使按钮固定在底部 */
    bottom: 0;
    background: linear-gradient(0deg, rgba(15, 23, 42, 0.95), rgba(15, 23, 42, 0.8)); /* 渐变背景 */
    padding: 20px; /* 增加内边距 */
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* 添加顶部边框 */
    box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.25); /* 增强阴影效果 */
    z-index: 10; /* 确保按钮在最上层 */
    border-radius: 0 0 15px 15px; /* 添加圆角 */
}

.settings-section {
    margin-bottom: 15px;
    padding: 0 15px 15px 15px; /* 移除顶部内边距，为标题留出空间 */
    background: rgba(30, 41, 59, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 200px; /* 设置最小高度，使布局更均匀 */
    position: relative; /* 添加相对定位 */
    overflow: visible; /* 确保标题可以溢出容器 */
}

/* 优化"其他设置"部分的高度 */
.settings-section:nth-child(5) {
    min-height: auto; /* 其他设置部分不需要最小高度 */
}

.settings-section h4 {
    color: #ffffff;
    font-size: 1.2em;
    border-bottom: 1px solid rgba(56, 189, 248, 0.3);
    padding: 12px 15px; /* 增加内边距，确保标题有足够空间 */
    margin: 0 -15px 20px -15px; /* 使标题延伸到容器边缘 */
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
    letter-spacing: 1px;
    position: relative; /* 添加相对定位 */
    background: linear-gradient(90deg, rgba(56, 189, 248, 0.3), rgba(99, 102, 241, 0.3)); /* 渐变背景 */
    border-radius: 10px 10px 0 0; /* 添加圆角 */
    box-shadow:
        0 -3px 10px rgba(0, 0, 0, 0.1),
        0 2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影 */
    text-align: center; /* 居中显示 */
    font-weight: 600;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* 确保筹码管理区域占据足够空间 */
.settings-section:nth-child(3) {
    grid-row: span 2; /* 筹码设置区域占据两行高度 */
}

.settings-row {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%; /* 确保宽度填满容器 */
}

.settings-row label {
    margin-right: 10px;
    min-width: 120px;
    color: #e2e8f0;
}

.settings-row input[type="range"] {
    flex: 1;
    min-width: 150px;
    height: 8px;
    -webkit-appearance: none;
    background: rgba(56, 189, 248, 0.2);
    border-radius: 5px;
    outline: none;
    margin: 0 10px;
}

.settings-row input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: #38bdf8;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
}

#save-settings {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 15px 30px; /* 进一步增加内边距 */
    border-radius: 10px;
    cursor: pointer;
    font-weight: 700; /* 增加字体粗细 */
    font-size: 18px; /* 增加字体大小 */
    box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
    letter-spacing: 1.5px; /* 增加字母间距 */
    min-width: 150px; /* 设置最小宽度 */
}

#save-settings:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

#save-settings:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

#cancel-settings {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
    border: none;
    padding: 15px 30px; /* 进一步增加内边距 */
    border-radius: 10px;
    cursor: pointer;
    font-weight: 700; /* 增加字体粗细 */
    font-size: 18px; /* 增加字体大小 */
    box-shadow: 0 6px 15px rgba(100, 116, 139, 0.3);
    transition: all 0.3s ease;
    letter-spacing: 1.5px; /* 增加字母间距 */
    min-width: 150px; /* 设置最小宽度 */
}

#cancel-settings:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(100, 116, 139, 0.3);
}

#cancel-settings:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.2);
}



/* 开关按钮样式 */
.toggle-container {
    display: flex;
    align-items: center;
    gap: 12px; /* 减小间距 */
    background: rgba(8, 12, 25, 0.9); /* 更深的背景色 */
    padding: 8px 15px; /* 减小内边距 */
    border-radius: 10px;
    box-shadow:
        0 3px 10px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    transition: all 0.3s ease;
}

.toggle-container:hover {
    background: rgba(15, 25, 40, 0.9);
    transform: translateY(-2px);
    box-shadow:
        0 5px 14px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-slider {
    position: relative;
    display: inline-block;
    width: 36px; /* 进一步减小宽度 */
    height: 20px; /* 进一步减小高度 */
    background-color: rgba(100, 116, 139, 0.5);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    margin-right: 6px; /* 进一步减小边距 */
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px; /* 进一步减小高度 */
    width: 14px; /* 进一步减小宽度 */
    left: 3px;
    bottom: 3px;
    background-color: #f8fafc;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle input:checked + .toggle-slider {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    box-shadow:
        0 0 15px rgba(56, 189, 248, 0.3),
        0 0 5px rgba(56, 189, 248, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(16px);
}

.toggle-label {
    font-size: 14px;
    color: #f1f5f9;
    user-select: none;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.toggle input:checked ~ .toggle-label {
    color: #38bdf8;
    text-shadow: 0 0 8px rgba(56, 189, 248, 0.4);
}

/* 速度控制样式 */
#auto-speed-container {
    display: none;
    margin-left: 10px;
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 12px; /* 减小间距 */
    background: rgba(8, 12, 25, 0.9); /* 更深的背景色 */
    padding: 8px 15px; /* 减小内边距 */
    border-radius: 10px;
    box-shadow:
        0 3px 10px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 400px;
}

.speed-control:hover {
    background: rgba(15, 25, 40, 0.9);
    transform: translateY(-2px);
    box-shadow:
        0 5px 14px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.speed-label {
    font-size: 15px;
    color: #f1f5f9;
    white-space: nowrap;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#auto-speed {
    width: 120px;
    height: 6px;
    -webkit-appearance: none;
    background: rgba(100, 116, 139, 0.3);
    border-radius: 3px;
    outline: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    max-width: 100%;
}

#auto-speed::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

#auto-speed::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.4), 0 2px 5px rgba(0,0,0,0.3);
}

#auto-speed::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

#auto-speed::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.4), 0 2px 5px rgba(0,0,0,0.3);
}

#speed-value {
    font-weight: bold;
    color: #38bdf8;
    min-width: 30px;
    text-align: center;
    text-shadow: 0 0 8px rgba(56, 189, 248, 0.4);
}

/* 操作按钮样式 */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin: 15px 0;
}

.controls button {
    padding: 12px 25px;
    font-size: 16px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
    color: white;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    min-width: 110px;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.controls button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    transform: skewX(-20deg);
    transition: all 0.7s ease;
}

.controls button:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    transform: translateY(-5px);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(99, 102, 241, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.controls button:hover:not(:disabled):not([disabled])::before {
    left: 100%;
}

/* 禁用按钮的统一样式 */
button:disabled, button[disabled] {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
    color: rgba(255, 255, 255, 0.6) !important;
    cursor: not-allowed;
    pointer-events: none;
    border: none !important;
    opacity: 0.7;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset !important;
    transform: none !important;
}

/* 结果横幅 */
.result-banner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 35px 50px;
    border-radius: 24px;
    font-size: 20px;
    font-weight: bold;
    text-align: left;
    z-index: 9999; /* 确保横幅始终在最上层 */
    background: rgba(15, 23, 42, 0.9);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 10px 25px rgba(0, 0, 0, 0.15),
        0 0 100px rgba(99, 102, 241, 0.15),
        inset 0 0 30px rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    white-space: pre-line;
    line-height: 1.6;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: bannerAppear 0.5s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
    transition: all 0.3s ease;
}

@keyframes bannerAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -55%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.result-banner::-webkit-scrollbar {
    width: 10px;
}

.result-banner::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 5px;
}

.result-banner::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    border: 2px solid rgba(15, 23, 42, 0.9);
}

.result-banner::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
}

.result-banner .win {
    color: #4eff75;
    text-shadow:
        0 0 20px rgba(78, 255, 117, 0.6),
        0 0 40px rgba(78, 255, 117, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.9);
    padding: 14px 25px;
    margin: 8px 0;
    display: block;
    border-radius: 16px;
    background: rgba(78, 255, 117, 0.1);
    border-left: 5px solid #4eff75;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(78, 255, 117, 0.1),
        inset 0 0 0 1px rgba(78, 255, 117, 0.1);
}

.result-banner .win:hover {
    transform: translateX(5px);
    background: rgba(78, 255, 117, 0.15);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.15),
        0 0 40px rgba(78, 255, 117, 0.15),
        inset 0 0 0 1px rgba(78, 255, 117, 0.2);
}

.result-banner .lose {
    color: #ff4e4e;
    text-shadow:
        0 0 20px rgba(255, 78, 78, 0.6),
        0 0 40px rgba(255, 78, 78, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.9);
    padding: 14px 25px;
    margin: 8px 0;
    display: block;
    border-radius: 16px;
    background: rgba(255, 78, 78, 0.1);
    border-left: 5px solid #ff4e4e;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(255, 78, 78, 0.1),
        inset 0 0 0 1px rgba(255, 78, 78, 0.1);
}

.result-banner .lose:hover {
    transform: translateX(5px);
    background: rgba(255, 78, 78, 0.15);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.15),
        0 0 40px rgba(255, 78, 78, 0.15),
        inset 0 0 0 1px rgba(255, 78, 78, 0.2);
}

.result-banner .push {
    color: #ffdc4e;
    text-shadow:
        0 0 20px rgba(255, 220, 78, 0.6),
        0 0 40px rgba(255, 220, 78, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.9);
    padding: 14px 25px;
    margin: 8px 0;
    display: block;
    border-radius: 16px;
    background: rgba(255, 220, 78, 0.1);
    border-left: 5px solid #ffdc4e;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(255, 220, 78, 0.1),
        inset 0 0 0 1px rgba(255, 220, 78, 0.1);
}

.result-banner .push:hover {
    transform: translateX(5px);
    background: rgba(255, 220, 78, 0.15);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.15),
        0 0 40px rgba(255, 220, 78, 0.15),
        inset 0 0 0 1px rgba(255, 220, 78, 0.2);
}

.result-banner .special {
    color: #4e9fff;
    text-shadow:
        0 0 20px rgba(78, 159, 255, 0.6),
        0 0 40px rgba(78, 159, 255, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.9);
    padding: 14px 25px;
    margin: 8px 0;
    display: block;
    border-radius: 16px;
    background: rgba(78, 159, 255, 0.1);
    border-left: 5px solid #4e9fff;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(78, 159, 255, 0.1),
        inset 0 0 0 1px rgba(78, 159, 255, 0.1);
}

.result-banner .special:hover {
    transform: translateX(5px);
    background: rgba(78, 159, 255, 0.15);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.15),
        0 0 40px rgba(78, 159, 255, 0.15),
        inset 0 0 0 1px rgba(78, 159, 255, 0.2);
}

.result-banner.show {
    display: block;
}

/* 动画效果 */
@keyframes pulse {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes glow {
    0% { box-shadow: 0 0 10px rgba(139, 92, 246, 0.3); }
    50% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.5); }
    100% { box-shadow: 0 0 10px rgba(139, 92, 246, 0.3); }
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    10% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
    20% {
        transform: translate(-50%, -50%) scale(1);
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* 分牌提示效果 */
.can-split-indicator {
    position: absolute;
    top: -30px; /* 增加顶部距离，避免与按钮重叠 */
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(45deg, #7c3aed, #6d28d9); /* 使用紫色渐变，与分牌按钮颜色一致 */
    color: white;
    padding: 2px 8px; /* 进一步减少内边距 */
    border-radius: 10px; /* 进一步减小圆角 */
    font-size: 0.8em; /* 进一步减小字体 */
    font-weight: bold;
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.5); /* 紫色阴影 */
    animation: splitPulse 1.5s infinite;
    z-index: 1000;
    white-space: nowrap;
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    width: auto;
    text-align: center;
    pointer-events: none; /* 确保不会阻挡按钮点击 */
}

.can-split-indicator::before {
    content: "可以分牌！";
}

.can-split-indicator::after {
    content: "↓";
    position: absolute;
    bottom: -12px; /* 减少底部距离 */
    left: 50%;
    transform: translateX(-50%);
    color: #7c3aed; /* 紫色箭头 */
    font-size: 16px; /* 减小字体大小 */
    text-shadow: 0 0 5px rgba(139, 92, 246, 0.5); /* 紫色阴影 */
    animation: arrowBounce 1s infinite;
}

/* 这个选择器已移至.action-buttons button.split.can-split */

/* 禁用按钮样式 */
button:disabled, button[disabled], button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
    color: #a0aec0 !important;
    box-shadow: none !important;
}

/* 移除冗余样式 */

/* 分牌提示动画 */
@keyframes splitPulse {
    0% { transform: translateX(-50%) scale(1); opacity: 1; }
    50% { transform: translateX(-50%) scale(1.1); opacity: 0.8; }
    100% { transform: translateX(-50%) scale(1); opacity: 1; }
}

@keyframes arrowBounce {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(5px); }
}

@keyframes splitButtonGlow {
    0% { box-shadow: 0 0 5px #7c3aed; }
    50% { box-shadow: 0 0 20px #7c3aed, 0 0 30px rgba(139, 92, 246, 0.4); }
    100% { box-shadow: 0 0 5px #7c3aed; }
}

/* 移除不再使用的动画 */

@keyframes containerGlow {
    0% { box-shadow: 0 0 15px rgba(139, 92, 246, 0.3); }
    50% { box-shadow: 0 0 25px rgba(139, 92, 246, 0.5); }
    100% { box-shadow: 0 0 15px rgba(139, 92, 246, 0.3); }
}

/* 标题样式增强 */
h2, h3 {
    color: #ffffff;
    font-size: 1.2em;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 10px rgba(255, 255, 255, 0.2);
    margin-bottom: 10px;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
}

h2::after, h3::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0));
}

/* 玩家名称样式增强 */
.player-name {
    font-size: 16px;
    font-weight: bold;
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
    margin: 5px 0;
    padding: 4px 8px;
    display: inline-block;
    position: relative;
    border-radius: 6px;
    background-color: rgba(14, 20, 35, 0.8);
    border: 1px solid rgba(96, 165, 250, 0.2);
    letter-spacing: 0.5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 通用按钮和控件样式 */
button, select {
    padding: 8px 15px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    outline: none;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

button {
    background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
    color: white;
    min-width: 110px;
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    transform: skewX(-20deg);
    transition: all 0.7s ease;
}

button:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    transform: translateY(-5px);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(99, 102, 241, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

button:hover:not(:disabled):not([disabled])::before {
    left: 100%;
}

select {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: #f8fafc;
    min-width: 100px;
    box-shadow:
        0 6px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    backdrop-filter: blur(5px);
    padding: 10px 15px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

select:hover {
    border-color: rgba(99, 102, 241, 0.5);
    transform: translateY(-3px);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}



/* 历史记录按钮 */
.history-controls {
    display: flex;
    gap: 8px; /* 减少间距 */
    margin: 8px 0; /* 减少外边距 */
    justify-content: center;
    flex-wrap: wrap;
}

.history-controls button {
    background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
    min-width: 120px;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(139, 92, 246, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.history-controls button:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: translateY(-5px);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(139, 92, 246, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* 牌组选择区域 */
.deck-selection {
    display: flex;
    gap: 10px; /* 减少间距 */
    margin: 10px 0; /* 减少外边距 */
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

/* 底部控制区域 */
.bottom-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px; /* 减少间距 */
    margin-top: 5px; /* 减少顶部外边距 */
}

/* 操作按钮容器 */
.controls {
    display: flex;
    justify-content: center;
    gap: 8px; /* 减少间距 */
    flex-wrap: wrap;
    padding-top: 10px; /* 减少顶部内边距 */
    position: relative; /* 为分牌提示标识提供定位参考点 */
    margin-top: 2px; /* 减少顶部外边距 */
}

/* 动作按钮 */
.action-buttons {
    display: flex;
    gap: 6px; /* 减少间距 */
    margin-top: 5px; /* 减少顶部外边距 */
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons button {
    width: 90px; /* 减小固定宽度 */
    height: 40px; /* 减小固定高度 */
    font-weight: 600;
    font-size: 16px; /* 减小字体大小 */
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    padding: 6px 0; /* 进一步减少内边距 */
    border-radius: 8px; /* 减少圆角 */
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    border: none;
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    display: flex;
    justify-content: center;
    align-items: center;
}

.action-buttons button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    transform: skewX(-20deg);
    transition: all 0.7s ease;
}

.action-buttons button:hover:not(:disabled):not([disabled])::before {
    left: 100%;
}

.action-buttons button.hit {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(16, 185, 129, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.action-buttons button.stand {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(239, 68, 68, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.action-buttons button.double {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(245, 158, 11, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.action-buttons button.split {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(139, 92, 246, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    position: relative; /* 添加相对定位，为绝对定位的提示标识提供参考点 */
}

/* 分牌按钮霓虹特效 */
.action-buttons button.split.can-split {
    animation: splitButtonGlow 1.5s infinite;
}

@keyframes splitButtonGlow {
    0% {
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.1),
            0 4px 12px rgba(139, 92, 246, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    }
    50% {
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.15),
            0 4px 15px rgba(139, 92, 246, 0.6),
            0 0 0 2px rgba(139, 92, 246, 0.4) inset,
            0 0 20px rgba(139, 92, 246, 0.4);
    }
    100% {
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.1),
            0 4px 12px rgba(139, 92, 246, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    }
}

.action-buttons button.surrender {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); /* 蓝色渐变 */
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(59, 130, 246, 0.2), /* 蓝色阴影 */
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.action-buttons button:hover:not(:disabled):not([disabled]) {
    transform: translateY(-5px);
}

.action-buttons button.hit:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(16, 185, 129, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.action-buttons button.stand:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(239, 68, 68, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.action-buttons button.double:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(245, 158, 11, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.action-buttons button.split:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(139, 92, 246, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.action-buttons button.surrender:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); /* 深蓝色渐变 */
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(59, 130, 246, 0.25), /* 蓝色阴影 */
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* 游戏控制按钮 */
.game-controls button {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    min-width: 80px; /* 减少最小宽度 */
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow:
        0 6px 15px rgba(0, 0, 0, 0.1),
        0 3px 8px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    padding: 6px 12px; /* 减少内边距 */
    font-size: 13px; /* 减小字体大小 */
    border-radius: 6px; /* 减小圆角 */
    letter-spacing: 0.5px;
}

.game-controls button:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #4f46e5, #4338ca);
    transform: translateY(-3px);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.12),
        0 5px 15px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* 开始新游戏按钮 */
#new-game {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow:
        0 6px 15px rgba(0, 0, 0, 0.1),
        0 3px 8px rgba(16, 185, 129, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

#new-game:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.12),
        0 5px 15px rgba(16, 185, 129, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* 游戏设置按钮 */
#game-settings {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    box-shadow:
        0 6px 15px rgba(0, 0, 0, 0.1),
        0 3px 8px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

#game-settings:hover:not(:disabled):not([disabled]) {
    background: linear-gradient(135deg, #4f46e5, #4338ca);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.12),
        0 5px 15px rgba(99, 102, 241, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}



/* 策略建议样式 */
.strategy-hint {
    background: rgba(8, 12, 25, 0.9); /* 更深的背景色 */
    padding: 5px 10px; /* 减少内边距 */
    border-radius: 8px; /* 减少圆角 */
    margin: 5px auto; /* 减少外边距 */
    text-align: center;
    font-size: 14px; /* 减小字体大小 */
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    transform-origin: center bottom;
}

.strategy-hint:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

#strategy-hint-text {
    color: #f8fafc;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

#strategy-hint-text.hit {
    color: #34d399;
    text-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

#strategy-hint-text.stand {
    color: #fb7185;
    text-shadow: 0 0 15px rgba(244, 63, 94, 0.5);
}

#strategy-hint-text.double {
    color: #fbbf24;
    text-shadow: 0 0 15px rgba(245, 158, 11, 0.5);
}

#strategy-hint-text.split {
    color: #a78bfa;
    text-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

#strategy-hint-text.surrender {
    color: #22c55e;  /* 修改为绿色 */
    text-shadow: 0 0 15px rgba(34, 197, 94, 0.5);  /* 修改为对应的绿色阴影 */
}

#strategy-hint-text.bust {
    color: #f87171;
    text-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
}

/* 策略建议容器样式 */
.strategy-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px; /* 减少间距 */
    margin: 5px 0; /* 减少上下外边距 */
    flex-wrap: wrap;
}

.strategy-container .toggle-container {
    margin: 0;
}

.strategy-container .strategy-hint {
    margin: 0;
    flex: 1;
    max-width: 500px;
}

/* 玩家统计信息样式 */
.player-stats {
    margin: 5px 0 8px 0;
    padding: 8px;
    background-color: rgba(14, 20, 35, 0.95);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    gap: 6px;
    position: relative;
    width: 100%; /* 确保宽度充满父容器 */
}

/* 行容器样式 */
.player-stats-row {
    display: flex;
    justify-content: space-between;
    gap: 6px; /* 减少间距以节省空间 */
    height: auto; /* 允许自动调整高度 */
    min-height: 30px; /* 保持最小高度 */
    flex-wrap: nowrap; /* 防止换行 */
}

/* 统计项目样式 */
.stat-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(24, 36, 52, 0.9);
    border-radius: 5px;
    padding: 6px 8px; /* 减少内边距，给数字留更多空间 */
    font-size: 0.88rem; /* 略微减小字体 */
    font-weight: 600;
    color: white;
    position: relative;
    overflow: visible; /* 允许内容溢出 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: center;
    letter-spacing: -0.2px; /* 轻微减少字母间距 */
    line-height: 1.2;
    min-width: 0; /* 允许内容收缩 */
}

/* 标签样式 */
.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: normal;
    margin-right: 6px; /* 增加右侧间距 */
    font-size: 0.85em; /* 减小字体大小 */
    text-align: left;
    min-width: 38px;
}

/* 为每种统计项添加左侧彩色边框和更明显的样式 */
.stat-item.win {
    border-left: 3px solid #fb7185;
    color: #fb7185;
    background: linear-gradient(90deg, rgba(251, 113, 133, 0.1) 0%, rgba(24, 36, 52, 0.9) 100%);
}

.stat-item.loss {
    border-left: 3px solid #34d399;
    color: #34d399;
    background: linear-gradient(90deg, rgba(52, 211, 153, 0.1) 0%, rgba(24, 36, 52, 0.9) 100%);
}

.stat-item.rate {
    border-left: 3px solid #fbbf24;
    color: #fbbf24;
    background: linear-gradient(90deg, rgba(251, 191, 36, 0.1) 0%, rgba(24, 36, 52, 0.9) 100%);
}

.stat-item.score {
    border-left: 3px solid #38bdf8;
    color: #38bdf8;
    background: linear-gradient(90deg, rgba(56, 189, 248, 0.1) 0%, rgba(24, 36, 52, 0.9) 100%);
}

/* 筹码样式 */
.stat-item.chips {
    border-left: 3px solid #a855f7;
    color: #a855f7;
    flex-basis: 100%;
    font-size: 0.95rem;
    justify-content: flex-start;
    height: auto; /* 允许高度自适应 */
    min-height: 30px; /* 保持最小高度 */
    background: linear-gradient(90deg, rgba(168, 85, 247, 0.15) 0%, rgba(24, 36, 52, 0.9) 100%);
    font-weight: 700;
    white-space: nowrap;
    overflow: visible;
    min-width: 100px; /* 确保足够空间显示大数值和负值 */
    padding-right: 15px; /* 增加右侧内边距 */
    margin-top: 3px; /* 与上方元素增加间距 */
}

.stat-item.chips .stat-label {
    min-width: 50px; /* 更宽的标签 */
    color: rgba(255, 255, 255, 0.9); /* 更亮的标签颜色 */
}

/* 移除旧样式 */
.player-stats span::after,
.player-stats span::before {
    display: none;
}

/* 保留悬停效果 */
.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

@keyframes subtlePulse {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 标题伪元素的悬停效果 */
#players-container:hover::before,
.dealer-section:hover::before {
    transform: translateY(-2px);
    box-shadow:
        0 6px 15px rgba(139, 92, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.player-hand {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.player-hand .cards {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap; /* 修改为nowrap避免换行 */
    gap: 0px; /* 移除间距，因为卡牌会通过负margin叠加 */
    width: 100%;
    margin: 0;
    position: relative;
    padding-left: 15px; /* 添加左内边距，为第一张卡牌的负margin提供空间 */
}

.hand-container .points-display {
    margin: 0;
    background-color: rgba(8, 12, 25, 0.95);
    color: #38bdf8;
    padding: 5px 12px;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15), 0 0 15px rgba(56, 189, 248, 0.1);
    border: 1px solid rgba(56, 189, 248, 0.2);
    text-shadow: 0 0 8px rgba(56, 189, 248, 0.6);
    order: 2;
}

/* 点数显示样式优化 */
.points-display {
    display: inline-block;
    padding: 7px 14px; /* 增加内边距 */
    font-size: 1.2em; /* 增大字体 */
    background-color: rgba(8, 12, 25, 0.95);
    border-radius: 10px; /* 增大圆角 */
    color: #38bdf8;
    font-weight: bold;
    margin: 10px auto 5px auto; /* 调整外边距，使其居中 */
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.7);
    border: 1px solid rgba(56, 189, 248, 0.3); /* 增强边框 */
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(56, 189, 248, 0.15);
    min-width: 40px; /* 确保有最小宽度 */
    text-align: center; /* 确保文字居中 */
}

/* 特殊样式：点数分数表示法 (如 6/16) */
.points-display.fraction {
    min-width: 60px; /* 确保分数有足够空间 */
    letter-spacing: 1px; /* 增加字母间距 */
}

/* 点数分数形式的特定样式 */
.points-display.fraction .small-value {
    font-size: 0.9em;
    color: #4ade80;
}

.points-display.fraction .big-value {
    font-size: 1.05em;
    color: #38bdf8;
    font-weight: 700;
}

/* 特殊宽度点数样式 */
.points-display.wide-points {
    min-width: 60px; /* 双位数或特殊值点数 */
    padding-left: 15px;
    padding-right: 15px;
}

.points-display.single-points {
    min-width: 35px; /* 单位数点数 */
}

/* 当前操作指示器增强 */
.current-player-indicator {
    animation-duration: 2s; /* 稍微放慢动画 */
}

/* 背景流光效果，放在单独的伪元素中 */
.hand-container.active .cards::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 79, 216, 0.15) 0%,
        rgba(255, 79, 216, 0) 50%,
        rgba(255, 79, 216, 0.15) 100%);
    z-index: 0;
    animation: activeHandGlow 3s infinite;
    pointer-events: none;
}

/* 移除之前添加的speed-arrow样式 */
.speed-arrow {
    background: transparent;
    color: #38bdf8;
    border: none;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
    padding: 0;
    margin: 0 1px;
    text-shadow: 0 0 4px rgba(56, 189, 248, 0.4);
}

.speed-arrow:hover {
    transform: scale(1.15);
    color: #60cdff;
    text-shadow: 0 0 6px rgba(56, 189, 248, 0.7);
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .player-section {
        min-width: 240px; /* 从200px增加到240px */
        max-width: 280px; /* 从240px增加到280px */
    }
    .card {
        width: 90px;
        height: 126px;
        margin-top: -110px; /* 恢复堆叠效果，与主样式保持一致的比例 */
    }
    .cards {
        min-height: 145px; /* 减小容器高度 */
        flex-direction: column; /* 确保纵向排列 */
    }

    /* 通用卡牌间距 */
    .cards .card:nth-child(2) { margin-left: 12px; }
    .cards .card:nth-child(3) { margin-left: 24px; }
    .cards .card:nth-child(4) { margin-left: 36px; }
    .cards .card:nth-child(5) { margin-left: 48px; }
    .cards .card:nth-child(6) { margin-left: 60px; }

    /* 玩家手牌卡牌间距 */
    .player-section .hand-container .cards .card:nth-child(2) { margin-left: 12px; }
    .player-section .hand-container .cards .card:nth-child(3) { margin-left: 24px; }
    .player-section .hand-container .cards .card:nth-child(4) { margin-left: 36px; }
    .player-section .hand-container .cards .card:nth-child(5) { margin-left: 48px; }
    .player-section .hand-container .cards .card:nth-child(6) { margin-left: 60px; }

    /* 庄家手牌卡牌间距 */
    .dealer-hand .cards .card:nth-child(2) { margin-left: 12px; }
    .dealer-hand .cards .card:nth-child(3) { margin-left: 24px; }
    .dealer-hand .cards .card:nth-child(4) { margin-left: 36px; }
    .dealer-hand .cards .card:nth-child(5) { margin-left: 48px; }
    .dealer-hand .cards .card:nth-child(6) { margin-left: 60px; }
}

@media (max-width: 768px) {
    .player-section {
        min-width: 180px; /* 从140px增加到180px */
        max-width: 220px; /* 从180px增加到220px */
    }
    .card {
        width: 70px;
        height: 98px;
        margin-top: -85px; /* 恢复堆叠效果，与主样式保持一致的比例 */
    }
    .cards {
        min-height: 130px; /* 减小容器高度 */
        flex-direction: column; /* 确保纵向排列 */
    }

    /* 通用卡牌间距 */
    .cards .card:nth-child(2) { margin-left: 8px; }
    .cards .card:nth-child(3) { margin-left: 16px; }
    .cards .card:nth-child(4) { margin-left: 24px; }
    .cards .card:nth-child(5) { margin-left: 32px; }
    .cards .card:nth-child(6) { margin-left: 40px; }

    /* 玩家手牌卡牌间距 */
    .player-section .hand-container .cards .card:nth-child(2) { margin-left: 8px; }
    .player-section .hand-container .cards .card:nth-child(3) { margin-left: 16px; }
    .player-section .hand-container .cards .card:nth-child(4) { margin-left: 24px; }
    .player-section .hand-container .cards .card:nth-child(5) { margin-left: 32px; }
    .player-section .hand-container .cards .card:nth-child(6) { margin-left: 40px; }

    /* 庄家手牌卡牌间距 */
    .dealer-hand .cards .card:nth-child(2) { margin-left: 8px; }
    .dealer-hand .cards .card:nth-child(3) { margin-left: 16px; }
    .dealer-hand .cards .card:nth-child(4) { margin-left: 24px; }
    .dealer-hand .cards .card:nth-child(5) { margin-left: 32px; }
    .dealer-hand .cards .card:nth-child(6) { margin-left: 40px; }

    .controls button {
        padding: 8px 16px;
        font-size: 14px;
        min-width: 80px;
    }
}

/* 左上角花色样式 */
.top-suit {
    font-size: 1.05rem; /* 从1.2rem调整为0.9rem和1.2rem之间的中间值 */
    position: absolute;
    top: 2px;
    left: 16px;
    line-height: 1;
    z-index: 2;
}

/* 右下角花色样式 */
.bottom-suit {
    font-size: 1.05rem; /* 从1.2rem调整为0.9rem和1.2rem之间的中间值 */
    position: absolute;
    bottom: 2px;
    right: 16px;
    line-height: 1;
    z-index: 2;
    transform: rotate(180deg);
}

/* 添加媒体查询适配不同屏幕尺寸 */
@media (max-width: 1400px) {
    .top-suit, .bottom-suit {
        font-size: 0.95rem; /* 从1.1rem调整为0.8rem和1.1rem之间的中间值 */
    }
}

@media (max-width: 768px) {
    .top-suit, .bottom-suit {
        font-size: 0.8rem; /* 从0.9rem调整为0.7rem和0.9rem之间的中间值 */
    }
}

/* 10号牌专用样式 */
.ten-card .rank.top {
    font-size: 0.9rem; /* 减小字体 */
    top: 3px;
    left: 3px;
}

.ten-card .rank.bottom {
    font-size: 0.9rem; /* 减小字体 */
    bottom: 3px;
    right: 3px;
}

/* 10号牌的花色位置调整 */
.top-suit.ten-suit {
    left: 24px; /* 增加向右偏移，为10号让出更多空间 */
    top: 3px;
    font-size: 0.98rem; /* 从1.1rem调整为0.85rem和1.1rem之间的中间值 */
}

.bottom-suit.ten-suit {
    right: 24px; /* 增加向左偏移，为10号让出更多空间 */
    bottom: 3px;
    font-size: 0.98rem; /* 从1.1rem调整为0.85rem和1.1rem之间的中间值 */
}

/* 响应式调整 */
@media (max-width: 1400px) {
    .ten-card .rank.top,
    .ten-card .rank.bottom {
        font-size: 0.85rem;
    }

    .top-suit.ten-suit {
        left: 22px;
        font-size: 0.9rem; /* 从1rem调整为0.8rem和1rem之间的中间值 */
    }

    .bottom-suit.ten-suit {
        right: 22px;
        font-size: 1rem; /* 从0.8rem增加到1rem */
    }
}

@media (max-width: 768px) {
    .ten-card .rank.top,
    .ten-card .rank.bottom {
        font-size: 0.8rem;
    }

    .top-suit.ten-suit {
        left: 20px;
        font-size: 0.8rem; /* 从0.85rem调整为0.75rem和0.85rem之间的中间值 */
    }

    .bottom-suit.ten-suit {
        right: 20px;
        font-size: 0.8rem; /* 从0.85rem调整为0.75rem和0.85rem之间的中间值 */
    }
}

/* 确保庄家区域的卡牌也使用通用的卡牌偏移样式，向右偏移 */
.dealer-hand .cards .card:nth-child(2) {
    margin-left: 15px; /* 减少偏移量与前面设置保持一致 */
    margin-right: -5px; /* 增加负右边距让卡片更加紧密 */
    align-self: flex-start;
    z-index: 2;
}

.dealer-hand .cards .card:nth-child(3) {
    margin-left: 30px; /* 减少偏移量与前面设置保持一致 */
    margin-right: -5px; /* 增加负右边距 */
    align-self: flex-start;
    z-index: 3;
}
.dealer-hand .cards .card:nth-child(4) {
    margin-left: 45px; /* 减少偏移量与前面设置保持一致 */
    margin-right: -5px; /* 增加负右边距 */
    align-self: flex-start;
    z-index: 4;
}

.dealer-hand .cards .card:nth-child(5) {
    margin-left: 60px; /* 减少偏移量与前面设置保持一致 */
    margin-right: -5px; /* 增加负右边距 */
    align-self: flex-start;
    z-index: 5;
}

.dealer-hand .cards .card:nth-child(6) {
    margin-left: 75px; /* 减少偏移量与前面设置保持一致 */
    margin-right: -5px; /* 增加负右边距 */
    align-self: flex-start;
    z-index: 6;
}

/* 响应式调整 - 中等屏幕 */
@media (max-width: 1400px) {
    .dealer-hand .cards .card:nth-child(2) {
        margin-left: 12px;
    }

    .dealer-hand .cards .card:nth-child(3) {
        margin-left: 24px;
    }

    .dealer-hand .cards .card:nth-child(4) {
        margin-left: 36px;
    }

    .dealer-hand .cards .card:nth-child(5) {
        margin-left: 48px;
    }

    .dealer-hand .cards .card:nth-child(6) {
        margin-left: 60px;
    }
}

/* 响应式调整 - 小屏幕 */
@media (max-width: 768px) {
    .dealer-hand .cards .card:nth-child(2) {
        margin-left: 8px;
    }

    .dealer-hand .cards .card:nth-child(3) {
        margin-left: 16px;
    }

    .dealer-hand .cards .card:nth-child(4) {
        margin-left: 24px;
    }

    .dealer-hand .cards .card:nth-child(5) {
        margin-left: 32px;
    }

    .dealer-hand .cards .card:nth-child(6) {
        margin-left: 40px;
    }
}

.small-button {
    padding: 5px 10px;
    background-color: #38bdf8;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 5px;
}

.small-button:hover {
    background-color: #60a5fa;
    transform: translateY(-1px);
}

.small-button:active {
    transform: translateY(1px);
}

#auto-bet-settings {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding-left: 25px;
}

#auto-bet-settings input {
    width: 80px;
    margin-left: 5px;
}

/* 确保胜率可显示较大数值 */
.stat-item.rate {
    min-width: 85px; /* 确保足够空间显示百分比 */
    flex: 1; /* 分配足够空间 */
}

/* 移动端优化统计项目显示 */
@media (max-width: 768px) {
    .stat-item {
        padding: 5px 8px;
        font-size: 0.8rem;
    }

    .stat-label {
        min-width: 30px;
        font-size: 0.8em;
    }
}

/* 调整总收益样式确保大数值完整显示 */
.player-stats span.total-value {
    font-weight: 700;
    overflow: visible;
    display: inline-block;
    margin-left: 5px;
    min-width: 40px; /* 确保有足够空间显示较大数值 */
}

/* 为赢/输统计项增加更多显示空间 */
.stat-item.win, .stat-item.loss {
    min-width: 65px; /* 确保数字显示完整 */
}

/* 增大筹码数字显示区域 */
.player-stats > div:first-child {
    margin-bottom: 5px; /* 给筹码值添加更多空间 */
}

.stat-item {
    padding: 6px 8px; /* 减少内边距，给数字留更多空间 */
}

/* 筹码显示优化 */
.stat-item:first-child {
    font-size: 0.9rem; /* 稍微减小字体 */
    white-space: nowrap;
    overflow: visible;
    flex: 1.2; /* 给筹码值分配更多空间 */
}

/* 胜率显示优化 */
.stat-item.rate {
    min-width: 85px; /* 确保足够空间显示百分比 */
    flex: 1; /* 分配足够空间 */
}

/* 总收益显示优化 */
.stat-item.chips {
    font-size: 0.95rem; /* 调整字体大小 */
    overflow: visible;
    padding-right: 15px; /* 增加右侧内边距 */
    min-width: 100px; /* 确保足够空间显示大数值和负值 */
}

/* 修改玩家统计行样式以容纳更多数据 */
.player-stats-row {
    gap: 6px; /* 减少间距以节省空间 */
    height: auto; /* 允许自动调整高度 */
    min-height: 30px; /* 保持最小高度 */
    flex-wrap: nowrap; /* 防止换行 */
}

/* 优化胜负统计项 */
.stat-item.win, .stat-item.loss {
    min-width: 75px; /* 增加最小宽度 */
    flex: 1; /* 平均分配空间 */
}

/* 单独优化筹码值显示 */
.player-stats > div:first-child .stat-item {
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: visible;
    flex: 1.2; /* 给筹码值分配更多空间 */
    min-width: 130px; /* 确保足够显示6位筹码 */
}

/* 优化总收益显示 */
.stat-item.chips {
    border-left: 3px solid #a855f7;
    color: #a855f7;
    flex-basis: 100%;
    font-size: 0.95rem;
    justify-content: flex-start;
    height: auto; /* 允许高度自适应 */
    min-height: 30px; /* 保持最小高度 */
    background: linear-gradient(90deg, rgba(168, 85, 247, 0.15) 0%, rgba(24, 36, 52, 0.9) 100%);
    font-weight: 700;
    white-space: nowrap;
    overflow: visible;
    min-width: 100px; /* 确保足够空间显示大数值和负值 */
    padding-right: 15px; /* 增加右侧内边距 */
    margin-top: 3px; /* 与上方元素增加间距 */
}

/* 确保大数值能够完整显示 */
.player-stats .stat-value,
.player-stats span.total-value {
    overflow: visible;
    flex-shrink: 0;
    min-width: 30px; /* 确保有显示空间 */
    margin-left: auto; /* 推到右侧 */
    text-align: right; /* 右对齐 */
}

/* 下注控制区域样式 */
#betting-controls {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(13, 20, 35, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(56, 189, 248, 0.2);
    z-index: 1000;
    transition: all 0.3s ease-in-out;
}

#betting-controls.hidden {
    transform: translateY(100%);
    opacity: 0;
    pointer-events: none;
}

#betting-controls button {
    padding: 12px 18px;
    font-size: 16px;
    border-radius: 10px;
    border: none;
    outline: none;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#clear-bets-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#clear-bets-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(239, 68, 68, 0.3);
}

#confirm-bet-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#confirm-bet-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3);
}

#confirm-all-bets-btn {
    background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 17px;
    padding: 13px 20px;
}

#confirm-all-bets-btn:hover {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(56, 189, 248, 0.3);
}

.settings-section input[type="number"] {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(56, 189, 248, 0.3);
    border-radius: 8px;
    color: #ffffff;
    padding: 8px 12px;
    font-size: 14px;
    width: 120px;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(56, 189, 248, 0.1);
    outline: none;
}

.settings-section input[type="number"]:focus {
    border-color: #38bdf8;
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.25);
}

.settings-section input[type="number"]:hover {
    border-color: rgba(56, 189, 248, 0.6);
}

.settings-section button {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin-top: 5px;
    display: inline-block;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.settings-section button:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.15);
}

.settings-section button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-section button:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 100%);
    transform: rotate(45deg);
    transition: all 0.5s ease;
        opacity: 0;
}

.settings-section button:hover:before {
    animation: btn-shine 1.5s ease forwards;
}

/* 新增的筹码管理按钮 */
.chip-management-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    margin-top: 15px;
}

.chip-management-buttons button {
    width: 100%;
}

.chip-add-single {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.chip-add-single:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
}

.chip-add-all {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%) !important;
}

.chip-add-all:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%) !important;
}

/* 玩家选择下拉菜单样式 */
.player-selector {
    width: 100%;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: nowrap; /* 修改为不换行 */
    align-items: center;
    gap: 8px;
}

.player-selector select {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(56, 189, 248, 0.3);
    border-radius: 8px;
    color: #ffffff;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(56, 189, 248, 0.1);
    outline: none;
    flex: 1; /* 让下拉框占据剩余空间 */
    min-width: 150px; /* 设置最小宽度 */
}

.player-selector select:focus {
    border-color: #38bdf8;
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.25);
}

.player-selector input[type="number"] {
    width: 100px;
}

.player-selector label {
    white-space: nowrap; /* 防止"金额："标签换行 */
}

/* 自动模式部分的样式优化 */
.info-tip {
    width: 100%;
}

/* 布局调整：2x2网格布局 */
/* 牌库设置 */
.settings-section:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
}

/* 游戏规则 */
.settings-section:nth-child(2) {
    grid-column: 2;
    grid-row: 1;
}

/* 筹码设置 */
.settings-section:nth-child(3) {
    grid-column: 1;
    grid-row: 2;
}

/* 自动模式 */
.settings-section:nth-child(4) {
    grid-column: 2;
    grid-row: 2;
}

/* 其他设置 */
.settings-section:nth-child(5) {
    grid-column: 1 / span 2;
    grid-row: 3;
}

/* Toast通知样式 */
.toast-notification {
    background-color: rgba(56, 189, 248, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
    transition: opacity 0.3s ease;
    font-weight: 500;
    letter-spacing: 0.5px;
    max-width: 80%;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 分牌提示标识样式 */
.split-indicator {
    position: absolute;
    top: -30px; /* 进一步调整到更高的位置，避免遮挡 */
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    color: white;
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    z-index: 100;
    animation: pulse 1.5s infinite;
    border: 1px solid rgba(255, 255, 255, 0.2);
    white-space: nowrap; /* 防止文字换行 */
}

@keyframes pulse {
    0% {
        transform: translateX(-50%) scale(1);
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
    }
    50% {
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 2px 12px rgba(139, 92, 246, 0.6);
    }
    100% {
        transform: translateX(-50%) scale(1);
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
    }
}

/* 历史记录结算横幅显示/隐藏按钮 */
.history-result-toggle {
    /* 基本样式 */
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    outline: none;
    white-space: nowrap; /* 防止文本换行 */

    /* 固定样式 */
    position: fixed !important; /* 确保按钮始终保持固定位置 */
    top: 35% !important; /* 将按钮放在屏幕上方部分 */
    left: 50% !important;
    transform: translateX(-50%) !important;
    margin: 0 !important;
    z-index: 10001 !important; /* 确保按钮在最上层 */
    padding: 12px 30px !important; /* 增大按钮尺寸，确保文字完全显示 */
    font-size: 16px !important; /* 确保文字大小固定 */
    width: auto !important; /* 让按钮宽度适应内容 */
    min-width: 180px !important; /* 确保最小宽度 */
}

.history-result-toggle:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateX(-50%) translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.history-result-toggle:active {
    transform: translateX(-50%) translateY(0) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 单独的按钮容器样式 */
.history-toggle-container {
    position: fixed !important;
    top: 35% !important; /* 与上面的按钮位置一致 */
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 10002 !important; /* 比横幅中的按钮高一层 */
    padding: 0 !important;
    margin: 0 !important;
    width: auto !important;
}

/* 隐藏但保持空间占用 */
.result-banner.history-hidden {
    opacity: 0 !important;
    max-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    border: none !important;
    transition: all 0.3s ease !important;
}

